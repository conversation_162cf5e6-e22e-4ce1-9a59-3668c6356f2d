#!/usr/bin/env node

/**
 * AUTONOBOT Feature Test Script
 * Tests all the enhanced features to ensure they're working correctly
 */

const { loggers } = require('./backend/services/loggerService');
const configService = require('./backend/services/configService');
const searchService = require('./backend/services/searchService');
const { llmService } = require('./backend/services/llmService');

async function testConfiguration() {
  console.log('\n🔧 Testing Configuration Service...');
  
  try {
    const status = configService.getStatus();
    console.log('✅ Configuration loaded successfully');
    console.log('📊 Available services:', status);
    
    // Test API key validation
    const hasGemini = configService.hasApiKey('gemini');
    const hasAnthropic = configService.hasApiKey('anthropic');
    const hasOpenAI = configService.hasApiKey('openai');
    const hasGoogleSearch = configService.hasApiKey('googleSearch');
    const hasBingSearch = configService.hasApiKey('bingSearch');
    
    console.log('🔑 API Key Status:');
    console.log(`   Gemini: ${hasGemini ? '✅' : '❌'}`);
    console.log(`   Anthropic: ${hasAnthropic ? '✅' : '❌'}`);
    console.log(`   OpenAI: ${hasOpenAI ? '✅' : '❌'}`);
    console.log(`   Google Search: ${hasGoogleSearch ? '✅' : '❌'}`);
    console.log(`   Bing Search: ${hasBingSearch ? '✅' : '❌'}`);
    
    return true;
  } catch (error) {
    console.error('❌ Configuration test failed:', error.message);
    return false;
  }
}

async function testLogging() {
  console.log('\n📝 Testing Logging Service...');
  
  try {
    loggers.main.info('Test log message from feature test');
    loggers.search.debug('Search service test log');
    loggers.llm.warn('LLM service test warning');
    
    console.log('✅ Logging service working correctly');
    return true;
  } catch (error) {
    console.error('❌ Logging test failed:', error.message);
    return false;
  }
}

async function testSearchService() {
  console.log('\n🔍 Testing Search Service...');
  
  try {
    const availableProviders = configService.getAvailableSearchProviders();
    console.log('📡 Available search providers:', availableProviders);
    
    if (availableProviders.length === 0) {
      console.log('⚠️  No search providers configured - skipping search test');
      return true;
    }
    
    // Test search functionality
    console.log('🔍 Testing search with query: "artificial intelligence"');
    const searchResults = await searchService.search('artificial intelligence', { 
      maxResults: 3,
      useCache: false 
    });
    
    console.log('✅ Search completed successfully');
    console.log(`📊 Found ${searchResults.results.length} results`);
    console.log(`🏷️  Provider used: ${searchResults.provider}`);
    console.log(`⏱️  Search time: ${searchResults.searchTime}ms`);
    
    if (searchResults.results.length > 0) {
      console.log('📄 First result:', {
        title: searchResults.results[0].title.substring(0, 50) + '...',
        url: searchResults.results[0].url
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ Search test failed:', error.message);
    return false;
  }
}

async function testLLMService() {
  console.log('\n🤖 Testing LLM Service...');
  
  try {
    const availableProviders = configService.getAvailableLLMProviders();
    console.log('🧠 Available LLM providers:', availableProviders);
    
    if (availableProviders.length === 0) {
      console.log('⚠️  No LLM providers configured - skipping LLM test');
      return true;
    }
    
    // Test LLM functionality
    console.log('🤖 Testing LLM with simple query...');
    const response = await llmService.callLLM('Say "Hello from AUTONOBOT test!" in a friendly way.');
    
    console.log('✅ LLM response received');
    console.log('📝 Response:', response.substring(0, 100) + (response.length > 100 ? '...' : ''));
    
    // Test LLM stats
    const stats = llmService.getStats();
    console.log('📊 LLM Stats:', stats);
    
    return true;
  } catch (error) {
    console.error('❌ LLM test failed:', error.message);
    return false;
  }
}

async function testIntegration() {
  console.log('\n🔗 Testing Service Integration...');
  
  try {
    const hasSearch = configService.getAvailableSearchProviders().length > 0;
    const hasLLM = configService.getAvailableLLMProviders().length > 0;
    
    if (!hasSearch || !hasLLM) {
      console.log('⚠️  Search or LLM not available - skipping integration test');
      return true;
    }
    
    // Test search + LLM integration
    console.log('🔗 Testing search + LLM integration...');
    const searchResults = await searchService.search('latest AI news', { maxResults: 2 });
    
    if (searchResults.results.length > 0) {
      const prompt = `Summarize this search result in one sentence: ${searchResults.results[0].title} - ${searchResults.results[0].snippet}`;
      const summary = await llmService.callLLM(prompt);
      
      console.log('✅ Integration test successful');
      console.log('📝 AI Summary:', summary.substring(0, 150) + '...');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting AUTONOBOT Feature Tests...\n');
  
  const tests = [
    { name: 'Configuration', fn: testConfiguration },
    { name: 'Logging', fn: testLogging },
    { name: 'Search Service', fn: testSearchService },
    { name: 'LLM Service', fn: testLLMService },
    { name: 'Integration', fn: testIntegration }
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, success: result });
    } catch (error) {
      console.error(`❌ Test ${test.name} crashed:`, error.message);
      results.push({ name: test.name, success: false });
    }
  }
  
  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  let passed = 0;
  let failed = 0;
  
  results.forEach(result => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}`);
    if (result.success) passed++;
    else failed++;
  });
  
  console.log(`\n🎯 Total: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed! AUTONOBOT is ready to use.');
  } else {
    console.log('⚠️  Some tests failed. Check your configuration and API keys.');
  }
  
  return failed === 0;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test runner crashed:', error);
      process.exit(1);
    });
}

module.exports = { runAllTests };
