import { AgentOrchestrator } from '../agents/AgentOrchestrator.js';
import { loggers } from '../services/loggerService.js';

/**
 * Agent Controller - Controlador principal para el sistema de agentes autónomos
 * Maneja las peticiones HTTP y coordina con el Agent Orchestrator
 */
class AgentController {
  constructor() {
    this.orchestrator = new AgentOrchestrator();
    this.activeConnections = new Map(); // Para WebSocket connections
    
    // Configurar event listeners del orchestrator
    this.setupOrchestratorEvents();
    
    loggers.main.info('Agent Controller initialized');
  }

  /**
   * Configura los event listeners del orchestrator
   */
  setupOrchestratorEvents() {
    this.orchestrator.on('orchestrator:task_started', (data) => {
      this.broadcastToConnections('task:started', data);
    });

    this.orchestrator.on('orchestrator:step_completed', (data) => {
      this.broadcastToConnections('task:step_completed', data);
    });

    this.orchestrator.on('orchestrator:task_completed', (data) => {
      this.broadcastToConnections('task:completed', data);
    });

    this.orchestrator.on('orchestrator:observation_complete', (data) => {
      this.broadcastToConnections('observation:complete', data);
    });

    this.orchestrator.on('orchestrator:search_complete', (data) => {
      this.broadcastToConnections('search:complete', data);
    });

    this.orchestrator.on('orchestrator:navigation_complete', (data) => {
      this.broadcastToConnections('navigation:complete', data);
    });

    this.orchestrator.on('orchestrator:response_generated', (data) => {
      this.broadcastToConnections('response:generated', data);
    });
  }

  /**
   * Procesa una instrucción del usuario (endpoint principal)
   */
  async processInstruction(req, res) {
    try {
      const { instruction, options = {} } = req.body;

      if (!instruction || typeof instruction !== 'string') {
        return res.status(400).json({
          success: false,
          error: 'Instruction is required and must be a string',
          timestamp: new Date().toISOString()
        });
      }

      loggers.main.info('Processing user instruction via API', { 
        instruction, 
        options,
        userAgent: req.get('User-Agent'),
        ip: req.ip 
      });

      // Validar opciones
      const validatedOptions = this.validateOptions(options);

      // Procesar instrucción con el orchestrator
      const result = await this.orchestrator.processUserInstruction(instruction, validatedOptions);

      // Respuesta exitosa
      res.json({
        success: true,
        taskId: result.taskId || 'unknown',
        result,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      loggers.main.error('Error processing instruction', { 
        error: error.message,
        stack: error.stack 
      });

      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Obtiene el estado actual del sistema de agentes
   */
  async getSystemStatus(req, res) {
    try {
      const status = this.orchestrator.getStatus();
      
      res.json({
        success: true,
        status,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      loggers.main.error('Error getting system status', { error: error.message });

      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Detiene la ejecución actual
   */
  async stopExecution(req, res) {
    try {
      await this.orchestrator.stop();

      res.json({
        success: true,
        message: 'Execution stopped successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      loggers.main.error('Error stopping execution', { error: error.message });

      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Obtiene el historial de tareas
   */
  async getTaskHistory(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 50;
      const history = this.orchestrator.taskManager.getTaskHistory(limit);

      res.json({
        success: true,
        history,
        count: history.length,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      loggers.main.error('Error getting task history', { error: error.message });

      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Obtiene estadísticas del sistema
   */
  async getStatistics(req, res) {
    try {
      const taskStats = this.orchestrator.taskManager.getStatistics();
      const stateStats = this.orchestrator.stateManager.getStatistics();
      
      // Obtener estadísticas de cada agente
      const agentStats = {};
      Object.entries(this.orchestrator.agents).forEach(([name, agent]) => {
        if (typeof agent.getStatistics === 'function') {
          agentStats[name] = agent.getStatistics();
        }
      });

      const statistics = {
        tasks: taskStats,
        state: stateStats,
        agents: agentStats,
        system: {
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          nodeVersion: process.version
        }
      };

      res.json({
        success: true,
        statistics,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      loggers.main.error('Error getting statistics', { error: error.message });

      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Endpoint para testing del sistema
   */
  async testSystem(req, res) {
    try {
      const testInstruction = req.body.instruction || "Test the autonomous browser system";
      
      loggers.main.info('Running system test', { testInstruction });

      // Ejecutar test básico
      const testResult = await this.runBasicTest(testInstruction);

      res.json({
        success: true,
        testResult,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      loggers.main.error('Error running system test', { error: error.message });

      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Maneja conexiones WebSocket para actualizaciones en tiempo real
   */
  handleWebSocketConnection(ws, req) {
    const connectionId = this.generateConnectionId();
    
    loggers.main.info('New WebSocket connection', { connectionId });

    // Agregar conexión al mapa
    this.activeConnections.set(connectionId, ws);

    // Enviar estado inicial
    ws.send(JSON.stringify({
      type: 'connection:established',
      connectionId,
      status: this.orchestrator.getStatus(),
      timestamp: new Date().toISOString()
    }));

    // Manejar mensajes del cliente
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        this.handleWebSocketMessage(connectionId, data);
      } catch (error) {
        loggers.main.error('Error parsing WebSocket message', { 
          connectionId, 
          error: error.message 
        });
      }
    });

    // Limpiar al cerrar conexión
    ws.on('close', () => {
      this.activeConnections.delete(connectionId);
      loggers.main.info('WebSocket connection closed', { connectionId });
    });

    ws.on('error', (error) => {
      loggers.main.error('WebSocket error', { connectionId, error: error.message });
      this.activeConnections.delete(connectionId);
    });
  }

  /**
   * Maneja mensajes WebSocket del cliente
   */
  handleWebSocketMessage(connectionId, data) {
    const ws = this.activeConnections.get(connectionId);
    if (!ws) return;

    switch (data.type) {
      case 'ping':
        ws.send(JSON.stringify({
          type: 'pong',
          timestamp: new Date().toISOString()
        }));
        break;

      case 'get_status':
        ws.send(JSON.stringify({
          type: 'status_update',
          status: this.orchestrator.getStatus(),
          timestamp: new Date().toISOString()
        }));
        break;

      case 'subscribe_events':
        // Cliente se suscribe a eventos específicos
        // Implementar lógica de suscripción si es necesario
        break;

      default:
        loggers.main.warn('Unknown WebSocket message type', { 
          connectionId, 
          type: data.type 
        });
    }
  }

  /**
   * Transmite eventos a todas las conexiones WebSocket activas
   */
  broadcastToConnections(eventType, data) {
    const message = JSON.stringify({
      type: eventType,
      data,
      timestamp: new Date().toISOString()
    });

    this.activeConnections.forEach((ws, connectionId) => {
      try {
        if (ws.readyState === ws.OPEN) {
          ws.send(message);
        } else {
          // Limpiar conexiones cerradas
          this.activeConnections.delete(connectionId);
        }
      } catch (error) {
        loggers.main.error('Error broadcasting to WebSocket', { 
          connectionId, 
          error: error.message 
        });
        this.activeConnections.delete(connectionId);
      }
    });
  }

  /**
   * Valida las opciones de la petición
   */
  validateOptions(options) {
    const validatedOptions = {
      priority: options.priority || 'normal',
      timeout: Math.min(options.timeout || 300000, 600000), // Máximo 10 minutos
      context: options.context || {},
      analysisType: options.analysisType || 'interactive',
      includeContent: Boolean(options.includeContent),
      maxRetries: Math.min(options.maxRetries || 3, 5)
    };

    // Validar prioridad
    if (!['low', 'normal', 'high', 'urgent'].includes(validatedOptions.priority)) {
      validatedOptions.priority = 'normal';
    }

    return validatedOptions;
  }

  /**
   * Ejecuta un test básico del sistema
   */
  async runBasicTest(instruction) {
    try {
      const testOptions = {
        priority: 'high',
        timeout: 60000, // 1 minuto para test
        context: { isTest: true }
      };

      const startTime = Date.now();
      const result = await this.orchestrator.processUserInstruction(instruction, testOptions);
      const duration = Date.now() - startTime;

      return {
        success: true,
        duration,
        result,
        systemStatus: this.orchestrator.getStatus()
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        systemStatus: this.orchestrator.getStatus()
      };
    }
  }

  /**
   * Genera ID único para conexiones WebSocket
   */
  generateConnectionId() {
    return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Limpia recursos y detiene el sistema
   */
  async shutdown() {
    try {
      loggers.main.info('Shutting down Agent Controller');

      // Cerrar todas las conexiones WebSocket
      this.activeConnections.forEach((ws, connectionId) => {
        try {
          ws.close();
        } catch (error) {
          loggers.main.error('Error closing WebSocket connection', { 
            connectionId, 
            error: error.message 
          });
        }
      });

      this.activeConnections.clear();

      // Detener el orchestrator
      await this.orchestrator.stop();

      loggers.main.info('Agent Controller shutdown completed');

    } catch (error) {
      loggers.main.error('Error during shutdown', { error: error.message });
      throw error;
    }
  }
}

// Exportar instancia singleton
export const agentController = new AgentController();
export default agentController;
