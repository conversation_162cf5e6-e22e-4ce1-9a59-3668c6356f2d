const winston = require('winston');
const path = require('path');

// Create logs directory if it doesn't exist
const fs = require('fs');
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  verbose: 4,
  debug: 5,
  silly: 6
};

// Define log colors
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  verbose: 'grey',
  debug: 'white',
  silly: 'rainbow'
};

winston.addColors(logColors);

// Create custom format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Create file format (without colors)
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Create transports
const transports = [
  // Console transport
  new winston.transports.Console({
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat
  }),
  
  // File transport for all logs
  new winston.transports.File({
    filename: path.join(logsDir, 'autonobot.log'),
    level: 'debug',
    format: fileFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5
  }),
  
  // File transport for errors only
  new winston.transports.File({
    filename: path.join(logsDir, 'error.log'),
    level: 'error',
    format: fileFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5
  }),
  
  // File transport for API calls
  new winston.transports.File({
    filename: path.join(logsDir, 'api.log'),
    level: 'http',
    format: fileFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 3
  })
];

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels: logLevels,
  format: logFormat,
  transports,
  exitOnError: false
});

// Create specialized loggers for different components
class ComponentLogger {
  constructor(component) {
    this.component = component;
  }

  formatMessage(message, data = null) {
    const baseMessage = `[${this.component}] ${message}`;
    return data ? `${baseMessage} | Data: ${JSON.stringify(data)}` : baseMessage;
  }

  error(message, data = null) {
    logger.error(this.formatMessage(message, data));
  }

  warn(message, data = null) {
    logger.warn(this.formatMessage(message, data));
  }

  info(message, data = null) {
    logger.info(this.formatMessage(message, data));
  }

  http(message, data = null) {
    logger.http(this.formatMessage(message, data));
  }

  debug(message, data = null) {
    logger.debug(this.formatMessage(message, data));
  }

  verbose(message, data = null) {
    logger.verbose(this.formatMessage(message, data));
  }
}

// Create component-specific loggers
const loggers = {
  main: new ComponentLogger('MAIN'),
  agent: new ComponentLogger('AGENT'),
  browser: new ComponentLogger('BROWSER'),
  llm: new ComponentLogger('LLM'),
  search: new ComponentLogger('SEARCH'),
  voice: new ComponentLogger('VOICE'),
  api: new ComponentLogger('API'),
  config: new ComponentLogger('CONFIG')
};

// Performance monitoring
class PerformanceLogger {
  constructor() {
    this.timers = new Map();
  }

  start(operation) {
    this.timers.set(operation, Date.now());
    loggers.main.debug(`Performance timer started for: ${operation}`);
  }

  end(operation) {
    const startTime = this.timers.get(operation);
    if (startTime) {
      const duration = Date.now() - startTime;
      loggers.main.info(`Performance: ${operation} completed in ${duration}ms`);
      this.timers.delete(operation);
      return duration;
    }
    loggers.main.warn(`Performance timer not found for: ${operation}`);
    return null;
  }
}

const performance = new PerformanceLogger();

// Error tracking
class ErrorTracker {
  constructor() {
    this.errorCounts = new Map();
    this.lastErrors = [];
    this.maxLastErrors = 100;
  }

  track(error, context = '') {
    const errorKey = `${error.name || 'Unknown'}: ${error.message || 'No message'}`;
    const count = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, count + 1);

    const errorInfo = {
      timestamp: new Date().toISOString(),
      error: errorKey,
      context,
      stack: error.stack,
      count: count + 1
    };

    this.lastErrors.unshift(errorInfo);
    if (this.lastErrors.length > this.maxLastErrors) {
      this.lastErrors.pop();
    }

    loggers.main.error(`Error tracked: ${errorKey}`, { context, count: count + 1 });
  }

  getStats() {
    return {
      totalUniqueErrors: this.errorCounts.size,
      errorCounts: Object.fromEntries(this.errorCounts),
      recentErrors: this.lastErrors.slice(0, 10)
    };
  }
}

const errorTracker = new ErrorTracker();

// API call logging middleware
const apiLogger = (req, res, next) => {
  const start = Date.now();
  const { method, url, ip } = req;
  
  loggers.api.http(`${method} ${url} - IP: ${ip}`);
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const { statusCode } = res;
    loggers.api.http(`${method} ${url} - ${statusCode} - ${duration}ms`);
  });
  
  next();
};

module.exports = {
  logger,
  loggers,
  performance,
  errorTracker,
  apiLogger,
  ComponentLogger
};
