import React from 'react';
import { Play } from 'lucide-react';

interface TaskInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  isDisabled: boolean;
}

const TaskInput: React.FC<TaskInputProps> = ({ 
  value, 
  onChange, 
  onSubmit, 
  isDisabled 
}) => {
  return (
    <div className="glass-panel p-6 rounded-xl neon-border">
      <h2 className="text-2xl font-semibold mb-4 neon-text">Task Input</h2>
      <div className="mb-4">
        <label htmlFor="task-input" className="block text-sm font-medium text-blue-200 mb-2">
          What would you like AUTONOBOT to do?
        </label>
        <textarea
          id="task-input"
          className="w-full p-4 bg-gray-900/50 border border-[rgb(var(--neon-primary))/30 rounded-lg 
                   focus:outline-none focus:ring-2 focus:ring-[rgb(var(--neon-primary))] focus:border-transparent
                   transition duration-300 resize-y min-h-[100px] text-blue-100"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="E.g., 'Find the top 3 best-rated laptops under $1000 on Amazon'"
          disabled={isDisabled}
        ></textarea>
      </div>
      <button
        onClick={onSubmit}
        className={`w-full py-4 px-6 rounded-lg font-semibold transition-all duration-300 
                   flex items-center justify-center group
                   ${isDisabled 
          ? 'bg-gray-700 cursor-not-allowed' 
          : 'bg-[rgb(var(--neon-primary))] hover:bg-[rgb(var(--neon-primary))/90] neon-border'
        }`}
        disabled={isDisabled}
      >
        {isDisabled ? (
          <span className="flex items-center">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
          </span>
        ) : (
          <>
            <Play size={20} className="mr-2 transform group-hover:translate-x-1 transition-transform duration-300" />
            Start Task
          </>
        )}
      </button>
    </div>
  );
};

export default TaskInput;