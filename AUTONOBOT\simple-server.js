// Simplified AUTONOBOT server for testing
import dotenv from 'dotenv';
import express from 'express';
import cors from 'cors';
import path from 'path';
import autonomousAgentRoutes from './backend/routes/autonomousAgentRoutes.js';

dotenv.config();

const app = express();
const port = process.env.PORT || 5000;

// Basic middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Basic routes
app.get('/', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.status(200).json({
    name: 'AUTONOBOT API Server',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    message: 'Simple server mode - some features may be limited',
    endpoints: {
      health: '/health',
      search: '/api/autonobot/search/search',
      agent: '/api/autonobot/start-task'
    },
    frontend: 'http://localhost:5173'
  });
});

app.get('/health', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    mode: 'simple',
    services: {
      frontend: 'http://localhost:5173',
      backend: 'http://localhost:5000'
    }
  });
});

// Status endpoint with more details
app.get('/status', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.status(200).json({
    application: 'AUTONOBOT',
    version: '1.0.0',
    status: 'running',
    mode: 'simplified',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV || 'development',
    services: {
      frontend: {
        url: 'http://localhost:5173',
        status: 'should be running'
      },
      backend: {
        url: 'http://localhost:5000',
        status: 'running'
      }
    },
    features: {
      webSearch: 'mock mode',
      aiIntegration: 'configured',
      voiceRecognition: 'available',
      webNavigation: 'limited'
    },
    endpoints: {
      root: '/',
      health: '/health',
      status: '/status',
      search: '/api/autonobot/search/search',
      agent: '/api/autonobot/start-task'
    }
  });
});

// Basic search endpoint (mock)
app.post('/api/autonobot/search/search', (req, res) => {
  const { query } = req.body;
  res.json({
    query,
    results: [
      {
        title: 'Mock Search Result',
        url: 'https://example.com',
        snippet: 'This is a mock search result for testing purposes.'
      }
    ],
    provider: 'mock',
    timestamp: new Date().toISOString()
  });
});

// Autonomous Agent Routes
app.use('/api/autonomous', autonomousAgentRoutes);

// Basic agent endpoint (mock) - mantener para compatibilidad
app.post('/api/autonobot/start-task', (req, res) => {
  const { taskInput } = req.body;
  res.json({
    taskId: 'mock-task-' + Date.now(),
    status: 'started',
    message: 'Task started in simple mode - use /api/autonomous/process for full autonomous capabilities',
    taskInput,
    timestamp: new Date().toISOString(),
    recommendation: 'Use POST /api/autonomous/process for advanced autonomous navigation'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Server error:', error.message);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message,
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(port, () => {
  console.log(`🤖 AUTONOBOT Simple Server listening at http://localhost:${port}`);
  console.log(`📊 Health check available at http://localhost:${port}/health`);
  console.log(`🔍 Mock search API available at http://localhost:${port}/api/autonobot/search/search`);
  console.log(`🎯 Mock agent API available at http://localhost:${port}/api/autonobot/start-task`);
  console.log(`\n✨ Frontend should be running at http://localhost:5173`);
  console.log(`\n🚀 AUTONOBOT is ready! Open http://localhost:5173 in your browser.`);
});
