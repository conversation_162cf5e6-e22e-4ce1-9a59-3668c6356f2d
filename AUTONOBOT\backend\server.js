require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const agentRoutes = require('./routes/agentRoutes');
const searchRoutes = require('./routes/searchRoutes');
const browserService = require('./services/browserService');
const { loggers, apiLogger } = require('./services/loggerService');
const configService = require('./services/configService');

const app = express();
const port = configService.get('server.port');

// Security middleware
if (configService.get('security.enableHelmet')) {
  app.use(helmet({
    contentSecurityPolicy: false, // Disable for development
    crossOriginEmbedderPolicy: false
  }));
}

// Rate limiting
if (configService.get('security.enableRateLimit')) {
  const limiter = rateLimit({
    windowMs: configService.get('security.rateLimitWindow'),
    max: configService.get('security.rateLimitMax'),
    message: {
      error: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.ceil(configService.get('security.rateLimitWindow') / 1000 / 60) + ' minutes'
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use('/api/', limiter);
}

// CORS configuration
app.use(cors({
  origin: configService.get('security.corsOrigins'),
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// API logging middleware
app.use('/api/', apiLogger);

// Initialize browser when server starts
browserService.initializeBrowser().then(() => {
  loggers.main.info('Playwright browser initialized successfully');
}).catch(err => {
  loggers.main.error('Error initializing browser', { error: err.message });
  process.exit(1); // Exit if browser cannot be started
});

// Process cleanup
process.on('exit', async () => {
  await browserService.closeBrowser();
  loggers.main.info('Browser closed on exit');
});

process.on('SIGINT', async () => {
  await browserService.closeBrowser();
  loggers.main.info('Browser closed on SIGINT');
  process.exit(0);
});

process.on('uncaughtException', (error) => {
  loggers.main.error('Uncaught Exception', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  loggers.main.error('Unhandled Rejection', { reason, promise });
});

// Routes
app.use('/api/autonobot', agentRoutes);
app.use('/api/autonobot/search', searchRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    services: {
      browser: !!browserService,
      config: configService.getStatus()
    }
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    name: 'AUTONOBOT API Server',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      agent: '/api/autonobot',
      search: '/api/autonobot/search'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Error handler
app.use((error, req, res, next) => {
  loggers.main.error('Express error handler', {
    error: error.message,
    path: req.path,
    method: req.method
  });

  res.status(500).json({
    error: 'Internal server error',
    message: configService.isDevelopment() ? error.message : 'Something went wrong',
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(port, () => {
  loggers.main.info(`AUTONOBOT server started successfully`, {
    port,
    environment: configService.get('server.nodeEnv'),
    availableServices: {
      search: configService.getAvailableSearchProviders().length > 0,
      llm: configService.getAvailableLLMProviders().length > 0,
      voice: configService.get('voice.enabled')
    }
  });
  console.log(`🤖 AUTONOBOT server listening at http://localhost:${port}`);
  console.log(`📊 Health check available at http://localhost:${port}/health`);
});