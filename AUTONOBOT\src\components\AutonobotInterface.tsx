import React, { useState, useEffect, useRef } from 'react';
import { Loader2, Mic } from 'lucide-react';
import TaskInput from './TaskInput';
import AgentConsole from './AgentConsole';
import StatusDisplay from './StatusDisplay';
import ResultsPanel from './ResultsPanel';
import { fetchAgentStatus, startAgentTask } from '../services/apiService';
import { voiceService } from '../services/voiceService';
import { aiService } from '../services/aiService';

const AutonobotInterface: React.FC = () => {
  const [taskInput, setTaskInput] = useState('');
  const [agentLog, setAgentLog] = useState<string[]>([]);
  const [results, setResults] = useState('');
  const [currentUrl, setCurrentUrl] = useState('about:blank');
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  
  const statusInterval = useRef<number | null>(null);

  useEffect(() => {
    // Initialize voice recognition
    voiceService.initialize(async (command) => {
      setIsListening(false);
      setTaskInput(command);
      const response = await aiService.processCommand(command);
      voiceService.speak(response);
      handleStartTask(command);
    });

    return () => {
      if (statusInterval.current) {
        clearInterval(statusInterval.current);
      }
      voiceService.cleanup();
    };
  }, []);

  const pollAgentStatus = () => {
    if (statusInterval.current) {
      clearInterval(statusInterval.current);
    }
    
    const interval = window.setInterval(async () => {
      try {
        const response = await fetchAgentStatus();
        const { log, status, results, currentUrl } = response;
        
        setAgentLog(log);
        setResults(results);
        setCurrentUrl(currentUrl);
        
        if (status === 'finished') {
          setIsLoading(false);
          clearInterval(interval);
          if (results) {
            voiceService.speak(results);
          }
        }
      } catch (error) {
        console.error("Error fetching agent status:", error);
        setAgentLog(prev => [...prev, `[ERROR UI] Connection to backend failed`]);
        setIsLoading(false);
        clearInterval(interval);
      }
    }, 2000);
    
    statusInterval.current = interval;
  };

  const handleStartTask = async (input: string = taskInput) => {
    if (!input.trim()) {
      setAgentLog(prev => [...prev, "[ERROR] Please enter a task."]);
      return;
    }

    setAgentLog([]);
    setResults('');
    setCurrentUrl('about:blank');
    setIsLoading(true);

    try {
      const response = await startAgentTask(input);
      setAgentLog([`[System] ${response.message}`]);
      pollAgentStatus();
    } catch (error) {
      console.error("Error starting task:", error);
      setAgentLog([`[ERROR UI] Failed to start task`]);
      setIsLoading(false);
    }
  };

  const toggleListening = () => {
    setIsListening(!isListening);
    if (!isListening) {
      voiceService.speak('Listening for commands...');
    }
  };

  return (
    <div className="p-4 md:p-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="flex flex-col space-y-6">
          <TaskInput 
            value={taskInput}
            onChange={setTaskInput}
            onSubmit={() => handleStartTask()}
            isDisabled={isLoading}
          />
          
          <button
            onClick={toggleListening}
            className={`p-4 rounded-lg flex items-center justify-center transition-all duration-300
                      ${isListening 
                        ? 'bg-red-500 hover:bg-red-600' 
                        : 'bg-[rgb(var(--neon-primary))] hover:bg-[rgb(var(--neon-primary))/90]'
                      } neon-border`}
          >
            <Mic className={`mr-2 ${isListening ? 'animate-pulse' : ''}`} />
            {isListening ? 'Stop Listening' : 'Start Voice Commands'}
          </button>
          
          <StatusDisplay currentUrl={currentUrl} />
        </div>

        <AgentConsole logs={agentLog} />
      </div>

      <ResultsPanel 
        results={results} 
        isLoading={isLoading}
      />
      
      {isLoading && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 pointer-events-none">
          <div className="glass-panel p-6 rounded-xl flex items-center">
            <Loader2 className="animate-spin mr-3 text-[rgb(var(--neon-primary))]" />
            <span className="font-semibold text-[rgb(var(--neon-primary))]">AUTONOBOT is working...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default AutonobotInterface;