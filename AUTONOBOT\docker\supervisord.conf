[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
user=root

[program:vnc]
command=/usr/bin/tightvncserver :1 -geometry %(ENV_VNC_RESOLUTION)s -depth %(ENV_VNC_COL_DEPTH)s -localhost no
user=autonobot
autorestart=true
stdout_logfile=/var/log/supervisor/vnc.log
stderr_logfile=/var/log/supervisor/vnc_error.log
environment=HOME="/home/<USER>",USER="autonobot"

[program:novnc]
command=/usr/share/novnc/utils/launch.sh --vnc localhost:5901 --listen 6901
user=autonobot
autorestart=true
stdout_logfile=/var/log/supervisor/novnc.log
stderr_logfile=/var/log/supervisor/novnc_error.log

[program:selenium-hub]
command=python3 /home/<USER>/autonobot-workspace/selenium_server.py
user=autonobot
autorestart=true
stdout_logfile=/var/log/supervisor/selenium.log
stderr_logfile=/var/log/supervisor/selenium_error.log
environment=HOME="/home/<USER>",USER="autonobot",DISPLAY=":1"

[program:autonobot-bridge]
command=python3 /home/<USER>/autonobot-workspace/autonobot_bridge.py
user=autonobot
autorestart=true
stdout_logfile=/var/log/supervisor/bridge.log
stderr_logfile=/var/log/supervisor/bridge_error.log
environment=HOME="/home/<USER>",USER="autonobot",DISPLAY=":1"
