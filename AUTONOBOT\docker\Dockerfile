# AUTONOBOT Remote Desktop Environment
# Ubuntu with XFCE desktop, VNC server, and Chrome browser
FROM ubuntu:22.04

# Evitar prompts interactivos durante la instalación
ENV DEBIAN_FRONTEND=noninteractive
ENV DISPLAY=:1
ENV VNC_PORT=5901
ENV NO_VNC_PORT=6901
ENV VNC_RESOLUTION=1920x1080
ENV VNC_COL_DEPTH=24

# Instalar dependencias del sistema
RUN apt-get update && apt-get install -y \
    # Desktop environment
    xfce4 xfce4-goodies \
    # VNC server
    tightvncserver \
    # noVNC para acceso web
    novnc websockify \
    # Navegador y herramientas
    google-chrome-stable \
    firefox \
    # Herramientas de desarrollo
    curl wget git \
    # Python y Selenium
    python3 python3-pip \
    # Node.js
    nodejs npm \
    # Utilidades
    supervisor \
    dbus-x11 \
    xfonts-base \
    xauth \
    # Herramientas de red
    net-tools \
    # Editor de texto
    nano vim \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Instalar Google Chrome
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && apt-get clean

# Instalar ChromeDriver
RUN CHROME_VERSION=$(google-chrome --version | grep -oP '\d+\.\d+\.\d+') \
    && CHROMEDRIVER_VERSION=$(curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${CHROME_VERSION%%.*}") \
    && wget -O /tmp/chromedriver.zip "https://chromedriver.storage.googleapis.com/${CHROMEDRIVER_VERSION}/chromedriver_linux64.zip" \
    && unzip /tmp/chromedriver.zip -d /tmp/ \
    && mv /tmp/chromedriver /usr/local/bin/ \
    && chmod +x /usr/local/bin/chromedriver \
    && rm /tmp/chromedriver.zip

# Instalar dependencias de Python
RUN pip3 install \
    selenium \
    webdriver-manager \
    requests \
    websockets \
    asyncio \
    pillow \
    numpy

# Crear usuario para VNC
RUN useradd -m -s /bin/bash autonobot \
    && echo "autonobot:autonobot" | chpasswd \
    && usermod -aG sudo autonobot

# Configurar VNC para el usuario
USER autonobot
WORKDIR /home/<USER>

# Configurar VNC password
RUN mkdir -p ~/.vnc \
    && echo "autonobot" | vncpasswd -f > ~/.vnc/passwd \
    && chmod 600 ~/.vnc/passwd

# Configurar startup script para VNC
RUN echo '#!/bin/bash' > ~/.vnc/xstartup \
    && echo 'xrdb $HOME/.Xresources' >> ~/.vnc/xstartup \
    && echo 'startxfce4 &' >> ~/.vnc/xstartup \
    && chmod +x ~/.vnc/xstartup

# Crear directorio para aplicaciones
RUN mkdir -p ~/autonobot-workspace

# Volver a root para configuración final
USER root

# Configurar supervisor para gestionar servicios
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Script de inicio
COPY start-services.sh /usr/local/bin/start-services.sh
RUN chmod +x /usr/local/bin/start-services.sh

# Configurar noVNC
RUN ln -s /usr/share/novnc/vnc.html /usr/share/novnc/index.html

# Exponer puertos
EXPOSE 5901 6901 4444 8080

# Crear directorio para logs
RUN mkdir -p /var/log/supervisor

# Comando de inicio
CMD ["/usr/local/bin/start-services.sh"]
