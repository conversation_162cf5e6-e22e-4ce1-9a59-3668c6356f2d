# AUTONOBOT Startup Script
Write-Host "🤖 Starting AUTONOBOT..." -ForegroundColor Green
Write-Host ""

# Change to script directory
Set-Location $PSScriptRoot
Write-Host "📁 Current directory: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

# Function to check if port is available
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Check if ports are available
Write-Host "🔍 Checking ports..." -ForegroundColor Cyan
if (Test-Port 5000) {
    Write-Host "⚠️  Port 5000 is already in use. Please close the application using it." -ForegroundColor Red
    Read-Host "Press Enter to continue anyway"
}

if (Test-Port 5173) {
    Write-Host "⚠️  Port 5173 is already in use. Please close the application using it." -ForegroundColor Red
    Read-Host "Press Enter to continue anyway"
}

# Check if Node.js supports ES modules
Write-Host "🔧 Checking Node.js version..." -ForegroundColor Cyan
$nodeVersion = node --version
Write-Host "   Node.js version: $nodeVersion" -ForegroundColor Gray

# Start backend server
Write-Host "🔧 Starting Backend Server..." -ForegroundColor Blue
$backendJob = Start-Job -ScriptBlock {
    Set-Location $using:PSScriptRoot
    node simple-server.js
}

# Wait for backend to start
Start-Sleep -Seconds 3

# Start frontend server
Write-Host "🎨 Starting Frontend Server..." -ForegroundColor Magenta
$frontendJob = Start-Job -ScriptBlock {
    Set-Location $using:PSScriptRoot
    npx vite
}

# Wait for frontend to start
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "✅ AUTONOBOT is starting up!" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Frontend: http://localhost:5173" -ForegroundColor Cyan
Write-Host "🔧 Backend:  http://localhost:5000" -ForegroundColor Cyan
Write-Host "📊 Health:   http://localhost:5000/health" -ForegroundColor Cyan
Write-Host ""

# Open browser
Write-Host "🚀 Opening browser..." -ForegroundColor Yellow
Start-Process "http://localhost:5173"

Write-Host ""
Write-Host "📝 Press Ctrl+C to stop all servers" -ForegroundColor Yellow
Write-Host ""

# Monitor jobs
try {
    while ($true) {
        if ($backendJob.State -eq "Failed") {
            Write-Host "❌ Backend server failed!" -ForegroundColor Red
            break
        }
        if ($frontendJob.State -eq "Failed") {
            Write-Host "❌ Frontend server failed!" -ForegroundColor Red
            break
        }
        Start-Sleep -Seconds 2
    }
}
finally {
    Write-Host "🛑 Stopping servers..." -ForegroundColor Yellow
    Stop-Job $backendJob, $frontendJob -ErrorAction SilentlyContinue
    Remove-Job $backendJob, $frontendJob -ErrorAction SilentlyContinue
    Write-Host "✅ Servers stopped." -ForegroundColor Green
}
