import React, { useEffect, useRef } from 'react';
import { Terminal } from 'lucide-react';

interface AgentConsoleProps {
  logs: string[];
}

const AgentConsole: React.FC<AgentConsoleProps> = ({ logs }) => {
  const consoleRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when logs update
  useEffect(() => {
    if (consoleRef.current) {
      consoleRef.current.scrollTop = consoleRef.current.scrollHeight;
    }
  }, [logs]);

  return (
    <div className="bg-gray-50 p-6 rounded-lg shadow-md border border-gray-200 flex flex-col h-full">
      <div className="flex items-center mb-4">
        <Terminal size={20} className="text-blue-800 mr-2" />
        <h2 className="text-xl font-semibold text-blue-800">Console</h2>
      </div>
      
      <div 
        ref={consoleRef}
        className="flex-grow bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-y-auto h-64 shadow-inner"
      >
        {logs.length > 0 ? (
          logs.map((log, index) => {
            // Apply different styling based on log type
            let logClass = "mb-1";
            
            if (log.includes("[ERROR]")) {
              logClass += " text-red-400";
            } else if (log.includes("[Decisión]") || log.includes("[AUTONOBOT]")) {
              logClass += " text-blue-300";
            } else if (log.includes("[Percepción]")) {
              logClass += " text-purple-300";
            } else if (log.includes("[Ejecución]")) {
              logClass += " text-yellow-300";
            } else if (log.includes("[Sistema]")) {
              logClass += " text-gray-400";
            }
            
            return <p key={index} className={logClass}>{log}</p>;
          })
        ) : (
          <p className="text-gray-500">Waiting for task to start...</p>
        )}
      </div>
    </div>
  );
};

export default AgentConsole;