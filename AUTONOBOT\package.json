{"name": "autonobot-web-agent", "private": true, "version": "0.1.0", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "backend": "node backend/server.js", "start": "concurrently \"npm run dev\" \"npm run backend\""}, "dependencies": {"@anthropic-ai/sdk": "^0.17.1", "@google/generative-ai": "^0.2.0", "annyang": "^2.6.1", "axios": "^1.6.7", "cheerio": "^1.0.0-rc.12", "concurrently": "^8.2.2", "cors": "^2.8.5", "dotenv": "^16.4.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "lucide-react": "^0.344.0", "node-config": "^0.0.2", "openai": "^4.24.1", "playwright": "^1.41.2", "react": "^18.3.1", "react-dom": "^18.3.1", "winston": "^3.11.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/annyang": "^2.6.5", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}