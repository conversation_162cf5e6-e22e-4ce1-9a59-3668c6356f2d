# 🤖 AUTONOBOT - Guía de Instalación y Ejecución

## ✅ Estado de la Instalación

**¡AUTONOBOT está instalado y funcionando!** 🎉

### 🔧 Componentes Instalados:
- ✅ Node.js y dependencias
- ✅ Frontend (React + Vite)
- ✅ Backend (Express.js)
- ✅ Servidor simplificado funcional

## 🚀 Cómo Ejecutar AUTONOBOT

### Opción 1: Ejecución Automática (Recomendada)

**Windows Batch Script:**
```bash
# Hacer doble clic en:
start-autonobot.bat
```

**PowerShell Script:**
```powershell
# Ejecutar en PowerShell:
.\start-autonobot.ps1
```

### Opción 2: Ejecución Manual

**Terminal 1 - Backend:**
```bash
cd AUTONOBOT
node simple-server.js
```

**Terminal 2 - Frontend:**
```bash
cd AUTONOBOT
npx vite
```

## 🌐 URLs de Acceso

Una vez ejecutado, AUTONOBOT estará disponible en:

- **🎨 Aplicación Web:** http://localhost:5173
- **🔧 API Backend:** http://localhost:5000
- **📊 Health Check:** http://localhost:5000/health

## 🎯 Funcionalidades Disponibles

### ✅ Funcionando:
- ✅ Interfaz web React
- ✅ Servidor backend Express
- ✅ APIs básicas (mock)
- ✅ Health check
- ✅ CORS configurado

### 🔄 En Modo Simplificado:
- 🔄 Búsqueda web (modo mock)
- 🔄 Integración AI (modo mock)
- 🔄 Navegación web (requiere configuración adicional)

## 🔧 Configuración de APIs

Para habilitar todas las funcionalidades, edita el archivo `.env`:

```env
# APIs de IA (ya configuradas)
VITE_OPENAI_API_KEY=********************************************************************************************************************************************************************
VITE_GEMINI_API_KEY=AIzaSyD6ikk1VkjOYOZiYUapm8wmySYwBG0qviQ
VITE_ANTHROPIC_API_KEY=************************************************************************************************************

# APIs de Búsqueda (opcional)
VITE_GOOGLE_SEARCH_API_KEY=tu_clave_aqui
VITE_GOOGLE_SEARCH_ENGINE_ID=tu_id_aqui
VITE_BING_SEARCH_API_KEY=tu_clave_aqui
```

## 🛠️ Solución de Problemas

### Puerto en Uso:
```bash
# Si el puerto 5000 está ocupado:
netstat -ano | findstr :5000
taskkill /PID <PID> /F

# Si el puerto 5173 está ocupado:
netstat -ano | findstr :5173
taskkill /PID <PID> /F
```

### Reinstalar Dependencias:
```bash
cd AUTONOBOT
npm install
```

### Limpiar Cache:
```bash
npm cache clean --force
```

## 📝 Archivos Importantes

- `package.json` - Configuración del proyecto
- `simple-server.js` - Servidor backend simplificado
- `.env` - Variables de entorno y API keys
- `start-autonobot.bat` - Script de inicio para Windows
- `start-autonobot.ps1` - Script de inicio para PowerShell

## 🎮 Cómo Usar AUTONOBOT

1. **Abrir la aplicación:** http://localhost:5173
2. **Probar comandos de voz:** Decir "autonobot" para activar
3. **Usar la interfaz:** Escribir comandos en el campo de texto
4. **Monitorear:** Ver logs en la consola del navegador

## 🔄 Próximos Pasos

Para habilitar funcionalidades avanzadas:

1. **Configurar APIs de búsqueda** (Google/Bing)
2. **Instalar Playwright** para navegación web:
   ```bash
   npx playwright install
   ```
3. **Configurar servicios adicionales** según necesidades

## 📞 Soporte

Si encuentras problemas:
1. Verifica que Node.js esté instalado
2. Revisa que los puertos 5000 y 5173 estén libres
3. Comprueba los logs en la consola del navegador
4. Reinicia los servidores

---

**¡AUTONOBOT está listo para usar! 🚀**
