#!/usr/bin/env node

/**
 * AUTONOBOT Autonomous System Launcher
 * Inicia el sistema completo de agentes autónomos
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🤖 AUTONOBOT Autonomous System Launcher');
console.log('=====================================\n');

// Verificar que estamos en el directorio correcto
const packageJsonPath = join(__dirname, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Error: package.json not found. Make sure you are in the AUTONOBOT directory.');
  process.exit(1);
}

// Verificar dependencias
console.log('📦 Checking dependencies...');
const nodeModulesPath = join(__dirname, 'node_modules');
if (!fs.existsSync(nodeModulesPath)) {
  console.log('⚠️  Node modules not found. Installing dependencies...');
  
  const npmInstall = spawn('npm', ['install'], {
    cwd: __dirname,
    stdio: 'inherit',
    shell: true
  });
  
  npmInstall.on('close', (code) => {
    if (code === 0) {
      console.log('✅ Dependencies installed successfully');
      startSystem();
    } else {
      console.error('❌ Failed to install dependencies');
      process.exit(1);
    }
  });
} else {
  console.log('✅ Dependencies found');
  startSystem();
}

function startSystem() {
  console.log('\n🚀 Starting AUTONOBOT Autonomous System...\n');
  
  // Iniciar backend
  console.log('🔧 Starting backend server...');
  const backend = spawn('node', ['simple-autonomous-server.js'], {
    cwd: __dirname,
    stdio: ['inherit', 'pipe', 'pipe'],
    shell: true
  });
  
  backend.stdout.on('data', (data) => {
    console.log(`[BACKEND] ${data.toString().trim()}`);
  });
  
  backend.stderr.on('data', (data) => {
    console.error(`[BACKEND ERROR] ${data.toString().trim()}`);
  });
  
  // Esperar un poco antes de iniciar el frontend
  setTimeout(() => {
    console.log('🎨 Starting frontend server...');
    const frontend = spawn('npm', ['run', 'dev'], {
      cwd: __dirname,
      stdio: ['inherit', 'pipe', 'pipe'],
      shell: true
    });
    
    frontend.stdout.on('data', (data) => {
      const output = data.toString().trim();
      console.log(`[FRONTEND] ${output}`);
      
      // Detectar cuando el frontend esté listo
      if (output.includes('Local:') && output.includes('5173')) {
        setTimeout(() => {
          console.log('\n🎉 AUTONOBOT Autonomous System is ready!');
          console.log('📱 Frontend: http://localhost:5173');
          console.log('🔧 Backend:  http://localhost:5000');
          console.log('🧪 Test Page: http://localhost:5173/test-frontend-api.html');
          console.log('\n💡 Instructions:');
          console.log('   1. Open http://localhost:5173 in your browser');
          console.log('   2. Click "Autonomous Agents" in the header');
          console.log('   3. Enter instructions in natural language');
          console.log('   4. Click "Process" to execute');
          console.log('\n📝 Example instructions:');
          console.log('   • "Search for React best practices"');
          console.log('   • "Find information about AI frameworks"');
          console.log('   • "Navigate to GitHub trending repositories"');
          console.log('\n🛑 Press Ctrl+C to stop the system');
        }, 2000);
      }
    });
    
    frontend.stderr.on('data', (data) => {
      const error = data.toString().trim();
      if (!error.includes('WARN') && !error.includes('deprecated')) {
        console.error(`[FRONTEND ERROR] ${error}`);
      }
    });
    
    frontend.on('close', (code) => {
      console.log(`\n🔌 Frontend server stopped (code: ${code})`);
      if (backend.pid) {
        console.log('🛑 Stopping backend server...');
        backend.kill();
      }
      process.exit(code);
    });
    
  }, 3000);
  
  backend.on('close', (code) => {
    console.log(`\n🔌 Backend server stopped (code: ${code})`);
    process.exit(code);
  });
  
  // Manejar señales de terminación
  process.on('SIGINT', () => {
    console.log('\n\n🛑 Shutting down AUTONOBOT Autonomous System...');
    
    if (backend.pid) {
      console.log('🔧 Stopping backend server...');
      backend.kill('SIGTERM');
    }
    
    setTimeout(() => {
      console.log('✅ AUTONOBOT Autonomous System stopped');
      process.exit(0);
    }, 2000);
  });
  
  process.on('SIGTERM', () => {
    console.log('\n\n🛑 Received SIGTERM, shutting down...');
    if (backend.pid) {
      backend.kill('SIGTERM');
    }
    process.exit(0);
  });
}

// Manejar errores no capturados
process.on('uncaughtException', (error) => {
  console.error('\n💥 Uncaught Exception:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('\n💥 Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});
