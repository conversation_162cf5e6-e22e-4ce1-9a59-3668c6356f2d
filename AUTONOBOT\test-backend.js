// Simple test to check if backend dependencies work
console.log('🧪 Testing AUTONOBOT Backend Dependencies...');

try {
  // Test basic requires
  console.log('✅ Testing require statements...');
  require('dotenv').config();
  const express = require('express');
  const cors = require('cors');
  
  console.log('✅ Basic dependencies loaded successfully');
  
  // Test if we can create an express app
  const app = express();
  console.log('✅ Express app created successfully');
  
  // Test basic middleware
  app.use(cors());
  app.use(express.json());
  console.log('✅ Basic middleware configured');
  
  // Test basic route
  app.get('/test', (req, res) => {
    res.json({ status: 'Backend test successful!' });
  });
  
  // Start server on different port for testing
  const port = 5001;
  const server = app.listen(port, () => {
    console.log(`✅ Test server running on http://localhost:${port}`);
    console.log('🎉 Backend dependencies test completed successfully!');
    
    // Close server after test
    setTimeout(() => {
      server.close();
      console.log('✅ Test server closed');
    }, 2000);
  });
  
} catch (error) {
  console.error('❌ Backend test failed:', error.message);
  process.exit(1);
}
