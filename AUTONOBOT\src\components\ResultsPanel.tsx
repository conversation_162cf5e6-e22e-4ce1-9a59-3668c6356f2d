import React from 'react';
import { FileText, Loader2 } from 'lucide-react';

interface ResultsPanelProps {
  results: string;
  isLoading: boolean;
}

const ResultsPanel: React.FC<ResultsPanelProps> = ({ results, isLoading }) => {
  return (
    <div className="mt-8 bg-gray-50 p-6 rounded-lg shadow-md border border-gray-200">
      <div className="flex items-center mb-4">
        <FileText size={20} className="text-blue-800 mr-2" />
        <h2 className="text-xl font-semibold text-blue-800">Results</h2>
      </div>
      <div className="bg-white p-6 rounded-lg border border-blue-200 shadow-inner min-h-[150px]">
        {isLoading ? (
          <div className="flex items-center justify-center h-full text-gray-500">
            <Loader2 className="animate-spin mr-2" />
            <span>Waiting for results...</span>
          </div>
        ) : results ? (
          <div className="text-gray-700 whitespace-pre-wrap">{results}</div>
        ) : (
          <p className="text-gray-500 text-center">Results will appear here when AUTONOBOT completes the task.</p>
        )}
      </div>
    </div>
  );
};

export default ResultsPanel;