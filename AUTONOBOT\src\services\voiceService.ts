import annyang from 'annyang';

interface VoiceConfig {
  wakeWord: string;
  responseDelay: number;
  maxListeningTime: number;
  voiceSettings: {
    rate: number;
    pitch: number;
    volume: number;
    voice: string;
  };
}

interface VoiceStats {
  totalCommands: number;
  successfulCommands: number;
  failedCommands: number;
  averageConfidence: number;
  isListening: boolean;
  isSupported: boolean;
}

class VoiceService {
  private isListening: boolean = false;
  private isWakeWordMode: boolean = true;
  private callback: ((command: string) => void) | null = null;
  private config: VoiceConfig;
  private stats: VoiceStats;
  private listeningTimeout: NodeJS.Timeout | null = null;
  private responseQueue: string[] = [];
  private isProcessingResponse: boolean = false;
  private availableVoices: SpeechSynthesisVoice[] = [];

  constructor() {
    this.config = {
      wakeWord: 'autonobot',
      responseDelay: 500,
      maxListeningTime: 30000,
      voiceSettings: {
        rate: 1,
        pitch: 1,
        volume: 1,
        voice: 'default'
      }
    };

    this.stats = {
      totalCommands: 0,
      successfulCommands: 0,
      failedCommands: 0,
      averageConfidence: 0,
      isListening: false,
      isSupported: this.checkSupport()
    };

    this.initializeVoices();
    this.initializeSpeechRecognition();
  }

  private checkSupport(): boolean {
    const hasRecognition = !!annyang && 'webkitSpeechRecognition' in window;
    const hasSynthesis = 'speechSynthesis' in window;

    if (!hasRecognition) {
      console.error('Speech recognition is not supported in this browser');
    }
    if (!hasSynthesis) {
      console.error('Speech synthesis is not supported in this browser');
    }

    return hasRecognition && hasSynthesis;
  }

  private initializeVoices(): void {
    if ('speechSynthesis' in window) {
      const updateVoices = () => {
        this.availableVoices = window.speechSynthesis.getVoices();
        console.log('Available voices:', this.availableVoices.length);
      };

      updateVoices();
      window.speechSynthesis.onvoiceschanged = updateVoices;
    }
  }

  private initializeSpeechRecognition(): void {
    if (!annyang) {
      console.error('Speech recognition is not supported');
      return;
    }

    // Configure annyang
    annyang.addCallback('result', (phrases: string[]) => {
      this.handleSpeechResult(phrases);
    });

    annyang.addCallback('error', (error: any) => {
      console.error('Speech recognition error:', error);
      this.stats.failedCommands++;
      this.handleListeningTimeout();
    });

    annyang.addCallback('start', () => {
      console.log('Speech recognition started');
      this.stats.isListening = true;
    });

    annyang.addCallback('end', () => {
      console.log('Speech recognition ended');
      this.stats.isListening = false;
    });
  }

  private handleSpeechResult(phrases: string[]): void {
    if (!phrases || phrases.length === 0) return;

    const command = phrases[0].toLowerCase().trim();
    const confidence = this.calculateConfidence(phrases);

    this.updateConfidenceStats(confidence);
    this.stats.totalCommands++;

    console.log('Speech result:', { command, confidence, isWakeWordMode: this.isWakeWordMode });

    if (this.isWakeWordMode) {
      if (command.includes(this.config.wakeWord)) {
        this.activateListening();
      }
    } else if (this.isListening && this.callback) {
      this.stats.successfulCommands++;
      this.callback(command);
      this.deactivateListening();
    }
  }

  private calculateConfidence(phrases: string[]): number {
    // Simple confidence calculation based on phrase consistency
    if (phrases.length === 1) return 1.0;

    const firstPhrase = phrases[0].toLowerCase();
    let matches = 0;

    for (let i = 1; i < Math.min(phrases.length, 5); i++) {
      const similarity = this.calculateSimilarity(firstPhrase, phrases[i].toLowerCase());
      if (similarity > 0.8) matches++;
    }

    return matches / Math.min(phrases.length - 1, 4);
  }

  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  private updateConfidenceStats(confidence: number): void {
    const total = this.stats.totalCommands;
    this.stats.averageConfidence = (this.stats.averageConfidence * total + confidence) / (total + 1);
  }

  public initialize(callback: (command: string) => void): void {
    if (!this.stats.isSupported) {
      console.error('Voice service cannot be initialized - not supported');
      return;
    }

    this.callback = callback;
    annyang.start({ autoRestart: true, continuous: false });
    console.log('Voice recognition initialized');
  }

  private activateListening(): void {
    this.isListening = true;
    this.isWakeWordMode = false;
    this.speak('How can I help you?');

    // Set timeout for listening
    this.listeningTimeout = setTimeout(() => {
      this.handleListeningTimeout();
    }, this.config.maxListeningTime);
  }

  private deactivateListening(): void {
    this.isListening = false;
    this.isWakeWordMode = true;

    if (this.listeningTimeout) {
      clearTimeout(this.listeningTimeout);
      this.listeningTimeout = null;
    }
  }

  private handleListeningTimeout(): void {
    if (this.isListening) {
      this.speak('I didn\'t hear anything. Say "autonobot" to wake me up again.');
      this.deactivateListening();
    }
  }

  public speak(text: string): void {
    this.responseQueue.push(text);
    this.processResponseQueue();
  }

  private async processResponseQueue(): Promise<void> {
    if (this.isProcessingResponse || this.responseQueue.length === 0) {
      return;
    }

    this.isProcessingResponse = true;

    while (this.responseQueue.length > 0) {
      const text = this.responseQueue.shift()!;
      await this.speakText(text);

      if (this.responseQueue.length > 0) {
        await this.delay(this.config.responseDelay);
      }
    }

    this.isProcessingResponse = false;
  }

  private speakText(text: string): Promise<void> {
    return new Promise((resolve) => {
      // Stop any current speech
      window.speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);

      // Apply voice settings
      utterance.rate = this.config.voiceSettings.rate;
      utterance.pitch = this.config.voiceSettings.pitch;
      utterance.volume = this.config.voiceSettings.volume;

      // Select voice if available
      if (this.config.voiceSettings.voice !== 'default' && this.availableVoices.length > 0) {
        const selectedVoice = this.availableVoices.find(voice =>
          voice.name.toLowerCase().includes(this.config.voiceSettings.voice.toLowerCase())
        );
        if (selectedVoice) {
          utterance.voice = selectedVoice;
        }
      }

      utterance.onend = () => resolve();
      utterance.onerror = (error) => {
        console.error('Speech synthesis error:', error);
        resolve();
      };

      window.speechSynthesis.speak(utterance);
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public toggleListening(): void {
    if (this.isWakeWordMode) {
      // Manual activation
      this.activateListening();
    } else {
      // Manual deactivation
      this.deactivateListening();
    }
  }

  public setWakeWord(wakeWord: string): void {
    this.config.wakeWord = wakeWord.toLowerCase();
    console.log('Wake word updated:', this.config.wakeWord);
  }

  public setVoiceSettings(settings: Partial<VoiceConfig['voiceSettings']>): void {
    this.config.voiceSettings = { ...this.config.voiceSettings, ...settings };
    console.log('Voice settings updated:', this.config.voiceSettings);
  }

  public getAvailableVoices(): SpeechSynthesisVoice[] {
    return this.availableVoices;
  }

  public getStats(): VoiceStats {
    return { ...this.stats };
  }

  public getConfig(): VoiceConfig {
    return { ...this.config };
  }

  public isCurrentlyListening(): boolean {
    return this.isListening && !this.isWakeWordMode;
  }

  public isInWakeWordMode(): boolean {
    return this.isWakeWordMode;
  }

  public clearResponseQueue(): void {
    this.responseQueue = [];
    window.speechSynthesis.cancel();
    this.isProcessingResponse = false;
  }

  public resetStats(): void {
    this.stats = {
      ...this.stats,
      totalCommands: 0,
      successfulCommands: 0,
      failedCommands: 0,
      averageConfidence: 0
    };
  }

  public cleanup(): void {
    if (this.listeningTimeout) {
      clearTimeout(this.listeningTimeout);
    }

    this.clearResponseQueue();

    if (annyang) {
      annyang.abort();
    }

    console.log('Voice service cleaned up');
  }
}

export const voiceService = new VoiceService();