import EventEmitter from 'events';
import { loggers } from '../services/loggerService.js';
import llmService from '../services/llmService.js';

/**
 * Response Agent - Especializado en procesamiento de información y generación de respuestas
 * Procesa información recopilada, genera respuestas coherentes, sintetiza resultados
 */
export class ResponseAgent extends EventEmitter {
  constructor(stateManager) {
    super();
    this.stateManager = stateManager;
    this.isActive = false;
    this.responseHistory = [];
    this.maxHistorySize = 100;
    
    loggers.main.info('Response Agent initialized');
  }

  /**
   * Genera una respuesta intermedia durante la ejecución de la tarea
   */
  async generateIntermediateResponse(task, stepResults) {
    try {
      loggers.main.info('Generating intermediate response', { 
        taskId: task.id, 
        stepCount: stepResults.length 
      });
      
      this.isActive = true;
      
      // Obtener contexto completo de la tarea
      const taskContext = await this.stateManager.getTaskContext(task.id);
      
      // Analizar progreso actual
      const progressAnalysis = this.analyzeProgress(stepResults, task);
      
      // Generar respuesta basada en el progreso
      const response = await this.generateProgressResponse(task, stepResults, progressAnalysis);
      
      // Guardar respuesta intermedia
      const responseRecord = {
        taskId: task.id,
        type: 'intermediate',
        timestamp: new Date().toISOString(),
        stepCount: stepResults.length,
        response,
        progressAnalysis
      };
      
      this.addToHistory(responseRecord);
      
      this.isActive = false;
      
      // Emitir evento
      this.emit('response:intermediate', {
        taskId: task.id,
        response,
        progress: progressAnalysis.progressPercentage
      });
      
      loggers.main.info('Intermediate response generated', { 
        taskId: task.id,
        progress: progressAnalysis.progressPercentage 
      });
      
      return response;
      
    } catch (error) {
      this.isActive = false;
      loggers.main.error('Error generating intermediate response', { 
        taskId: task.id, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Genera la respuesta final de la tarea
   */
  async generateFinalResponse(task, executionResults) {
    try {
      loggers.main.info('Generating final response', { taskId: task.id });
      
      this.isActive = true;
      
      // Obtener todo el contexto de la tarea
      const taskContext = await this.stateManager.getTaskContext(task.id);
      
      // Analizar resultados completos
      const resultsAnalysis = this.analyzeExecutionResults(executionResults, task);
      
      // Extraer información clave
      const extractedInfo = await this.extractKeyInformation(taskContext, executionResults);
      
      // Generar respuesta final comprehensiva
      const finalResponse = await this.generateComprehensiveResponse(
        task, 
        executionResults, 
        resultsAnalysis, 
        extractedInfo
      );
      
      // Guardar respuesta final
      const responseRecord = {
        taskId: task.id,
        type: 'final',
        timestamp: new Date().toISOString(),
        success: resultsAnalysis.overallSuccess,
        response: finalResponse,
        resultsAnalysis,
        extractedInfo
      };
      
      this.addToHistory(responseRecord);
      
      this.isActive = false;
      
      // Emitir evento
      this.emit('response:generated', {
        taskId: task.id,
        response: finalResponse,
        success: resultsAnalysis.overallSuccess
      });
      
      loggers.main.info('Final response generated', { 
        taskId: task.id,
        success: resultsAnalysis.overallSuccess 
      });
      
      return finalResponse;
      
    } catch (error) {
      this.isActive = false;
      loggers.main.error('Error generating final response', { 
        taskId: task.id, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Analiza el progreso de los pasos ejecutados
   */
  analyzeProgress(stepResults, task) {
    const totalSteps = stepResults.length;
    const successfulSteps = stepResults.filter(step => 
      step.result && (step.result.success !== false)
    ).length;
    
    const progressPercentage = totalSteps > 0 ? (successfulSteps / totalSteps) * 100 : 0;
    
    // Identificar el paso actual
    const currentStep = stepResults[stepResults.length - 1];
    
    // Determinar estado general
    let status = 'in_progress';
    if (progressPercentage === 100) {
      status = 'completed';
    } else if (progressPercentage < 50) {
      status = 'early_stage';
    } else {
      status = 'advanced_stage';
    }
    
    return {
      totalSteps,
      successfulSteps,
      progressPercentage,
      currentStep: currentStep?.step || 'unknown',
      status,
      lastStepSuccess: currentStep?.result?.success !== false
    };
  }

  /**
   * Analiza los resultados completos de la ejecución
   */
  analyzeExecutionResults(executionResults, task) {
    const stepResults = executionResults || [];
    const totalSteps = stepResults.length;
    
    // Contar éxitos y fallos
    const successfulSteps = stepResults.filter(step => 
      step.result && step.result.success !== false
    ).length;
    
    const failedSteps = stepResults.filter(step => 
      step.result && step.result.success === false
    ).length;
    
    // Determinar éxito general
    const overallSuccess = successfulSteps > 0 && (successfulSteps >= failedSteps);
    
    // Identificar información extraída
    const extractedData = this.collectExtractedData(stepResults);
    
    // Analizar tipos de pasos ejecutados
    const stepTypes = stepResults.map(step => step.step);
    const uniqueStepTypes = [...new Set(stepTypes)];
    
    return {
      totalSteps,
      successfulSteps,
      failedSteps,
      overallSuccess,
      successRate: totalSteps > 0 ? (successfulSteps / totalSteps) * 100 : 0,
      extractedData,
      stepTypes: uniqueStepTypes,
      completionTime: this.calculateExecutionTime(stepResults)
    };
  }

  /**
   * Extrae información clave del contexto y resultados
   */
  async extractKeyInformation(taskContext, executionResults) {
    try {
      const extractedInfo = {
        urls: [],
        content: [],
        interactions: [],
        searchResults: [],
        observations: []
      };
      
      // Extraer URLs visitadas
      if (taskContext.context?.navigationUrl) {
        extractedInfo.urls.push(taskContext.context.navigationUrl);
      }
      
      // Extraer resultados de búsqueda
      if (taskContext.context?.searchResults) {
        extractedInfo.searchResults = taskContext.context.searchResults.map(result => ({
          title: result.title,
          url: result.url,
          snippet: result.snippet
        }));
      }
      
      // Extraer observaciones
      if (taskContext.observations) {
        extractedInfo.observations = taskContext.observations.map(obs => ({
          timestamp: obs.timestamp,
          url: obs.url,
          elements: obs.interactiveElements?.length || 0,
          content: obs.visualAnalysis?.content?.structured?.mainText || null
        }));
      }
      
      // Extraer interacciones de los resultados de ejecución
      executionResults.forEach(step => {
        if (step.result?.steps) {
          extractedInfo.interactions.push(...step.result.steps);
        }
      });
      
      return extractedInfo;
      
    } catch (error) {
      loggers.main.error('Error extracting key information', { error: error.message });
      return {
        urls: [],
        content: [],
        interactions: [],
        searchResults: [],
        observations: []
      };
    }
  }

  /**
   * Genera respuesta de progreso
   */
  async generateProgressResponse(task, stepResults, progressAnalysis) {
    try {
      const prompt = this.buildProgressPrompt(task, stepResults, progressAnalysis);
      
      const response = await llmService.callLLM(prompt, null, {
        maxTokens: 512,
        temperature: 0.7
      });
      
      return this.formatProgressResponse(response, progressAnalysis);
      
    } catch (error) {
      loggers.main.error('Error generating progress response', { error: error.message });
      return this.generateFallbackProgressResponse(progressAnalysis);
    }
  }

  /**
   * Genera respuesta comprehensiva final
   */
  async generateComprehensiveResponse(task, executionResults, resultsAnalysis, extractedInfo) {
    try {
      const prompt = this.buildFinalResponsePrompt(task, executionResults, resultsAnalysis, extractedInfo);
      
      const response = await llmService.callLLM(prompt, null, {
        maxTokens: 1024,
        temperature: 0.6
      });
      
      return this.formatFinalResponse(response, resultsAnalysis, extractedInfo);
      
    } catch (error) {
      loggers.main.error('Error generating comprehensive response', { error: error.message });
      return this.generateFallbackFinalResponse(task, resultsAnalysis, extractedInfo);
    }
  }

  /**
   * Construye prompt para respuesta de progreso
   */
  buildProgressPrompt(task, stepResults, progressAnalysis) {
    const currentStep = progressAnalysis.currentStep;
    const progress = progressAnalysis.progressPercentage;
    
    return `Estoy ejecutando la tarea: "${task.instruction}"

Progreso actual:
- Paso actual: ${currentStep}
- Progreso: ${progress.toFixed(1)}%
- Pasos completados: ${progressAnalysis.successfulSteps}/${progressAnalysis.totalSteps}
- Estado: ${progressAnalysis.status}

Genera una respuesta breve (máximo 100 palabras) informando al usuario sobre el progreso actual de la tarea. Sé específico sobre lo que se está haciendo ahora y mantén un tono profesional pero amigable.`;
  }

  /**
   * Construye prompt para respuesta final
   */
  buildFinalResponsePrompt(task, executionResults, resultsAnalysis, extractedInfo) {
    const success = resultsAnalysis.overallSuccess ? 'exitosa' : 'con problemas';
    
    return `He completado la tarea: "${task.instruction}"

Resultados de la ejecución:
- Estado: ${success}
- Pasos ejecutados: ${resultsAnalysis.totalSteps}
- Tasa de éxito: ${resultsAnalysis.successRate.toFixed(1)}%
- URLs visitadas: ${extractedInfo.urls.join(', ') || 'Ninguna'}
- Resultados de búsqueda: ${extractedInfo.searchResults.length}
- Interacciones realizadas: ${extractedInfo.interactions.length}

Información extraída:
${this.formatExtractedInfo(extractedInfo)}

Genera una respuesta comprehensiva (máximo 300 palabras) que:
1. Resuma lo que se logró
2. Presente la información encontrada de manera clara
3. Indique si la tarea se completó exitosamente
4. Sugiera próximos pasos si es relevante

Mantén un tono profesional y estructurado.`;
  }

  /**
   * Formatea información extraída para el prompt
   */
  formatExtractedInfo(extractedInfo) {
    let formatted = '';
    
    if (extractedInfo.searchResults.length > 0) {
      formatted += `\nResultados de búsqueda encontrados:\n`;
      extractedInfo.searchResults.slice(0, 3).forEach((result, index) => {
        formatted += `${index + 1}. ${result.title} - ${result.url}\n`;
      });
    }
    
    if (extractedInfo.observations.length > 0) {
      const lastObs = extractedInfo.observations[extractedInfo.observations.length - 1];
      if (lastObs.content) {
        formatted += `\nContenido de la página: ${lastObs.content.substring(0, 200)}...\n`;
      }
    }
    
    return formatted || 'No se extrajo información específica.';
  }

  /**
   * Formatea respuesta de progreso
   */
  formatProgressResponse(response, progressAnalysis) {
    return {
      type: 'progress',
      message: response,
      progress: progressAnalysis.progressPercentage,
      currentStep: progressAnalysis.currentStep,
      status: progressAnalysis.status,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Formatea respuesta final
   */
  formatFinalResponse(response, resultsAnalysis, extractedInfo) {
    return {
      type: 'final',
      message: response,
      success: resultsAnalysis.overallSuccess,
      summary: {
        stepsExecuted: resultsAnalysis.totalSteps,
        successRate: resultsAnalysis.successRate,
        urlsVisited: extractedInfo.urls.length,
        interactionsPerformed: extractedInfo.interactions.length
      },
      extractedInfo,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Genera respuesta de progreso de respaldo
   */
  generateFallbackProgressResponse(progressAnalysis) {
    const messages = {
      early_stage: `Iniciando la tarea... Progreso: ${progressAnalysis.progressPercentage.toFixed(1)}%`,
      in_progress: `Ejecutando paso: ${progressAnalysis.currentStep}. Progreso: ${progressAnalysis.progressPercentage.toFixed(1)}%`,
      advanced_stage: `Casi terminando... Progreso: ${progressAnalysis.progressPercentage.toFixed(1)}%`,
      completed: `Tarea completada. Generando respuesta final...`
    };
    
    return this.formatProgressResponse(
      messages[progressAnalysis.status] || messages.in_progress,
      progressAnalysis
    );
  }

  /**
   * Genera respuesta final de respaldo
   */
  generateFallbackFinalResponse(task, resultsAnalysis, extractedInfo) {
    const successMessage = resultsAnalysis.overallSuccess 
      ? 'La tarea se completó exitosamente.'
      : 'La tarea se completó con algunos problemas.';
    
    const message = `${successMessage}

Resumen de la ejecución:
- Pasos ejecutados: ${resultsAnalysis.totalSteps}
- Tasa de éxito: ${resultsAnalysis.successRate.toFixed(1)}%
- URLs visitadas: ${extractedInfo.urls.length}
- Interacciones realizadas: ${extractedInfo.interactions.length}

${extractedInfo.searchResults.length > 0 ? 
  `Se encontraron ${extractedInfo.searchResults.length} resultados de búsqueda relevantes.` : 
  ''
}`;
    
    return this.formatFinalResponse(message, resultsAnalysis, extractedInfo);
  }

  // Métodos auxiliares
  collectExtractedData(stepResults) {
    const data = [];
    
    stepResults.forEach(step => {
      if (step.result?.extractedData) {
        data.push(step.result.extractedData);
      }
    });
    
    return data;
  }

  calculateExecutionTime(stepResults) {
    if (stepResults.length === 0) return 0;
    
    const firstStep = stepResults[0];
    const lastStep = stepResults[stepResults.length - 1];
    
    if (firstStep.timestamp && lastStep.timestamp) {
      return new Date(lastStep.timestamp) - new Date(firstStep.timestamp);
    }
    
    return 0;
  }

  addToHistory(record) {
    this.responseHistory.push(record);
    if (this.responseHistory.length > this.maxHistorySize) {
      this.responseHistory = this.responseHistory.slice(-this.maxHistorySize);
    }
  }

  getResponseHistory(limit = 20) {
    return this.responseHistory.slice(-limit);
  }

  getStatistics() {
    const history = this.responseHistory;
    const finalResponses = history.filter(r => r.type === 'final');
    
    return {
      totalResponses: history.length,
      finalResponses: finalResponses.length,
      successfulTasks: finalResponses.filter(r => r.success).length,
      averageStepsPerTask: finalResponses.length > 0 
        ? finalResponses.reduce((sum, r) => sum + (r.resultsAnalysis?.totalSteps || 0), 0) / finalResponses.length
        : 0
    };
  }

  getStatus() {
    return {
      isActive: this.isActive,
      responseCount: this.responseHistory.length,
      lastResponse: this.responseHistory[this.responseHistory.length - 1]?.timestamp || null
    };
  }

  async stop() {
    this.isActive = false;
    loggers.main.info('Response Agent stopped');
  }
}
