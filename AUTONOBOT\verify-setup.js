#!/usr/bin/env node

/**
 * AUTONOBOT Setup Verification Script
 * Quickly verifies that all enhanced features are properly configured
 */

const fs = require('fs');
const path = require('path');

function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

function checkEnvFile() {
  console.log('🔍 Checking environment configuration...');
  
  if (!checkFileExists('.env')) {
    console.log('❌ .env file not found');
    return false;
  }
  
  const envContent = fs.readFileSync('.env', 'utf8');
  const requiredKeys = [
    'GEMINI_API_KEY',
    'ANTHROPIC_API_KEY', 
    'OPENAI_API_KEY',
    'GOOGLE_SEARCH_API_KEY',
    'BING_SEARCH_API_KEY'
  ];
  
  let configuredKeys = 0;
  requiredKeys.forEach(key => {
    if (envContent.includes(key) && !envContent.includes(`${key}=your_`)) {
      configuredKeys++;
      console.log(`✅ ${key} configured`);
    } else {
      console.log(`⚠️  ${key} not configured or using placeholder`);
    }
  });
  
  console.log(`📊 ${configuredKeys}/${requiredKeys.length} API keys configured`);
  return configuredKeys > 0;
}

function checkBackendServices() {
  console.log('\n🔧 Checking backend services...');
  
  const services = [
    'backend/services/loggerService.js',
    'backend/services/configService.js', 
    'backend/services/searchService.js',
    'backend/services/llmService.js',
    'backend/services/browserService.js'
  ];
  
  let allExist = true;
  services.forEach(service => {
    if (checkFileExists(service)) {
      console.log(`✅ ${service}`);
    } else {
      console.log(`❌ ${service} missing`);
      allExist = false;
    }
  });
  
  return allExist;
}

function checkFrontendServices() {
  console.log('\n🎨 Checking frontend services...');
  
  const services = [
    'src/services/searchService.ts',
    'src/services/voiceService.ts',
    'src/services/aiService.ts',
    'src/services/apiService.ts'
  ];
  
  let allExist = true;
  services.forEach(service => {
    if (checkFileExists(service)) {
      console.log(`✅ ${service}`);
    } else {
      console.log(`❌ ${service} missing`);
      allExist = false;
    }
  });
  
  return allExist;
}

function checkRoutes() {
  console.log('\n🛣️  Checking API routes...');
  
  const routes = [
    'backend/routes/agentRoutes.js',
    'backend/routes/searchRoutes.js'
  ];
  
  let allExist = true;
  routes.forEach(route => {
    if (checkFileExists(route)) {
      console.log(`✅ ${route}`);
    } else {
      console.log(`❌ ${route} missing`);
      allExist = false;
    }
  });
  
  return allExist;
}

function checkPackageJson() {
  console.log('\n📦 Checking package dependencies...');
  
  if (!checkFileExists('package.json')) {
    console.log('❌ package.json not found');
    return false;
  }
  
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = [
    'express-rate-limit',
    'helmet', 
    'winston',
    'openai',
    '@anthropic-ai/sdk',
    '@google/generative-ai',
    'annyang',
    'axios'
  ];
  
  let installedDeps = 0;
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}`);
      installedDeps++;
    } else {
      console.log(`❌ ${dep} missing`);
    }
  });
  
  console.log(`📊 ${installedDeps}/${requiredDeps.length} required dependencies found`);
  return installedDeps === requiredDeps.length;
}

function checkDocumentation() {
  console.log('\n📚 Checking documentation...');
  
  const docs = [
    'README.md',
    'docs/setup.md',
    'config/default.json'
  ];
  
  let allExist = true;
  docs.forEach(doc => {
    if (checkFileExists(doc)) {
      console.log(`✅ ${doc}`);
    } else {
      console.log(`❌ ${doc} missing`);
      allExist = false;
    }
  });
  
  return allExist;
}

function generateReport() {
  console.log('\n🚀 AUTONOBOT Setup Verification Report');
  console.log('=====================================\n');
  
  const checks = [
    { name: 'Environment Configuration', fn: checkEnvFile },
    { name: 'Backend Services', fn: checkBackendServices },
    { name: 'Frontend Services', fn: checkFrontendServices },
    { name: 'API Routes', fn: checkRoutes },
    { name: 'Package Dependencies', fn: checkPackageJson },
    { name: 'Documentation', fn: checkDocumentation }
  ];
  
  let passed = 0;
  let failed = 0;
  
  checks.forEach(check => {
    try {
      const result = check.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`❌ ${check.name} check failed:`, error.message);
      failed++;
    }
  });
  
  console.log('\n📊 Summary:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 All checks passed! AUTONOBOT is ready to use.');
    console.log('\n🚀 Next steps:');
    console.log('1. Configure your API keys in the .env file');
    console.log('2. Run "npm start" to start the application');
    console.log('3. Open http://localhost:5173 in your browser');
    console.log('4. Say "autonobot" to test voice commands');
  } else {
    console.log('\n⚠️  Some checks failed. Please review the issues above.');
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Ensure all files are properly created');
    console.log('2. Run "npm install" to install dependencies');
    console.log('3. Check the setup documentation in docs/setup.md');
  }
  
  return failed === 0;
}

// Enhanced Features Summary
function showFeatureSummary() {
  console.log('\n🌟 AUTONOBOT Enhanced Features:');
  console.log('================================');
  console.log('🔍 Real-time Web Search (Google Custom Search + Bing)');
  console.log('🎤 Advanced Voice Recognition (Wake word detection)');
  console.log('🔊 Natural Voice Synthesis (Customizable settings)');
  console.log('🧠 Multi-Provider AI Integration (Gemini + Claude + OpenAI)');
  console.log('🌐 Autonomous Web Navigation (Playwright-powered)');
  console.log('📊 Comprehensive Logging & Monitoring');
  console.log('🛡️ Production-Ready Security Features');
  console.log('⚡ Performance Optimization & Caching');
  console.log('🔧 Modular & Extensible Architecture');
  console.log('📱 Responsive Web Interface');
}

// Main execution
if (require.main === module) {
  showFeatureSummary();
  const success = generateReport();
  process.exit(success ? 0 : 1);
}

module.exports = { generateReport };
