# AUTONOBOT Docker VNC Fix Script
# Diagnostica y soluciona problemas de conexión VNC

Write-Host "🖥️ AUTONOBOT Docker VNC Diagnostic & Fix Script" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Función para verificar si un comando existe
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Función para verificar puertos
function Test-Port($port) {
    $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
    return $connection.TcpTestSucceeded
}

Write-Host "`n🔍 STEP 1: Checking Docker Installation" -ForegroundColor Yellow

if (Test-Command docker) {
    Write-Host "✅ Docker command found" -ForegroundColor Green
    
    try {
        $dockerVersion = docker --version 2>$null
        Write-Host "✅ Docker version: $dockerVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ Docker command exists but not responding" -ForegroundColor Red
        Write-Host "   Please start Docker Desktop manually" -ForegroundColor Yellow
        exit 1
    }
} else {
    Write-Host "❌ Docker not found. Please install Docker Desktop" -ForegroundColor Red
    Write-Host "   Download from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n🔍 STEP 2: Checking Docker Daemon" -ForegroundColor Yellow

try {
    $dockerInfo = docker info 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker daemon is running" -ForegroundColor Green
    } else {
        throw "Docker daemon not responding"
    }
} catch {
    Write-Host "❌ Docker daemon is not running" -ForegroundColor Red
    Write-Host "   Starting Docker Desktop..." -ForegroundColor Yellow
    
    # Intentar iniciar Docker Desktop
    $dockerDesktopPath = "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    if (Test-Path $dockerDesktopPath) {
        Start-Process $dockerDesktopPath
        Write-Host "   Waiting for Docker Desktop to start..." -ForegroundColor Yellow
        
        # Esperar hasta 60 segundos
        $timeout = 60
        $elapsed = 0
        while ($elapsed -lt $timeout) {
            Start-Sleep 5
            $elapsed += 5
            try {
                docker info 2>$null | Out-Null
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✅ Docker Desktop started successfully" -ForegroundColor Green
                    break
                }
            } catch {
                # Continuar esperando
            }
            Write-Host "   Still waiting... ($elapsed/$timeout seconds)" -ForegroundColor Yellow
        }
        
        if ($elapsed -ge $timeout) {
            Write-Host "❌ Docker Desktop failed to start within $timeout seconds" -ForegroundColor Red
            Write-Host "   Please start Docker Desktop manually and run this script again" -ForegroundColor Yellow
            exit 1
        }
    } else {
        Write-Host "❌ Docker Desktop not found at expected location" -ForegroundColor Red
        Write-Host "   Please start Docker Desktop manually" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "`n🔍 STEP 3: Checking Required Ports" -ForegroundColor Yellow

$requiredPorts = @(5901, 6901, 8080)
$portsOk = $true

foreach ($port in $requiredPorts) {
    if (Test-Port $port) {
        Write-Host "❌ Port $port is already in use" -ForegroundColor Red
        $portsOk = $false
        
        # Mostrar qué proceso está usando el puerto
        try {
            $process = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue | Select-Object -First 1
            if ($process) {
                $processInfo = Get-Process -Id $process.OwningProcess -ErrorAction SilentlyContinue
                Write-Host "   Used by: $($processInfo.ProcessName) (PID: $($process.OwningProcess))" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "   Could not determine which process is using the port" -ForegroundColor Yellow
        }
    } else {
        Write-Host "✅ Port $port is available" -ForegroundColor Green
    }
}

if (-not $portsOk) {
    Write-Host "`n⚠️ Some required ports are in use. You may need to:" -ForegroundColor Yellow
    Write-Host "   1. Stop other VNC servers" -ForegroundColor Yellow
    Write-Host "   2. Stop other Docker containers using these ports" -ForegroundColor Yellow
    Write-Host "   3. Restart your computer if necessary" -ForegroundColor Yellow
    
    $continue = Read-Host "`nDo you want to continue anyway? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
}

Write-Host "`n🔍 STEP 4: Checking Existing Containers" -ForegroundColor Yellow

try {
    $containers = docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>$null
    if ($containers) {
        Write-Host "📋 Existing containers:" -ForegroundColor Cyan
        Write-Host $containers
        
        # Detener contenedores relacionados con AUTONOBOT
        $autobotContainers = docker ps -a --filter "name=autonobot" --format "{{.Names}}" 2>$null
        if ($autobotContainers) {
            Write-Host "`n🛑 Stopping existing AUTONOBOT containers..." -ForegroundColor Yellow
            foreach ($container in $autobotContainers) {
                docker stop $container 2>$null
                docker rm $container 2>$null
                Write-Host "   Removed: $container" -ForegroundColor Green
            }
        }
    } else {
        Write-Host "✅ No existing containers found" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ Could not check existing containers" -ForegroundColor Yellow
}

Write-Host "`n🚀 STEP 5: Starting AUTONOBOT Remote Desktop" -ForegroundColor Yellow

# Crear directorios necesarios
$directories = @("screenshots", "downloads", "logs")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✅ Created directory: $dir" -ForegroundColor Green
    }
}

Write-Host "🐳 Starting Docker container with VNC desktop..." -ForegroundColor Cyan

try {
    # Usar docker-compose para iniciar el servicio de prueba
    docker-compose -f docker-compose.test.yml up -d 2>$null
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker container started successfully" -ForegroundColor Green
        
        Write-Host "`n⏳ Waiting for VNC server to be ready..." -ForegroundColor Yellow
        
        # Esperar hasta que el servicio VNC esté listo
        $timeout = 120
        $elapsed = 0
        $vncReady = $false
        
        while ($elapsed -lt $timeout -and -not $vncReady) {
            Start-Sleep 5
            $elapsed += 5
            
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:6901" -TimeoutSec 5 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    $vncReady = $true
                    Write-Host "✅ VNC web interface is ready!" -ForegroundColor Green
                }
            } catch {
                Write-Host "   Still waiting for VNC... ($elapsed/$timeout seconds)" -ForegroundColor Yellow
            }
        }
        
        if (-not $vncReady) {
            Write-Host "⚠️ VNC interface not responding after $timeout seconds" -ForegroundColor Yellow
            Write-Host "   Container may still be starting up..." -ForegroundColor Yellow
        }
        
    } else {
        throw "Docker compose failed"
    }
} catch {
    Write-Host "❌ Failed to start Docker container" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
    
    # Mostrar logs del contenedor para diagnóstico
    Write-Host "`n📋 Container logs:" -ForegroundColor Cyan
    docker-compose -f docker-compose.test.yml logs --tail=20
    exit 1
}

Write-Host "`n🎉 AUTONOBOT Remote Desktop Setup Complete!" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

Write-Host "`n🌐 Access URLs:" -ForegroundColor Cyan
Write-Host "   VNC Web Interface: http://localhost:6901" -ForegroundColor White
Write-Host "   VNC Direct Client: localhost:5901 (password: autonobot)" -ForegroundColor White
Write-Host "   Frontend App: http://localhost:5173" -ForegroundColor White

Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "   1. Open http://localhost:6901 in your browser" -ForegroundColor White
Write-Host "   2. Click 'Connect' and enter password: autonobot" -ForegroundColor White
Write-Host "   3. You should see an Ubuntu desktop with Firefox" -ForegroundColor White
Write-Host "   4. Go back to your AUTONOBOT frontend and try the Remote Desktop mode" -ForegroundColor White

Write-Host "`n🔧 Troubleshooting:" -ForegroundColor Cyan
Write-Host "   - If VNC doesn't load: docker-compose -f docker-compose.test.yml logs" -ForegroundColor White
Write-Host "   - To restart: docker-compose -f docker-compose.test.yml restart" -ForegroundColor White
Write-Host "   - To stop: docker-compose -f docker-compose.test.yml down" -ForegroundColor White

Write-Host "`n✨ Ready to test AUTONOBOT Remote Desktop!" -ForegroundColor Green
