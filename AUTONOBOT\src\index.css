@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --neon-primary: 88 208 255;
    --neon-secondary: 147 51 255;
  }
}

.bg-grid {
  background-image: radial-gradient(rgb(var(--neon-primary) / 0.1) 1px, transparent 1px);
  background-size: 40px 40px;
}

.glass-panel {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.neon-border {
  box-shadow: 0 0 15px rgb(var(--neon-primary) / 0.5);
  border: 1px solid rgb(var(--neon-primary) / 0.3);
}

.neon-text {
  text-shadow: 0 0 10px rgb(var(--neon-primary) / 0.5);
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.console-text {
  color: #00ff9d;
  text-shadow: 0 0 5px rgba(0, 255, 157, 0.5);
}