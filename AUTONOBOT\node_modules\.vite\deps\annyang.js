import {
  __commonJS
} from "./chunk-HKJ2B2AA.js";

// node_modules/annyang/dist/annyang.min.js
var require_annyang_min = __commonJS({
  "node_modules/annyang/dist/annyang.min.js"(exports, module) {
    var _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e) {
      return typeof e;
    } : function(e) {
      return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e;
    };
    !function(e, n) {
      "function" == typeof define && define.amd ? define([], function() {
        return e.annyang = n(e);
      }) : "object" === ("undefined" == typeof module ? "undefined" : _typeof(module)) && module.exports ? module.exports = n(e) : e.annyang = n(e);
    }("undefined" != typeof window ? window : void 0, function(r, i) {
      var t, o = r.SpeechRecognition || r.webkitSpeechRecognition || r.mozSpeechRecognition || r.msSpeechRecognition || r.oSpeechRecognition;
      if (!o) return null;
      var a, c, s = [], u = { start: [], error: [], end: [], soundstart: [], result: [], resultMatch: [], resultNoMatch: [], errorNetwork: [], errorPermissionBlocked: [], errorPermissionDenied: [] }, f = 0, l = 0, d = false, p = "font-weight: bold; color: #00f;", g = false, m = false, h = /\s*\((.*?)\)\s*/g, y = /(\(\?:[^)]+\))\?/g, b = /(\(\?)?:\w+/g, v = /\*\w+/g, w = /[\-{}\[\]+?.,\\\^$|#]/g, S = function(e2) {
        for (var n = arguments.length, t2 = Array(1 < n ? n - 1 : 0), o2 = 1; o2 < n; o2++) t2[o2 - 1] = arguments[o2];
        e2.forEach(function(e3) {
          e3.callback.apply(e3.context, t2);
        });
      }, e = function() {
        return a !== i;
      }, k = function(e2, n) {
        -1 !== e2.indexOf("%c") || n ? console.log(e2, n || p) : console.log(e2);
      }, x = function() {
        e() || t.init({}, false);
      }, R = function(e2, n, t2) {
        s.push({ command: e2, callback: n, originalPhrase: t2 }), d && k("Command successfully loaded: %c" + t2, p);
      }, P = function(e2) {
        var n;
        S(u.result, e2);
        for (var t2 = 0; t2 < e2.length; t2++) {
          n = e2[t2].trim(), d && k("Speech recognized: %c" + n, p);
          for (var o2 = 0, r2 = s.length; o2 < r2; o2++) {
            var i2 = s[o2], a2 = i2.command.exec(n);
            if (a2) {
              var c2 = a2.slice(1);
              return d && (k("command matched: %c" + i2.originalPhrase, p), c2.length && k("with parameters", c2)), i2.callback.apply(this, c2), void S(u.resultMatch, n, i2.originalPhrase, e2);
            }
          }
        }
        S(u.resultNoMatch, e2);
      };
      return t = { init: function(e2) {
        var n = !(1 < arguments.length && arguments[1] !== i) || arguments[1];
        a && a.abort && a.abort(), (a = new o()).maxAlternatives = 5, a.continuous = "http:" === r.location.protocol, a.lang = "en-US", a.onstart = function() {
          m = true, S(u.start);
        }, a.onsoundstart = function() {
          S(u.soundstart);
        }, a.onerror = function(e3) {
          switch (S(u.error, e3), e3.error) {
            case "network":
              S(u.errorNetwork, e3);
              break;
            case "not-allowed":
            case "service-not-allowed":
              c = false, (/* @__PURE__ */ new Date()).getTime() - f < 200 ? S(u.errorPermissionBlocked, e3) : S(u.errorPermissionDenied, e3);
          }
        }, a.onend = function() {
          if (m = false, S(u.end), c) {
            var e3 = (/* @__PURE__ */ new Date()).getTime() - f;
            (l += 1) % 10 == 0 && d && k("Speech Recognition is repeatedly stopping and starting. See http://is.gd/annyang_restarts for tips."), e3 < 1e3 ? setTimeout(function() {
              t.start({ paused: g });
            }, 1e3 - e3) : t.start({ paused: g });
          }
        }, a.onresult = function(e3) {
          if (g) return d && k("Speech heard, but annyang is paused"), false;
          for (var n2 = e3.results[e3.resultIndex], t2 = [], o2 = 0; o2 < n2.length; o2++) t2[o2] = n2[o2].transcript;
          P(t2);
        }, n && (s = []), e2.length && this.addCommands(e2);
      }, start: function(e2) {
        x(), g = (e2 = e2 || {}).paused !== i && !!e2.paused, c = e2.autoRestart === i || !!e2.autoRestart, e2.continuous !== i && (a.continuous = !!e2.continuous), f = (/* @__PURE__ */ new Date()).getTime();
        try {
          a.start();
        } catch (e3) {
          d && k(e3.message);
        }
      }, abort: function() {
        c = false, l = 0, e() && a.abort();
      }, pause: function() {
        g = true;
      }, resume: function() {
        t.start();
      }, debug: function() {
        var e2 = !(0 < arguments.length && arguments[0] !== i) || arguments[0];
        d = !!e2;
      }, setLanguage: function(e2) {
        x(), a.lang = e2;
      }, addCommands: function(e2) {
        var n, t2;
        for (var o2 in x(), e2) if (e2.hasOwnProperty(o2)) if ("function" == typeof (n = r[e2[o2]] || e2[o2])) R((t2 = (t2 = o2).replace(w, "\\$&").replace(h, "(?:$1)?").replace(b, function(e3, n2) {
          return n2 ? e3 : "([^\\s]+)";
        }).replace(v, "(.*?)").replace(y, "\\s*$1?\\s*"), new RegExp("^" + t2 + "$", "i")), n, o2);
        else {
          if (!("object" === (void 0 === n ? "undefined" : _typeof(n)) && n.regexp instanceof RegExp)) {
            d && k("Can not register command: %c" + o2, p);
            continue;
          }
          R(new RegExp(n.regexp.source, "i"), n.callback, o2);
        }
      }, removeCommands: function(t2) {
        t2 === i ? s = [] : (t2 = Array.isArray(t2) ? t2 : [t2], s = s.filter(function(e2) {
          for (var n = 0; n < t2.length; n++) if (t2[n] === e2.originalPhrase) return false;
          return true;
        }));
      }, addCallback: function(e2, n, t2) {
        var o2 = r[n] || n;
        "function" == typeof o2 && u[e2] !== i && u[e2].push({ callback: o2, context: t2 || this });
      }, removeCallback: function(e2, n) {
        var t2 = function(e3) {
          return e3.callback !== n;
        };
        for (var o2 in u) u.hasOwnProperty(o2) && (e2 !== i && e2 !== o2 || (u[o2] = n === i ? [] : u[o2].filter(t2)));
      }, isListening: function() {
        return m && !g;
      }, getSpeechRecognizer: function() {
        return a;
      }, trigger: function(e2) {
        t.isListening() ? (Array.isArray(e2) || (e2 = [e2]), P(e2)) : d && k(m ? "Speech heard, but annyang is paused" : "Cannot trigger while annyang is aborted");
      } };
    });
  }
});
export default require_annyang_min();
/*! Bundled license information:

annyang/dist/annyang.min.js:
  (*! annyang *)
  (*! version : 2.6.1 *)
  (*! author  : Tal Ater @TalAter *)
  (*! license : MIT *)
  (*! https://www.TalAter.com/annyang/ *)
*/
//# sourceMappingURL=annyang.js.map
