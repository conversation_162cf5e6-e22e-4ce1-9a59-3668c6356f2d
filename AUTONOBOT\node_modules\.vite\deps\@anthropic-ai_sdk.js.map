{"version": 3, "sources": ["../../@anthropic-ai/sdk/src/version.ts", "../../@anthropic-ai/sdk/src/_shims/registry.ts", "../../@anthropic-ai/sdk/src/_shims/MultipartBody.ts", "../../@anthropic-ai/sdk/src/_shims/web-runtime.ts", "../../@anthropic-ai/sdk/_shims/index.mjs", "../../@anthropic-ai/sdk/src/error.ts", "../../@anthropic-ai/sdk/src/streaming.ts", "../../@anthropic-ai/sdk/src/uploads.ts", "../../@anthropic-ai/sdk/src/core.ts", "../../@anthropic-ai/sdk/src/resource.ts", "../../@anthropic-ai/sdk/src/resources/completions.ts", "../../@anthropic-ai/sdk/src/lib/MessageStream.ts", "../../@anthropic-ai/sdk/src/resources/messages.ts", "../../@anthropic-ai/sdk/src/index.ts"], "sourcesContent": ["export const VERSION = '0.17.2'; // x-release-please-version\n", "/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nimport { type RequestOptions } from '../core';\n\nexport interface Shims {\n  kind: string;\n  fetch: any;\n  Request: any;\n  Response: any;\n  Headers: any;\n  FormData: any;\n  Blob: any;\n  File: any;\n  ReadableStream: any;\n  getMultipartRequestOptions: <T = Record<string, unknown>>(\n    form: Shims['FormData'],\n    opts: RequestOptions<T>,\n  ) => Promise<RequestOptions<T>>;\n  getDefaultAgent: (url: string) => any;\n  fileFromPath:\n    | ((path: string, filename?: string, options?: {}) => Promise<Shims['File']>)\n    | ((path: string, options?: {}) => Promise<Shims['File']>);\n  isFsReadStream: (value: any) => boolean;\n}\n\nexport let auto = false;\nexport let kind: Shims['kind'] | undefined = undefined;\nexport let fetch: Shims['fetch'] | undefined = undefined;\nexport let Request: Shims['Request'] | undefined = undefined;\nexport let Response: Shims['Response'] | undefined = undefined;\nexport let Headers: Shims['Headers'] | undefined = undefined;\nexport let FormData: Shims['FormData'] | undefined = undefined;\nexport let Blob: Shims['Blob'] | undefined = undefined;\nexport let File: Shims['File'] | undefined = undefined;\nexport let ReadableStream: Shims['ReadableStream'] | undefined = undefined;\nexport let getMultipartRequestOptions: Shims['getMultipartRequestOptions'] | undefined = undefined;\nexport let getDefaultAgent: Shims['getDefaultAgent'] | undefined = undefined;\nexport let fileFromPath: Shims['fileFromPath'] | undefined = undefined;\nexport let isFsReadStream: Shims['isFsReadStream'] | undefined = undefined;\n\nexport function setShims(shims: Shims, options: { auto: boolean } = { auto: false }) {\n  if (auto) {\n    throw new Error(\n      `you must \\`import '@anthropic-ai/sdk/shims/${shims.kind}'\\` before importing anything else from @anthropic-ai/sdk`,\n    );\n  }\n  if (kind) {\n    throw new Error(\n      `can't \\`import '@anthropic-ai/sdk/shims/${shims.kind}'\\` after \\`import '@anthropic-ai/sdk/shims/${kind}'\\``,\n    );\n  }\n  auto = options.auto;\n  kind = shims.kind;\n  fetch = shims.fetch;\n  Request = shims.Request;\n  Response = shims.Response;\n  Headers = shims.Headers;\n  FormData = shims.FormData;\n  Blob = shims.Blob;\n  File = shims.File;\n  ReadableStream = shims.ReadableStream;\n  getMultipartRequestOptions = shims.getMultipartRequestOptions;\n  getDefaultAgent = shims.getDefaultAgent;\n  fileFromPath = shims.fileFromPath;\n  isFsReadStream = shims.isFsReadStream;\n}\n", "/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nexport class MultipartBody {\n  constructor(public body: any) {}\n  get [Symbol.toStringTag](): string {\n    return 'MultipartBody';\n  }\n}\n", "/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nimport { MultipartBody } from './MultipartBody';\nimport { type RequestOptions } from '../core';\nimport { type Shi<PERSON> } from './registry';\n\nexport function getRuntime({ manuallyImported }: { manuallyImported?: boolean } = {}): Shims {\n  const recommendation =\n    manuallyImported ?\n      `You may need to use polyfills`\n    : `Add one of these imports before your first \\`import … from '@anthropic-ai/sdk'\\`:\n- \\`import '@anthropic-ai/sdk/shims/node'\\` (if you're running on Node)\n- \\`import '@anthropic-ai/sdk/shims/web'\\` (otherwise)\n`;\n\n  let _fetch, _Request, _Response, _Headers;\n  try {\n    // @ts-ignore\n    _fetch = fetch;\n    // @ts-ignore\n    _Request = Request;\n    // @ts-ignore\n    _Response = Response;\n    // @ts-ignore\n    _Headers = Headers;\n  } catch (error) {\n    throw new Error(\n      `this environment is missing the following Web Fetch API type: ${\n        (error as any).message\n      }. ${recommendation}`,\n    );\n  }\n\n  return {\n    kind: 'web',\n    fetch: _fetch,\n    Request: _Request,\n    Response: _Response,\n    Headers: _Headers,\n    FormData:\n      // @ts-ignore\n      typeof FormData !== 'undefined' ? FormData : (\n        class FormData {\n          // @ts-ignore\n          constructor() {\n            throw new Error(\n              `file uploads aren't supported in this environment yet as 'FormData' is undefined. ${recommendation}`,\n            );\n          }\n        }\n      ),\n    Blob:\n      typeof Blob !== 'undefined' ? Blob : (\n        class Blob {\n          constructor() {\n            throw new Error(\n              `file uploads aren't supported in this environment yet as 'Blob' is undefined. ${recommendation}`,\n            );\n          }\n        }\n      ),\n    File:\n      // @ts-ignore\n      typeof File !== 'undefined' ? File : (\n        class File {\n          // @ts-ignore\n          constructor() {\n            throw new Error(\n              `file uploads aren't supported in this environment yet as 'File' is undefined. ${recommendation}`,\n            );\n          }\n        }\n      ),\n    ReadableStream:\n      // @ts-ignore\n      typeof ReadableStream !== 'undefined' ? ReadableStream : (\n        class ReadableStream {\n          // @ts-ignore\n          constructor() {\n            throw new Error(\n              `streaming isn't supported in this environment yet as 'ReadableStream' is undefined. ${recommendation}`,\n            );\n          }\n        }\n      ),\n    getMultipartRequestOptions: async <T = Record<string, unknown>>(\n      // @ts-ignore\n      form: FormData,\n      opts: RequestOptions<T>,\n    ): Promise<RequestOptions<T>> => ({\n      ...opts,\n      body: new MultipartBody(form) as any,\n    }),\n    getDefaultAgent: (url: string) => undefined,\n    fileFromPath: () => {\n      throw new Error(\n        'The `fileFromPath` function is only supported in Node. See the README for more details: https://www.github.com/anthropics/anthropic-sdk-typescript#file-uploads',\n      );\n    },\n    isFsReadStream: (value: any) => false,\n  };\n}\n", "/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nimport * as shims from './registry.mjs';\nimport * as auto from '@anthropic-ai/sdk/_shims/auto/runtime';\nif (!shims.kind) shims.setShims(auto.getRuntime(), { auto: true });\nexport * from './registry.mjs';\n", "// File generated from our OpenAPI spec by Stainless.\n\nimport { castToError, Headers } from './core';\n\nexport class AnthropicError extends Error {}\n\nexport class APIError extends AnthropicError {\n  readonly status: number | undefined;\n  readonly headers: Headers | undefined;\n  readonly error: Object | undefined;\n\n  constructor(\n    status: number | undefined,\n    error: Object | undefined,\n    message: string | undefined,\n    headers: Headers | undefined,\n  ) {\n    super(`${APIError.makeMessage(status, error, message)}`);\n    this.status = status;\n    this.headers = headers;\n    this.error = error;\n  }\n\n  private static makeMessage(status: number | undefined, error: any, message: string | undefined) {\n    const msg =\n      error?.message ?\n        typeof error.message === 'string' ?\n          error.message\n        : JSON.stringify(error.message)\n      : error ? JSON.stringify(error)\n      : message;\n\n    if (status && msg) {\n      return `${status} ${msg}`;\n    }\n    if (status) {\n      return `${status} status code (no body)`;\n    }\n    if (msg) {\n      return msg;\n    }\n    return '(no status code or body)';\n  }\n\n  static generate(\n    status: number | undefined,\n    errorResponse: Object | undefined,\n    message: string | undefined,\n    headers: Headers | undefined,\n  ) {\n    if (!status) {\n      return new APIConnectionError({ cause: castToError(errorResponse) });\n    }\n\n    const error = errorResponse as Record<string, any>;\n\n    if (status === 400) {\n      return new BadRequestError(status, error, message, headers);\n    }\n\n    if (status === 401) {\n      return new AuthenticationError(status, error, message, headers);\n    }\n\n    if (status === 403) {\n      return new PermissionDeniedError(status, error, message, headers);\n    }\n\n    if (status === 404) {\n      return new NotFoundError(status, error, message, headers);\n    }\n\n    if (status === 409) {\n      return new ConflictError(status, error, message, headers);\n    }\n\n    if (status === 422) {\n      return new UnprocessableEntityError(status, error, message, headers);\n    }\n\n    if (status === 429) {\n      return new RateLimitError(status, error, message, headers);\n    }\n\n    if (status >= 500) {\n      return new InternalServerError(status, error, message, headers);\n    }\n\n    return new APIError(status, error, message, headers);\n  }\n}\n\nexport class APIUserAbortError extends APIError {\n  override readonly status: undefined = undefined;\n\n  constructor({ message }: { message?: string } = {}) {\n    super(undefined, undefined, message || 'Request was aborted.', undefined);\n  }\n}\n\nexport class APIConnectionError extends APIError {\n  override readonly status: undefined = undefined;\n\n  constructor({ message, cause }: { message?: string; cause?: Error | undefined }) {\n    super(undefined, undefined, message || 'Connection error.', undefined);\n    // in some environments the 'cause' property is already declared\n    // @ts-ignore\n    if (cause) this.cause = cause;\n  }\n}\n\nexport class APIConnectionTimeoutError extends APIConnectionError {\n  constructor({ message }: { message?: string } = {}) {\n    super({ message: message ?? 'Request timed out.' });\n  }\n}\n\nexport class BadRequestError extends APIError {\n  override readonly status: 400 = 400;\n}\n\nexport class AuthenticationError extends APIError {\n  override readonly status: 401 = 401;\n}\n\nexport class PermissionDeniedError extends APIError {\n  override readonly status: 403 = 403;\n}\n\nexport class NotFoundError extends APIError {\n  override readonly status: 404 = 404;\n}\n\nexport class ConflictError extends APIError {\n  override readonly status: 409 = 409;\n}\n\nexport class UnprocessableEntityError extends APIError {\n  override readonly status: 422 = 422;\n}\n\nexport class RateLimitError extends APIError {\n  override readonly status: 429 = 429;\n}\n\nexport class InternalServerError extends APIError {}\n", "import { ReadableStream, type Response } from './_shims/index';\nimport { AnthropicError } from './error';\n\nimport { safeJSON, createResponseHeaders } from \"./core\";\nimport { APIError } from \"./error\";\n\ntype Bytes = string | ArrayBuffer | Uint8Array | Buffer | null | undefined;\n\nexport type ServerSentEvent = {\n  event: string | null;\n  data: string;\n  raw: string[];\n};\n\nexport class Stream<Item> implements AsyncIterable<Item> {\n  controller: AbortController;\n\n  constructor(\n    private iterator: () => AsyncIterator<Item>,\n    controller: AbortController,\n  ) {\n    this.controller = controller;\n  }\n\n  static fromSSEResponse<Item>(response: Response, controller: AbortController) {\n    let consumed = false;\n    const decoder = new SSEDecoder();\n\n    async function* iterMessages(): AsyncGenerator<ServerSentEvent, void, unknown> {\n      if (!response.body) {\n        controller.abort();\n        throw new AnthropicError(`Attempted to iterate over a response with no body`);\n      }\n\n      const lineDecoder = new LineDecoder();\n\n      const iter = readableStreamAsyncIterable<Bytes>(response.body);\n      for await (const chunk of iter) {\n        for (const line of lineDecoder.decode(chunk)) {\n          const sse = decoder.decode(line);\n          if (sse) yield sse;\n        }\n      }\n\n      for (const line of lineDecoder.flush()) {\n        const sse = decoder.decode(line);\n        if (sse) yield sse;\n      }\n    }\n\n    async function* iterator(): AsyncIterator<Item, any, undefined> {\n      if (consumed) {\n        throw new Error('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n      }\n      consumed = true;\n      let done = false;\n      try {\n        for await (const sse of iterMessages()) {\n          if (sse.event === 'completion') {\n            try {\n              yield JSON.parse(sse.data);\n            } catch (e) {\n              console.error(`Could not parse message into JSON:`, sse.data);\n              console.error(`From chunk:`, sse.raw);\n              throw e;\n            }\n          }\n\n          if (\n            sse.event === 'message_start' ||\n            sse.event === 'message_delta' ||\n            sse.event === 'message_stop' ||\n            sse.event === 'content_block_start' ||\n            sse.event === 'content_block_delta' ||\n            sse.event === 'content_block_stop'\n          ) {\n            try {\n              yield JSON.parse(sse.data);\n            } catch (e) {\n              console.error(`Could not parse message into JSON:`, sse.data);\n              console.error(`From chunk:`, sse.raw);\n              throw e;\n            }\n          }\n\n          if (sse.event === 'ping') {\n            continue;\n          }\n\n          if (sse.event === 'error') {\n            const errText = sse.data;\n            const errJSON = safeJSON(errText);\n            const errMessage = errJSON ? undefined : errText;\n\n            throw APIError.generate(undefined, errJSON, errMessage, createResponseHeaders(response.headers));\n          }\n        }\n        done = true;\n      } catch (e) {\n        // If the user calls `stream.controller.abort()`, we should exit without throwing.\n        if (e instanceof Error && e.name === 'AbortError') return;\n        throw e;\n      } finally {\n        // If the user `break`s, abort the ongoing request.\n        if (!done) controller.abort();\n      }\n    }\n\n    return new Stream(iterator, controller);\n  }\n\n  /**\n   * Generates a Stream from a newline-separated ReadableStream\n   * where each item is a JSON value.\n   */\n  static fromReadableStream<Item>(readableStream: ReadableStream, controller: AbortController) {\n    let consumed = false;\n\n    async function* iterLines(): AsyncGenerator<string, void, unknown> {\n      const lineDecoder = new LineDecoder();\n\n      const iter = readableStreamAsyncIterable<Bytes>(readableStream);\n      for await (const chunk of iter) {\n        for (const line of lineDecoder.decode(chunk)) {\n          yield line;\n        }\n      }\n\n      for (const line of lineDecoder.flush()) {\n        yield line;\n      }\n    }\n\n    async function* iterator(): AsyncIterator<Item, any, undefined> {\n      if (consumed) {\n        throw new Error('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n      }\n      consumed = true;\n      let done = false;\n      try {\n        for await (const line of iterLines()) {\n          if (done) continue;\n          if (line) yield JSON.parse(line);\n        }\n        done = true;\n      } catch (e) {\n        // If the user calls `stream.controller.abort()`, we should exit without throwing.\n        if (e instanceof Error && e.name === 'AbortError') return;\n        throw e;\n      } finally {\n        // If the user `break`s, abort the ongoing request.\n        if (!done) controller.abort();\n      }\n    }\n\n    return new Stream(iterator, controller);\n  }\n\n  [Symbol.asyncIterator](): AsyncIterator<Item> {\n    return this.iterator();\n  }\n\n  /**\n   * Splits the stream into two streams which can be\n   * independently read from at different speeds.\n   */\n  tee(): [Stream<Item>, Stream<Item>] {\n    const left: Array<Promise<IteratorResult<Item>>> = [];\n    const right: Array<Promise<IteratorResult<Item>>> = [];\n    const iterator = this.iterator();\n\n    const teeIterator = (queue: Array<Promise<IteratorResult<Item>>>): AsyncIterator<Item> => {\n      return {\n        next: () => {\n          if (queue.length === 0) {\n            const result = iterator.next();\n            left.push(result);\n            right.push(result);\n          }\n          return queue.shift()!;\n        },\n      };\n    };\n\n    return [\n      new Stream(() => teeIterator(left), this.controller),\n      new Stream(() => teeIterator(right), this.controller),\n    ];\n  }\n\n  /**\n   * Converts this stream to a newline-separated ReadableStream of\n   * JSON stringified values in the stream\n   * which can be turned back into a Stream with `Stream.fromReadableStream()`.\n   */\n  toReadableStream(): ReadableStream {\n    const self = this;\n    let iter: AsyncIterator<Item>;\n    const encoder = new TextEncoder();\n\n    return new ReadableStream({\n      async start() {\n        iter = self[Symbol.asyncIterator]();\n      },\n      async pull(ctrl) {\n        try {\n          const { value, done } = await iter.next();\n          if (done) return ctrl.close();\n\n          const bytes = encoder.encode(JSON.stringify(value) + '\\n');\n\n          ctrl.enqueue(bytes);\n        } catch (err) {\n          ctrl.error(err);\n        }\n      },\n      async cancel() {\n        await iter.return?.();\n      },\n    });\n  }\n}\n\nclass SSEDecoder {\n  private data: string[];\n  private event: string | null;\n  private chunks: string[];\n\n  constructor() {\n    this.event = null;\n    this.data = [];\n    this.chunks = [];\n  }\n\n  decode(line: string) {\n    if (line.endsWith('\\r')) {\n      line = line.substring(0, line.length - 1);\n    }\n\n    if (!line) {\n      // empty line and we didn't previously encounter any messages\n      if (!this.event && !this.data.length) return null;\n\n      const sse: ServerSentEvent = {\n        event: this.event,\n        data: this.data.join('\\n'),\n        raw: this.chunks,\n      };\n\n      this.event = null;\n      this.data = [];\n      this.chunks = [];\n\n      return sse;\n    }\n\n    this.chunks.push(line);\n\n    if (line.startsWith(':')) {\n      return null;\n    }\n\n    let [fieldname, _, value] = partition(line, ':');\n\n    if (value.startsWith(' ')) {\n      value = value.substring(1);\n    }\n\n    if (fieldname === 'event') {\n      this.event = value;\n    } else if (fieldname === 'data') {\n      this.data.push(value);\n    }\n\n    return null;\n  }\n}\n\n/**\n * A re-implementation of httpx's `LineDecoder` in Python that handles incrementally\n * reading lines from text.\n *\n * https://github.com/encode/httpx/blob/920333ea98118e9cf617f246905d7b202510941c/httpx/_decoders.py#L258\n */\nclass LineDecoder {\n  // prettier-ignore\n  static NEWLINE_CHARS = new Set(['\\n', '\\r', '\\x0b', '\\x0c', '\\x1c', '\\x1d', '\\x1e', '\\x85', '\\u2028', '\\u2029']);\n  static NEWLINE_REGEXP = /\\r\\n|[\\n\\r\\x0b\\x0c\\x1c\\x1d\\x1e\\x85\\u2028\\u2029]/g;\n\n  buffer: string[];\n  trailingCR: boolean;\n  textDecoder: any; // TextDecoder found in browsers; not typed to avoid pulling in either \"dom\" or \"node\" types.\n\n  constructor() {\n    this.buffer = [];\n    this.trailingCR = false;\n  }\n\n  decode(chunk: Bytes): string[] {\n    let text = this.decodeText(chunk);\n\n    if (this.trailingCR) {\n      text = '\\r' + text;\n      this.trailingCR = false;\n    }\n    if (text.endsWith('\\r')) {\n      this.trailingCR = true;\n      text = text.slice(0, -1);\n    }\n\n    if (!text) {\n      return [];\n    }\n\n    const trailingNewline = LineDecoder.NEWLINE_CHARS.has(text[text.length - 1] || '');\n    let lines = text.split(LineDecoder.NEWLINE_REGEXP);\n\n    // if there is a trailing new line then the last entry will be an empty\n    // string which we don't care about\n    if (trailingNewline) {\n      lines.pop();\n    }\n\n    if (lines.length === 1 && !trailingNewline) {\n      this.buffer.push(lines[0]!);\n      return [];\n    }\n\n    if (this.buffer.length > 0) {\n      lines = [this.buffer.join('') + lines[0], ...lines.slice(1)];\n      this.buffer = [];\n    }\n\n    if (!trailingNewline) {\n      this.buffer = [lines.pop() || ''];\n    }\n\n    return lines;\n  }\n\n  decodeText(bytes: Bytes): string {\n    if (bytes == null) return '';\n    if (typeof bytes === 'string') return bytes;\n\n    // Node:\n    if (typeof Buffer !== 'undefined') {\n      if (bytes instanceof Buffer) {\n        return bytes.toString();\n      }\n      if (bytes instanceof Uint8Array) {\n        return Buffer.from(bytes).toString();\n      }\n\n      throw new AnthropicError(\n        `Unexpected: received non-Uint8Array (${bytes.constructor.name}) stream chunk in an environment with a global \"Buffer\" defined, which this library assumes to be Node. Please report this error.`,\n      );\n    }\n\n    // Browser\n    if (typeof TextDecoder !== 'undefined') {\n      if (bytes instanceof Uint8Array || bytes instanceof ArrayBuffer) {\n        this.textDecoder ??= new TextDecoder('utf8');\n        return this.textDecoder.decode(bytes);\n      }\n\n      throw new AnthropicError(\n        `Unexpected: received non-Uint8Array/ArrayBuffer (${\n          (bytes as any).constructor.name\n        }) in a web platform. Please report this error.`,\n      );\n    }\n\n    throw new AnthropicError(\n      `Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.`,\n    );\n  }\n\n  flush(): string[] {\n    if (!this.buffer.length && !this.trailingCR) {\n      return [];\n    }\n\n    const lines = [this.buffer.join('')];\n    this.buffer = [];\n    this.trailingCR = false;\n    return lines;\n  }\n}\n\n/** This is an internal helper function that's just used for testing */\nexport function _decodeChunks(chunks: string[]): string[] {\n  const decoder = new LineDecoder();\n  const lines: string[] = [];\n  for (const chunk of chunks) {\n    lines.push(...decoder.decode(chunk));\n  }\n\n  return lines;\n}\n\nfunction partition(str: string, delimiter: string): [string, string, string] {\n  const index = str.indexOf(delimiter);\n  if (index !== -1) {\n    return [str.substring(0, index), delimiter, str.substring(index + delimiter.length)];\n  }\n\n  return [str, '', ''];\n}\n\n/**\n * Most browsers don't yet have async iterable support for ReadableStream,\n * and Node has a very different way of reading bytes from its \"ReadableStream\".\n *\n * This polyfill was pulled from https://github.com/MattiasBuelens/web-streams-polyfill/pull/122#issuecomment-1627354490\n */\nexport function readableStreamAsyncIterable<T>(stream: any): AsyncIterableIterator<T> {\n  if (stream[Symbol.asyncIterator]) return stream;\n\n  const reader = stream.getReader();\n  return {\n    async next() {\n      try {\n        const result = await reader.read();\n        if (result?.done) reader.releaseLock(); // release lock when stream becomes closed\n        return result;\n      } catch (e) {\n        reader.releaseLock(); // release lock when stream becomes errored\n        throw e;\n      }\n    },\n    async return() {\n      const cancelPromise = reader.cancel();\n      reader.releaseLock();\n      await cancelPromise;\n      return { done: true, value: undefined };\n    },\n    [Symbol.asyncIterator]() {\n      return this;\n    },\n  };\n}\n", "import { type RequestOptions } from './core';\nimport {\n  FormData,\n  File,\n  type Blob,\n  type FilePropertyBag,\n  getMultipartRequestOptions,\n  type FsReadStream,\n  isFsReadStream,\n} from './_shims/index';\nimport { MultipartBody } from './_shims/MultipartBody';\nexport { fileFromPath } from './_shims/index';\n\ntype BlobLikePart = string | ArrayBuffer | ArrayBufferView | BlobLike | Uint8Array | DataView;\nexport type BlobPart = string | ArrayBuffer | ArrayBufferView | Blob | Uint8Array | DataView;\n\n/**\n * Typically, this is a native \"File\" class.\n *\n * We provide the {@link toFile} utility to convert a variety of objects\n * into the File class.\n *\n * For convenience, you can also pass a fetch Response, or in Node,\n * the result of fs.createReadStream().\n */\nexport type Uploadable = FileLike | ResponseLike | FsReadStream;\n\n/**\n * Intended to match web.Blob, node.Blob, node-fetch.Blob, etc.\n */\nexport interface BlobLike {\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Blob/size) */\n  readonly size: number;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Blob/type) */\n  readonly type: string;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Blob/text) */\n  text(): Promise<string>;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Blob/slice) */\n  slice(start?: number, end?: number): BlobLike;\n  // unfortunately @types/node-fetch@^2.6.4 doesn't type the arrayBuffer method\n}\n\n/**\n * Intended to match web.File, node.File, node-fetch.File, etc.\n */\nexport interface FileLike extends BlobLike {\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/File/lastModified) */\n  readonly lastModified: number;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/File/name) */\n  readonly name: string;\n}\n\n/**\n * Intended to match web.Response, node.Response, node-fetch.Response, etc.\n */\nexport interface ResponseLike {\n  url: string;\n  blob(): Promise<BlobLike>;\n}\n\nexport const isResponseLike = (value: any): value is ResponseLike =>\n  value != null &&\n  typeof value === 'object' &&\n  typeof value.url === 'string' &&\n  typeof value.blob === 'function';\n\nexport const isFileLike = (value: any): value is FileLike =>\n  value != null &&\n  typeof value === 'object' &&\n  typeof value.name === 'string' &&\n  typeof value.lastModified === 'number' &&\n  isBlobLike(value);\n\n/**\n * The BlobLike type omits arrayBuffer() because @types/node-fetch@^2.6.4 lacks it; but this check\n * adds the arrayBuffer() method type because it is available and used at runtime\n */\nexport const isBlobLike = (value: any): value is BlobLike & { arrayBuffer(): Promise<ArrayBuffer> } =>\n  value != null &&\n  typeof value === 'object' &&\n  typeof value.size === 'number' &&\n  typeof value.type === 'string' &&\n  typeof value.text === 'function' &&\n  typeof value.slice === 'function' &&\n  typeof value.arrayBuffer === 'function';\n\nexport const isUploadable = (value: any): value is Uploadable => {\n  return isFileLike(value) || isResponseLike(value) || isFsReadStream(value);\n};\n\nexport type ToFileInput = Uploadable | Exclude<BlobLikePart, string> | AsyncIterable<BlobLikePart>;\n\n/**\n * Helper for creating a {@link File} to pass to an SDK upload method from a variety of different data formats\n * @param value the raw content of the file.  Can be an {@link Uploadable}, {@link BlobLikePart}, or {@link AsyncIterable} of {@link BlobLikePart}s\n * @param {string=} name the name of the file. If omitted, toFile will try to determine a file name from bits if possible\n * @param {Object=} options additional properties\n * @param {string=} options.type the MIME type of the content\n * @param {number=} options.lastModified the last modified timestamp\n * @returns a {@link File} with the given properties\n */\nexport async function toFile(\n  value: ToFileInput | PromiseLike<ToFileInput>,\n  name?: string | null | undefined,\n  options: FilePropertyBag | undefined = {},\n): Promise<FileLike> {\n  // If it's a promise, resolve it.\n  value = await value;\n\n  if (isResponseLike(value)) {\n    const blob = await value.blob();\n    name ||= new URL(value.url).pathname.split(/[\\\\/]/).pop() ?? 'unknown_file';\n\n    return new File([blob as any], name, options);\n  }\n\n  const bits = await getBytes(value);\n\n  name ||= getName(value) ?? 'unknown_file';\n\n  if (!options.type) {\n    const type = (bits[0] as any)?.type;\n    if (typeof type === 'string') {\n      options = { ...options, type };\n    }\n  }\n\n  return new File(bits, name, options);\n}\n\nasync function getBytes(value: ToFileInput): Promise<Array<BlobPart>> {\n  let parts: Array<BlobPart> = [];\n  if (\n    typeof value === 'string' ||\n    ArrayBuffer.isView(value) || // includes Uint8Array, Buffer, etc.\n    value instanceof ArrayBuffer\n  ) {\n    parts.push(value);\n  } else if (isBlobLike(value)) {\n    parts.push(await value.arrayBuffer());\n  } else if (\n    isAsyncIterableIterator(value) // includes Readable, ReadableStream, etc.\n  ) {\n    for await (const chunk of value) {\n      parts.push(chunk as BlobPart); // TODO, consider validating?\n    }\n  } else {\n    throw new Error(\n      `Unexpected data type: ${typeof value}; constructor: ${value?.constructor\n        ?.name}; props: ${propsForError(value)}`,\n    );\n  }\n\n  return parts;\n}\n\nfunction propsForError(value: any): string {\n  const props = Object.getOwnPropertyNames(value);\n  return `[${props.map((p) => `\"${p}\"`).join(', ')}]`;\n}\n\nfunction getName(value: any): string | undefined {\n  return (\n    getStringFromMaybeBuffer(value.name) ||\n    getStringFromMaybeBuffer(value.filename) ||\n    // For fs.ReadStream\n    getStringFromMaybeBuffer(value.path)?.split(/[\\\\/]/).pop()\n  );\n}\n\nconst getStringFromMaybeBuffer = (x: string | Buffer | unknown): string | undefined => {\n  if (typeof x === 'string') return x;\n  if (typeof Buffer !== 'undefined' && x instanceof Buffer) return String(x);\n  return undefined;\n};\n\nconst isAsyncIterableIterator = (value: any): value is AsyncIterableIterator<unknown> =>\n  value != null && typeof value === 'object' && typeof value[Symbol.asyncIterator] === 'function';\n\nexport const isMultipartBody = (body: any): body is MultipartBody =>\n  body && typeof body === 'object' && body.body && body[Symbol.toStringTag] === 'MultipartBody';\n\n/**\n * Returns a multipart/form-data request if any part of the given request body contains a File / Blob value.\n * Otherwise returns the request as is.\n */\nexport const maybeMultipartFormRequestOptions = async <T = Record<string, unknown>>(\n  opts: RequestOptions<T>,\n): Promise<RequestOptions<T | MultipartBody>> => {\n  if (!hasUploadableValue(opts.body)) return opts;\n\n  const form = await createForm(opts.body);\n  return getMultipartRequestOptions(form, opts);\n};\n\nexport const multipartFormRequestOptions = async <T = Record<string, unknown>>(\n  opts: RequestOptions<T>,\n): Promise<RequestOptions<T | MultipartBody>> => {\n  const form = await createForm(opts.body);\n  return getMultipartRequestOptions(form, opts);\n};\n\nexport const createForm = async <T = Record<string, unknown>>(body: T | undefined): Promise<FormData> => {\n  const form = new FormData();\n  await Promise.all(Object.entries(body || {}).map(([key, value]) => addFormValue(form, key, value)));\n  return form;\n};\n\nconst hasUploadableValue = (value: unknown): boolean => {\n  if (isUploadable(value)) return true;\n  if (Array.isArray(value)) return value.some(hasUploadableValue);\n  if (value && typeof value === 'object') {\n    for (const k in value) {\n      if (hasUploadableValue((value as any)[k])) return true;\n    }\n  }\n  return false;\n};\n\nconst addFormValue = async (form: FormData, key: string, value: unknown): Promise<void> => {\n  if (value === undefined) return;\n  if (value == null) {\n    throw new TypeError(\n      `Received null for \"${key}\"; to pass null in FormData, you must use the string 'null'`,\n    );\n  }\n\n  // TODO: make nested formats configurable\n  if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n    form.append(key, String(value));\n  } else if (isUploadable(value)) {\n    const file = await toFile(value);\n    form.append(key, file as File);\n  } else if (Array.isArray(value)) {\n    await Promise.all(value.map((entry) => addFormValue(form, key + '[]', entry)));\n  } else if (typeof value === 'object') {\n    await Promise.all(\n      Object.entries(value).map(([name, prop]) => addFormValue(form, `${key}[${name}]`, prop)),\n    );\n  } else {\n    throw new TypeError(\n      `Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${value} instead`,\n    );\n  }\n};\n", "import { VERSION } from './version';\nimport { Stream } from './streaming';\nimport {\n  AnthropicError,\n  APIError,\n  APIConnectionError,\n  APIConnectionTimeoutError,\n  APIUserAbortError,\n} from './error';\nimport {\n  kind as shimsKind,\n  type Readable,\n  getDefaultAgent,\n  type Agent,\n  fetch,\n  type RequestInfo,\n  type RequestInit,\n  type Response,\n  type HeadersInit,\n} from './_shims/index';\nexport { type Response };\nimport { isMultipartBody } from './uploads';\nexport {\n  maybeMultipartFormRequestOptions,\n  multipartFormRequestOptions,\n  createForm,\n  type Uploadable,\n} from './uploads';\n\nexport type Fetch = (url: RequestInfo, init?: RequestInit) => Promise<Response>;\n\ntype PromiseOrValue<T> = T | Promise<T>;\n\ntype APIResponseProps = {\n  response: Response;\n  options: FinalRequestOptions;\n  controller: AbortController;\n};\n\nasync function defaultParseResponse<T>(props: APIResponseProps): Promise<T> {\n  const { response } = props;\n  if (props.options.stream) {\n    debug('response', response.status, response.url, response.headers, response.body);\n\n    // Note: there is an invariant here that isn't represented in the type system\n    // that if you set `stream: true` the response type must also be `Stream<T>`\n\n    if (props.options.__streamClass) {\n      return props.options.__streamClass.fromSSEResponse(response, props.controller) as any;\n    }\n\n    return Stream.fromSSEResponse(response, props.controller) as any;\n  }\n\n  // fetch refuses to read the body when the status code is 204.\n  if (response.status === 204) {\n    return null as T;\n  }\n\n  if (props.options.__binaryResponse) {\n    return response as unknown as T;\n  }\n\n  const contentType = response.headers.get('content-type');\n  const isJSON =\n    contentType?.includes('application/json') || contentType?.includes('application/vnd.api+json');\n  if (isJSON) {\n    const json = await response.json();\n\n    debug('response', response.status, response.url, response.headers, json);\n\n    return json as T;\n  }\n\n  const text = await response.text();\n  debug('response', response.status, response.url, response.headers, text);\n\n  // TODO handle blob, arraybuffer, other content types, etc.\n  return text as unknown as T;\n}\n\n/**\n * A subclass of `Promise` providing additional helper methods\n * for interacting with the SDK.\n */\nexport class APIPromise<T> extends Promise<T> {\n  private parsedPromise: Promise<T> | undefined;\n\n  constructor(\n    private responsePromise: Promise<APIResponseProps>,\n    private parseResponse: (props: APIResponseProps) => PromiseOrValue<T> = defaultParseResponse,\n  ) {\n    super((resolve) => {\n      // this is maybe a bit weird but this has to be a no-op to not implicitly\n      // parse the response body; instead .then, .catch, .finally are overridden\n      // to parse the response\n      resolve(null as any);\n    });\n  }\n\n  _thenUnwrap<U>(transform: (data: T) => U): APIPromise<U> {\n    return new APIPromise(this.responsePromise, async (props) => transform(await this.parseResponse(props)));\n  }\n\n  /**\n   * Gets the raw `Response` instance instead of parsing the response\n   * data.\n   *\n   * If you want to parse the response body but still get the `Response`\n   * instance, you can use {@link withResponse()}.\n   *\n   * 👋 Getting the wrong TypeScript type for `Response`?\n   * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n   * or add one of these imports before your first `import … from '@anthropic-ai/sdk'`:\n   * - `import '@anthropic-ai/sdk/shims/node'` (if you're running on Node)\n   * - `import '@anthropic-ai/sdk/shims/web'` (otherwise)\n   */\n  asResponse(): Promise<Response> {\n    return this.responsePromise.then((p) => p.response);\n  }\n  /**\n   * Gets the parsed response data and the raw `Response` instance.\n   *\n   * If you just want to get the raw `Response` instance without parsing it,\n   * you can use {@link asResponse()}.\n   *\n   *\n   * 👋 Getting the wrong TypeScript type for `Response`?\n   * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n   * or add one of these imports before your first `import … from '@anthropic-ai/sdk'`:\n   * - `import '@anthropic-ai/sdk/shims/node'` (if you're running on Node)\n   * - `import '@anthropic-ai/sdk/shims/web'` (otherwise)\n   */\n  async withResponse(): Promise<{ data: T; response: Response }> {\n    const [data, response] = await Promise.all([this.parse(), this.asResponse()]);\n    return { data, response };\n  }\n\n  private parse(): Promise<T> {\n    if (!this.parsedPromise) {\n      this.parsedPromise = this.responsePromise.then(this.parseResponse);\n    }\n    return this.parsedPromise;\n  }\n\n  override then<TResult1 = T, TResult2 = never>(\n    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,\n    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null,\n  ): Promise<TResult1 | TResult2> {\n    return this.parse().then(onfulfilled, onrejected);\n  }\n\n  override catch<TResult = never>(\n    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null,\n  ): Promise<T | TResult> {\n    return this.parse().catch(onrejected);\n  }\n\n  override finally(onfinally?: (() => void) | undefined | null): Promise<T> {\n    return this.parse().finally(onfinally);\n  }\n}\n\nexport abstract class APIClient {\n  baseURL: string;\n  maxRetries: number;\n  timeout: number;\n  httpAgent: Agent | undefined;\n\n  private fetch: Fetch;\n  protected idempotencyHeader?: string;\n\n  constructor({\n    baseURL,\n    maxRetries = 2,\n    timeout = 600000, // 10 minutes\n    httpAgent,\n    fetch: overridenFetch,\n  }: {\n    baseURL: string;\n    maxRetries?: number | undefined;\n    timeout: number | undefined;\n    httpAgent: Agent | undefined;\n    fetch: Fetch | undefined;\n  }) {\n    this.baseURL = baseURL;\n    this.maxRetries = validatePositiveInteger('maxRetries', maxRetries);\n    this.timeout = validatePositiveInteger('timeout', timeout);\n    this.httpAgent = httpAgent;\n\n    this.fetch = overridenFetch ?? fetch;\n  }\n\n  protected authHeaders(opts: FinalRequestOptions): Headers {\n    return {};\n  }\n\n  /**\n   * Override this to add your own default headers, for example:\n   *\n   *  {\n   *    ...super.defaultHeaders(),\n   *    Authorization: 'Bearer 123',\n   *  }\n   */\n  protected defaultHeaders(opts: FinalRequestOptions): Headers {\n    return {\n      Accept: 'application/json',\n      'Content-Type': 'application/json',\n      'User-Agent': this.getUserAgent(),\n      ...getPlatformHeaders(),\n      ...this.authHeaders(opts),\n    };\n  }\n\n  protected abstract defaultQuery(): DefaultQuery | undefined;\n\n  /**\n   * Override this to add your own headers validation:\n   */\n  protected validateHeaders(headers: Headers, customHeaders: Headers) {}\n\n  protected defaultIdempotencyKey(): string {\n    return `stainless-node-retry-${uuid4()}`;\n  }\n\n  get<Req, Rsp>(path: string, opts?: PromiseOrValue<RequestOptions<Req>>): APIPromise<Rsp> {\n    return this.methodRequest('get', path, opts);\n  }\n\n  post<Req, Rsp>(path: string, opts?: PromiseOrValue<RequestOptions<Req>>): APIPromise<Rsp> {\n    return this.methodRequest('post', path, opts);\n  }\n\n  patch<Req, Rsp>(path: string, opts?: PromiseOrValue<RequestOptions<Req>>): APIPromise<Rsp> {\n    return this.methodRequest('patch', path, opts);\n  }\n\n  put<Req, Rsp>(path: string, opts?: PromiseOrValue<RequestOptions<Req>>): APIPromise<Rsp> {\n    return this.methodRequest('put', path, opts);\n  }\n\n  delete<Req, Rsp>(path: string, opts?: PromiseOrValue<RequestOptions<Req>>): APIPromise<Rsp> {\n    return this.methodRequest('delete', path, opts);\n  }\n\n  private methodRequest<Req, Rsp>(\n    method: HTTPMethod,\n    path: string,\n    opts?: PromiseOrValue<RequestOptions<Req>>,\n  ): APIPromise<Rsp> {\n    return this.request(Promise.resolve(opts).then((opts) => ({ method, path, ...opts })));\n  }\n\n  getAPIList<Item, PageClass extends AbstractPage<Item> = AbstractPage<Item>>(\n    path: string,\n    Page: new (...args: any[]) => PageClass,\n    opts?: RequestOptions<any>,\n  ): PagePromise<PageClass, Item> {\n    return this.requestAPIList(Page, { method: 'get', path, ...opts });\n  }\n\n  private calculateContentLength(body: unknown): string | null {\n    if (typeof body === 'string') {\n      if (typeof Buffer !== 'undefined') {\n        return Buffer.byteLength(body, 'utf8').toString();\n      }\n\n      if (typeof TextEncoder !== 'undefined') {\n        const encoder = new TextEncoder();\n        const encoded = encoder.encode(body);\n        return encoded.length.toString();\n      }\n    }\n\n    return null;\n  }\n\n  buildRequest<Req>(options: FinalRequestOptions<Req>): { req: RequestInit; url: string; timeout: number } {\n    const { method, path, query, headers: headers = {} } = options;\n\n    const body =\n      isMultipartBody(options.body) ? options.body.body\n      : options.body ? JSON.stringify(options.body, null, 2)\n      : null;\n    const contentLength = this.calculateContentLength(body);\n\n    const url = this.buildURL(path!, query);\n    if ('timeout' in options) validatePositiveInteger('timeout', options.timeout);\n    const timeout = options.timeout ?? this.timeout;\n    const httpAgent = options.httpAgent ?? this.httpAgent ?? getDefaultAgent(url);\n    const minAgentTimeout = timeout + 1000;\n    if (\n      typeof (httpAgent as any)?.options?.timeout === 'number' &&\n      minAgentTimeout > ((httpAgent as any).options.timeout ?? 0)\n    ) {\n      // Allow any given request to bump our agent active socket timeout.\n      // This may seem strange, but leaking active sockets should be rare and not particularly problematic,\n      // and without mutating agent we would need to create more of them.\n      // This tradeoff optimizes for performance.\n      (httpAgent as any).options.timeout = minAgentTimeout;\n    }\n\n    if (this.idempotencyHeader && method !== 'get') {\n      if (!options.idempotencyKey) options.idempotencyKey = this.defaultIdempotencyKey();\n      headers[this.idempotencyHeader] = options.idempotencyKey;\n    }\n\n    const reqHeaders = this.buildHeaders({ options, headers, contentLength });\n\n    const req: RequestInit = {\n      method,\n      ...(body && { body: body as any }),\n      headers: reqHeaders,\n      ...(httpAgent && { agent: httpAgent }),\n      // @ts-ignore node-fetch uses a custom AbortSignal type that is\n      // not compatible with standard web types\n      signal: options.signal ?? null,\n    };\n\n    return { req, url, timeout };\n  }\n\n  private buildHeaders({\n    options,\n    headers,\n    contentLength,\n  }: {\n    options: FinalRequestOptions;\n    headers: Record<string, string | null | undefined>;\n    contentLength: string | null | undefined;\n  }): Record<string, string> {\n    const reqHeaders: Record<string, string> = {};\n    if (contentLength) {\n      reqHeaders['content-length'] = contentLength;\n    }\n\n    const defaultHeaders = this.defaultHeaders(options);\n    applyHeadersMut(reqHeaders, defaultHeaders);\n    applyHeadersMut(reqHeaders, headers);\n\n    // let builtin fetch set the Content-Type for multipart bodies\n    if (isMultipartBody(options.body) && shimsKind !== 'node') {\n      delete reqHeaders['content-type'];\n    }\n\n    this.validateHeaders(reqHeaders, headers);\n\n    return reqHeaders;\n  }\n\n  /**\n   * Used as a callback for mutating the given `FinalRequestOptions` object.\n   */\n  protected async prepareOptions(options: FinalRequestOptions): Promise<void> {}\n\n  /**\n   * Used as a callback for mutating the given `RequestInit` object.\n   *\n   * This is useful for cases where you want to add certain headers based off of\n   * the request properties, e.g. `method` or `url`.\n   */\n  protected async prepareRequest(\n    request: RequestInit,\n    { url, options }: { url: string; options: FinalRequestOptions },\n  ): Promise<void> {}\n\n  protected parseHeaders(headers: HeadersInit | null | undefined): Record<string, string> {\n    return (\n      !headers ? {}\n      : Symbol.iterator in headers ?\n        Object.fromEntries(Array.from(headers as Iterable<string[]>).map((header) => [...header]))\n      : { ...headers }\n    );\n  }\n\n  protected makeStatusError(\n    status: number | undefined,\n    error: Object | undefined,\n    message: string | undefined,\n    headers: Headers | undefined,\n  ) {\n    return APIError.generate(status, error, message, headers);\n  }\n\n  request<Req, Rsp>(\n    options: PromiseOrValue<FinalRequestOptions<Req>>,\n    remainingRetries: number | null = null,\n  ): APIPromise<Rsp> {\n    return new APIPromise(this.makeRequest(options, remainingRetries));\n  }\n\n  private async makeRequest<Req>(\n    optionsInput: PromiseOrValue<FinalRequestOptions<Req>>,\n    retriesRemaining: number | null,\n  ): Promise<APIResponseProps> {\n    const options = await optionsInput;\n    if (retriesRemaining == null) {\n      retriesRemaining = options.maxRetries ?? this.maxRetries;\n    }\n\n    await this.prepareOptions(options);\n\n    const { req, url, timeout } = this.buildRequest(options);\n\n    await this.prepareRequest(req, { url, options });\n\n    debug('request', url, options, req.headers);\n\n    if (options.signal?.aborted) {\n      throw new APIUserAbortError();\n    }\n\n    const controller = new AbortController();\n    const response = await this.fetchWithTimeout(url, req, timeout, controller).catch(castToError);\n\n    if (response instanceof Error) {\n      if (options.signal?.aborted) {\n        throw new APIUserAbortError();\n      }\n      if (retriesRemaining) {\n        return this.retryRequest(options, retriesRemaining);\n      }\n      if (response.name === 'AbortError') {\n        throw new APIConnectionTimeoutError();\n      }\n      throw new APIConnectionError({ cause: response });\n    }\n\n    const responseHeaders = createResponseHeaders(response.headers);\n\n    if (!response.ok) {\n      if (retriesRemaining && this.shouldRetry(response)) {\n        const retryMessage = `retrying, ${retriesRemaining} attempts remaining`;\n        debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders);\n        return this.retryRequest(options, retriesRemaining, responseHeaders);\n      }\n\n      const errText = await response.text().catch((e) => castToError(e).message);\n      const errJSON = safeJSON(errText);\n      const errMessage = errJSON ? undefined : errText;\n      const retryMessage = retriesRemaining ? `(error; no more retries left)` : `(error; not retryable)`;\n\n      debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders, errMessage);\n\n      const err = this.makeStatusError(response.status, errJSON, errMessage, responseHeaders);\n      throw err;\n    }\n\n    return { response, options, controller };\n  }\n\n  requestAPIList<Item = unknown, PageClass extends AbstractPage<Item> = AbstractPage<Item>>(\n    Page: new (...args: ConstructorParameters<typeof AbstractPage>) => PageClass,\n    options: FinalRequestOptions,\n  ): PagePromise<PageClass, Item> {\n    const request = this.makeRequest(options, null);\n    return new PagePromise<PageClass, Item>(this, request, Page);\n  }\n\n  buildURL<Req>(path: string, query: Req | null | undefined): string {\n    const url =\n      isAbsoluteURL(path) ?\n        new URL(path)\n      : new URL(this.baseURL + (this.baseURL.endsWith('/') && path.startsWith('/') ? path.slice(1) : path));\n\n    const defaultQuery = this.defaultQuery();\n    if (!isEmptyObj(defaultQuery)) {\n      query = { ...defaultQuery, ...query } as Req;\n    }\n\n    if (typeof query === 'object' && query && !Array.isArray(query)) {\n      url.search = this.stringifyQuery(query as Record<string, unknown>);\n    }\n\n    return url.toString();\n  }\n\n  protected stringifyQuery(query: Record<string, unknown>): string {\n    return Object.entries(query)\n      .filter(([_, value]) => typeof value !== 'undefined')\n      .map(([key, value]) => {\n        if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n          return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;\n        }\n        if (value === null) {\n          return `${encodeURIComponent(key)}=`;\n        }\n        throw new AnthropicError(\n          `Cannot stringify type ${typeof value}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`,\n        );\n      })\n      .join('&');\n  }\n\n  async fetchWithTimeout(\n    url: RequestInfo,\n    init: RequestInit | undefined,\n    ms: number,\n    controller: AbortController,\n  ): Promise<Response> {\n    const { signal, ...options } = init || {};\n    if (signal) signal.addEventListener('abort', () => controller.abort());\n\n    const timeout = setTimeout(() => controller.abort(), ms);\n\n    return (\n      this.getRequestClient()\n        // use undefined this binding; fetch errors if bound to something else in browser/cloudflare\n        .fetch.call(undefined, url, { signal: controller.signal as any, ...options })\n        .finally(() => {\n          clearTimeout(timeout);\n        })\n    );\n  }\n\n  protected getRequestClient(): RequestClient {\n    return { fetch: this.fetch };\n  }\n\n  private shouldRetry(response: Response): boolean {\n    // Note this is not a standard header.\n    const shouldRetryHeader = response.headers.get('x-should-retry');\n\n    // If the server explicitly says whether or not to retry, obey.\n    if (shouldRetryHeader === 'true') return true;\n    if (shouldRetryHeader === 'false') return false;\n\n    // Retry on request timeouts.\n    if (response.status === 408) return true;\n\n    // Retry on lock timeouts.\n    if (response.status === 409) return true;\n\n    // Retry on rate limits.\n    if (response.status === 429) return true;\n\n    // Retry internal errors.\n    if (response.status >= 500) return true;\n\n    return false;\n  }\n\n  private async retryRequest(\n    options: FinalRequestOptions,\n    retriesRemaining: number,\n    responseHeaders?: Headers | undefined,\n  ): Promise<APIResponseProps> {\n    let timeoutMillis: number | undefined;\n\n    // Note the `retry-after-ms` header may not be standard, but is a good idea and we'd like proactive support for it.\n    const retryAfterMillisHeader = responseHeaders?.['retry-after-ms'];\n    if (retryAfterMillisHeader) {\n      const timeoutMs = parseFloat(retryAfterMillisHeader);\n      if (!Number.isNaN(timeoutMs)) {\n        timeoutMillis = timeoutMs;\n      }\n    }\n\n    // About the Retry-After header: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After\n    const retryAfterHeader = responseHeaders?.['retry-after'];\n    if (retryAfterHeader && !timeoutMillis) {\n      const timeoutSeconds = parseFloat(retryAfterHeader);\n      if (!Number.isNaN(timeoutSeconds)) {\n        timeoutMillis = timeoutSeconds * 1000;\n      } else {\n        timeoutMillis = Date.parse(retryAfterHeader) - Date.now();\n      }\n    }\n\n    // If the API asks us to wait a certain amount of time (and it's a reasonable amount),\n    // just do what it says, but otherwise calculate a default\n    if (!(timeoutMillis && 0 <= timeoutMillis && timeoutMillis < 60 * 1000)) {\n      const maxRetries = options.maxRetries ?? this.maxRetries;\n      timeoutMillis = this.calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries);\n    }\n    await sleep(timeoutMillis);\n\n    return this.makeRequest(options, retriesRemaining - 1);\n  }\n\n  private calculateDefaultRetryTimeoutMillis(retriesRemaining: number, maxRetries: number): number {\n    const initialRetryDelay = 0.5;\n    const maxRetryDelay = 8.0;\n\n    const numRetries = maxRetries - retriesRemaining;\n\n    // Apply exponential backoff, but not more than the max.\n    const sleepSeconds = Math.min(initialRetryDelay * Math.pow(2, numRetries), maxRetryDelay);\n\n    // Apply some jitter, take up to at most 25 percent of the retry time.\n    const jitter = 1 - Math.random() * 0.25;\n\n    return sleepSeconds * jitter * 1000;\n  }\n\n  private getUserAgent(): string {\n    return `${this.constructor.name}/JS ${VERSION}`;\n  }\n}\n\nexport type PageInfo = { url: URL } | { params: Record<string, unknown> | null };\n\nexport abstract class AbstractPage<Item> implements AsyncIterable<Item> {\n  #client: APIClient;\n  protected options: FinalRequestOptions;\n\n  protected response: Response;\n  protected body: unknown;\n\n  constructor(client: APIClient, response: Response, body: unknown, options: FinalRequestOptions) {\n    this.#client = client;\n    this.options = options;\n    this.response = response;\n    this.body = body;\n  }\n\n  /**\n   * @deprecated Use nextPageInfo instead\n   */\n  abstract nextPageParams(): Partial<Record<string, unknown>> | null;\n  abstract nextPageInfo(): PageInfo | null;\n\n  abstract getPaginatedItems(): Item[];\n\n  hasNextPage(): boolean {\n    const items = this.getPaginatedItems();\n    if (!items.length) return false;\n    return this.nextPageInfo() != null;\n  }\n\n  async getNextPage(): Promise<this> {\n    const nextInfo = this.nextPageInfo();\n    if (!nextInfo) {\n      throw new AnthropicError(\n        'No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.',\n      );\n    }\n    const nextOptions = { ...this.options };\n    if ('params' in nextInfo && typeof nextOptions.query === 'object') {\n      nextOptions.query = { ...nextOptions.query, ...nextInfo.params };\n    } else if ('url' in nextInfo) {\n      const params = [...Object.entries(nextOptions.query || {}), ...nextInfo.url.searchParams.entries()];\n      for (const [key, value] of params) {\n        nextInfo.url.searchParams.set(key, value as any);\n      }\n      nextOptions.query = undefined;\n      nextOptions.path = nextInfo.url.toString();\n    }\n    return await this.#client.requestAPIList(this.constructor as any, nextOptions);\n  }\n\n  async *iterPages() {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    let page: AbstractPage<Item> = this;\n    yield page;\n    while (page.hasNextPage()) {\n      page = await page.getNextPage();\n      yield page;\n    }\n  }\n\n  async *[Symbol.asyncIterator]() {\n    for await (const page of this.iterPages()) {\n      for (const item of page.getPaginatedItems()) {\n        yield item;\n      }\n    }\n  }\n}\n\n/**\n * This subclass of Promise will resolve to an instantiated Page once the request completes.\n *\n * It also implements AsyncIterable to allow auto-paginating iteration on an unawaited list call, eg:\n *\n *    for await (const item of client.items.list()) {\n *      console.log(item)\n *    }\n */\nexport class PagePromise<\n    PageClass extends AbstractPage<Item>,\n    Item = ReturnType<PageClass['getPaginatedItems']>[number],\n  >\n  extends APIPromise<PageClass>\n  implements AsyncIterable<Item>\n{\n  constructor(\n    client: APIClient,\n    request: Promise<APIResponseProps>,\n    Page: new (...args: ConstructorParameters<typeof AbstractPage>) => PageClass,\n  ) {\n    super(\n      request,\n      async (props) => new Page(client, props.response, await defaultParseResponse(props), props.options),\n    );\n  }\n\n  /**\n   * Allow auto-paginating iteration on an unawaited list call, eg:\n   *\n   *    for await (const item of client.items.list()) {\n   *      console.log(item)\n   *    }\n   */\n  async *[Symbol.asyncIterator]() {\n    const page = await this;\n    for await (const item of page) {\n      yield item;\n    }\n  }\n}\n\nexport const createResponseHeaders = (\n  headers: Awaited<ReturnType<Fetch>>['headers'],\n): Record<string, string> => {\n  return new Proxy(\n    Object.fromEntries(\n      // @ts-ignore\n      headers.entries(),\n    ),\n    {\n      get(target, name) {\n        const key = name.toString();\n        return target[key.toLowerCase()] || target[key];\n      },\n    },\n  );\n};\n\ntype HTTPMethod = 'get' | 'post' | 'put' | 'patch' | 'delete';\n\nexport type RequestClient = { fetch: Fetch };\nexport type Headers = Record<string, string | null | undefined>;\nexport type DefaultQuery = Record<string, string | undefined>;\nexport type KeysEnum<T> = { [P in keyof Required<T>]: true };\n\nexport type RequestOptions<Req = unknown | Record<string, unknown> | Readable> = {\n  method?: HTTPMethod;\n  path?: string;\n  query?: Req | undefined;\n  body?: Req | null | undefined;\n  headers?: Headers | undefined;\n\n  maxRetries?: number;\n  stream?: boolean | undefined;\n  timeout?: number;\n  httpAgent?: Agent;\n  signal?: AbortSignal | undefined | null;\n  idempotencyKey?: string;\n\n  __binaryResponse?: boolean | undefined;\n  __streamClass?: typeof Stream;\n};\n\n// This is required so that we can determine if a given object matches the RequestOptions\n// type at runtime. While this requires duplication, it is enforced by the TypeScript\n// compiler such that any missing / extraneous keys will cause an error.\nconst requestOptionsKeys: KeysEnum<RequestOptions> = {\n  method: true,\n  path: true,\n  query: true,\n  body: true,\n  headers: true,\n\n  maxRetries: true,\n  stream: true,\n  timeout: true,\n  httpAgent: true,\n  signal: true,\n  idempotencyKey: true,\n\n  __binaryResponse: true,\n  __streamClass: true,\n};\n\nexport const isRequestOptions = (obj: unknown): obj is RequestOptions => {\n  return (\n    typeof obj === 'object' &&\n    obj !== null &&\n    !isEmptyObj(obj) &&\n    Object.keys(obj).every((k) => hasOwn(requestOptionsKeys, k))\n  );\n};\n\nexport type FinalRequestOptions<Req = unknown | Record<string, unknown> | Readable> = RequestOptions<Req> & {\n  method: HTTPMethod;\n  path: string;\n};\n\ndeclare const Deno: any;\ndeclare const EdgeRuntime: any;\ntype Arch = 'x32' | 'x64' | 'arm' | 'arm64' | `other:${string}` | 'unknown';\ntype PlatformName =\n  | 'MacOS'\n  | 'Linux'\n  | 'Windows'\n  | 'FreeBSD'\n  | 'OpenBSD'\n  | 'iOS'\n  | 'Android'\n  | `Other:${string}`\n  | 'Unknown';\ntype Browser = 'ie' | 'edge' | 'chrome' | 'firefox' | 'safari';\ntype PlatformProperties = {\n  'X-Stainless-Lang': 'js';\n  'X-Stainless-Package-Version': string;\n  'X-Stainless-OS': PlatformName;\n  'X-Stainless-Arch': Arch;\n  'X-Stainless-Runtime': 'node' | 'deno' | 'edge' | `browser:${Browser}` | 'unknown';\n  'X-Stainless-Runtime-Version': string;\n};\nconst getPlatformProperties = (): PlatformProperties => {\n  if (typeof Deno !== 'undefined' && Deno.build != null) {\n    return {\n      'X-Stainless-Lang': 'js',\n      'X-Stainless-Package-Version': VERSION,\n      'X-Stainless-OS': normalizePlatform(Deno.build.os),\n      'X-Stainless-Arch': normalizeArch(Deno.build.arch),\n      'X-Stainless-Runtime': 'deno',\n      'X-Stainless-Runtime-Version': Deno.version,\n    };\n  }\n  if (typeof EdgeRuntime !== 'undefined') {\n    return {\n      'X-Stainless-Lang': 'js',\n      'X-Stainless-Package-Version': VERSION,\n      'X-Stainless-OS': 'Unknown',\n      'X-Stainless-Arch': `other:${EdgeRuntime}`,\n      'X-Stainless-Runtime': 'edge',\n      'X-Stainless-Runtime-Version': process.version,\n    };\n  }\n  // Check if Node.js\n  if (Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]') {\n    return {\n      'X-Stainless-Lang': 'js',\n      'X-Stainless-Package-Version': VERSION,\n      'X-Stainless-OS': normalizePlatform(process.platform),\n      'X-Stainless-Arch': normalizeArch(process.arch),\n      'X-Stainless-Runtime': 'node',\n      'X-Stainless-Runtime-Version': process.version,\n    };\n  }\n\n  const browserInfo = getBrowserInfo();\n  if (browserInfo) {\n    return {\n      'X-Stainless-Lang': 'js',\n      'X-Stainless-Package-Version': VERSION,\n      'X-Stainless-OS': 'Unknown',\n      'X-Stainless-Arch': 'unknown',\n      'X-Stainless-Runtime': `browser:${browserInfo.browser}`,\n      'X-Stainless-Runtime-Version': browserInfo.version,\n    };\n  }\n\n  // TODO add support for Cloudflare workers, etc.\n  return {\n    'X-Stainless-Lang': 'js',\n    'X-Stainless-Package-Version': VERSION,\n    'X-Stainless-OS': 'Unknown',\n    'X-Stainless-Arch': 'unknown',\n    'X-Stainless-Runtime': 'unknown',\n    'X-Stainless-Runtime-Version': 'unknown',\n  };\n};\n\ntype BrowserInfo = {\n  browser: Browser;\n  version: string;\n};\n\ndeclare const navigator: { userAgent: string } | undefined;\n\n// Note: modified from https://github.com/JS-DevTools/host-environment/blob/b1ab79ecde37db5d6e163c050e54fe7d287d7c92/src/isomorphic.browser.ts\nfunction getBrowserInfo(): BrowserInfo | null {\n  if (typeof navigator === 'undefined' || !navigator) {\n    return null;\n  }\n\n  // NOTE: The order matters here!\n  const browserPatterns = [\n    { key: 'edge' as const, pattern: /Edge(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'ie' as const, pattern: /MSIE(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'ie' as const, pattern: /Trident(?:.*rv\\:(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'chrome' as const, pattern: /Chrome(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'firefox' as const, pattern: /Firefox(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'safari' as const, pattern: /(?:Version\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?(?:\\W+Mobile\\S*)?\\W+Safari/ },\n  ];\n\n  // Find the FIRST matching browser\n  for (const { key, pattern } of browserPatterns) {\n    const match = pattern.exec(navigator.userAgent);\n    if (match) {\n      const major = match[1] || 0;\n      const minor = match[2] || 0;\n      const patch = match[3] || 0;\n\n      return { browser: key, version: `${major}.${minor}.${patch}` };\n    }\n  }\n\n  return null;\n}\n\nconst normalizeArch = (arch: string): Arch => {\n  // Node docs:\n  // - https://nodejs.org/api/process.html#processarch\n  // Deno docs:\n  // - https://doc.deno.land/deno/stable/~/Deno.build\n  if (arch === 'x32') return 'x32';\n  if (arch === 'x86_64' || arch === 'x64') return 'x64';\n  if (arch === 'arm') return 'arm';\n  if (arch === 'aarch64' || arch === 'arm64') return 'arm64';\n  if (arch) return `other:${arch}`;\n  return 'unknown';\n};\n\nconst normalizePlatform = (platform: string): PlatformName => {\n  // Node platforms:\n  // - https://nodejs.org/api/process.html#processplatform\n  // Deno platforms:\n  // - https://doc.deno.land/deno/stable/~/Deno.build\n  // - https://github.com/denoland/deno/issues/14799\n\n  platform = platform.toLowerCase();\n\n  // NOTE: this iOS check is untested and may not work\n  // Node does not work natively on IOS, there is a fork at\n  // https://github.com/nodejs-mobile/nodejs-mobile\n  // however it is unknown at the time of writing how to detect if it is running\n  if (platform.includes('ios')) return 'iOS';\n  if (platform === 'android') return 'Android';\n  if (platform === 'darwin') return 'MacOS';\n  if (platform === 'win32') return 'Windows';\n  if (platform === 'freebsd') return 'FreeBSD';\n  if (platform === 'openbsd') return 'OpenBSD';\n  if (platform === 'linux') return 'Linux';\n  if (platform) return `Other:${platform}`;\n  return 'Unknown';\n};\n\nlet _platformHeaders: PlatformProperties;\nconst getPlatformHeaders = () => {\n  return (_platformHeaders ??= getPlatformProperties());\n};\n\nexport const safeJSON = (text: string) => {\n  try {\n    return JSON.parse(text);\n  } catch (err) {\n    return undefined;\n  }\n};\n\n// https://stackoverflow.com/a/19709846\nconst startsWithSchemeRegexp = new RegExp('^(?:[a-z]+:)?//', 'i');\nconst isAbsoluteURL = (url: string): boolean => {\n  return startsWithSchemeRegexp.test(url);\n};\n\nexport const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));\n\nconst validatePositiveInteger = (name: string, n: unknown): number => {\n  if (typeof n !== 'number' || !Number.isInteger(n)) {\n    throw new AnthropicError(`${name} must be an integer`);\n  }\n  if (n < 0) {\n    throw new AnthropicError(`${name} must be a positive integer`);\n  }\n  return n;\n};\n\nexport const castToError = (err: any): Error => {\n  if (err instanceof Error) return err;\n  return new Error(err);\n};\n\nexport const ensurePresent = <T>(value: T | null | undefined): T => {\n  if (value == null) throw new AnthropicError(`Expected a value to be given but received ${value} instead.`);\n  return value;\n};\n\n/**\n * Read an environment variable.\n *\n * Trims beginning and trailing whitespace.\n *\n * Will return undefined if the environment variable doesn't exist or cannot be accessed.\n */\nexport const readEnv = (env: string): string | undefined => {\n  if (typeof process !== 'undefined') {\n    return process.env?.[env]?.trim() ?? undefined;\n  }\n  if (typeof Deno !== 'undefined') {\n    return Deno.env?.get?.(env)?.trim();\n  }\n  return undefined;\n};\n\nexport const coerceInteger = (value: unknown): number => {\n  if (typeof value === 'number') return Math.round(value);\n  if (typeof value === 'string') return parseInt(value, 10);\n\n  throw new AnthropicError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\n\nexport const coerceFloat = (value: unknown): number => {\n  if (typeof value === 'number') return value;\n  if (typeof value === 'string') return parseFloat(value);\n\n  throw new AnthropicError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\n\nexport const coerceBoolean = (value: unknown): boolean => {\n  if (typeof value === 'boolean') return value;\n  if (typeof value === 'string') return value === 'true';\n  return Boolean(value);\n};\n\nexport const maybeCoerceInteger = (value: unknown): number | undefined => {\n  if (value === undefined) {\n    return undefined;\n  }\n  return coerceInteger(value);\n};\n\nexport const maybeCoerceFloat = (value: unknown): number | undefined => {\n  if (value === undefined) {\n    return undefined;\n  }\n  return coerceFloat(value);\n};\n\nexport const maybeCoerceBoolean = (value: unknown): boolean | undefined => {\n  if (value === undefined) {\n    return undefined;\n  }\n  return coerceBoolean(value);\n};\n\n// https://stackoverflow.com/a/34491287\nexport function isEmptyObj(obj: Object | null | undefined): boolean {\n  if (!obj) return true;\n  for (const _k in obj) return false;\n  return true;\n}\n\n// https://eslint.org/docs/latest/rules/no-prototype-builtins\nexport function hasOwn(obj: Object, key: string): boolean {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\n/**\n * Copies headers from \"newHeaders\" onto \"targetHeaders\",\n * using lower-case for all properties,\n * ignoring any keys with undefined values,\n * and deleting any keys with null values.\n */\nfunction applyHeadersMut(targetHeaders: Headers, newHeaders: Headers): void {\n  for (const k in newHeaders) {\n    if (!hasOwn(newHeaders, k)) continue;\n    const lowerKey = k.toLowerCase();\n    if (!lowerKey) continue;\n\n    const val = newHeaders[k];\n\n    if (val === null) {\n      delete targetHeaders[lowerKey];\n    } else if (val !== undefined) {\n      targetHeaders[lowerKey] = val;\n    }\n  }\n}\n\nexport function debug(action: string, ...args: any[]) {\n  if (typeof process !== 'undefined' && process.env['DEBUG'] === 'true') {\n    console.log(`Anthropic:DEBUG:${action}`, ...args);\n  }\n}\n\n/**\n * https://stackoverflow.com/a/2117523\n */\nconst uuid4 = () => {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16) | 0;\n    const v = c === 'x' ? r : (r & 0x3) | 0x8;\n    return v.toString(16);\n  });\n};\n\nexport const isRunningInBrowser = () => {\n  return (\n    // @ts-ignore\n    typeof window !== 'undefined' &&\n    // @ts-ignore\n    typeof window.document !== 'undefined' &&\n    // @ts-ignore\n    typeof navigator !== 'undefined'\n  );\n};\n\nexport interface HeadersProtocol {\n  get: (header: string) => string | null | undefined;\n}\nexport type HeadersLike = Record<string, string | string[] | undefined> | HeadersProtocol;\n\nexport const isHeadersProtocol = (headers: any): headers is HeadersProtocol => {\n  return typeof headers?.get === 'function';\n};\n\nexport const getRequiredHeader = (headers: HeadersLike, header: string): string => {\n  const lowerCasedHeader = header.toLowerCase();\n  if (isHeadersProtocol(headers)) {\n    // to deal with the case where the header looks like Stainless-Event-Id\n    const intercapsHeader =\n      header[0]?.toUpperCase() +\n      header.substring(1).replace(/([^\\w])(\\w)/g, (_m, g1, g2) => g1 + g2.toUpperCase());\n    for (const key of [header, lowerCasedHeader, header.toUpperCase(), intercapsHeader]) {\n      const value = headers.get(key);\n      if (value) {\n        return value;\n      }\n    }\n  }\n\n  for (const [key, value] of Object.entries(headers)) {\n    if (key.toLowerCase() === lowerCasedHeader) {\n      if (Array.isArray(value)) {\n        if (value.length <= 1) return value[0];\n        console.warn(`Received ${value.length} entries for the ${header} header, using the first entry.`);\n        return value[0];\n      }\n      return value;\n    }\n  }\n\n  throw new Error(`Could not find ${header} header`);\n};\n\n/**\n * Encodes a string to Base64 format.\n */\nexport const toBase64 = (str: string | null | undefined): string => {\n  if (!str) return '';\n  if (typeof Buffer !== 'undefined') {\n    return Buffer.from(str).toString('base64');\n  }\n\n  if (typeof btoa !== 'undefined') {\n    return btoa(str);\n  }\n\n  throw new AnthropicError('Cannot generate b64 string; Expected `Buffer` or `btoa` to be defined');\n};\n\nexport function isObj(obj: unknown): obj is Record<string, unknown> {\n  return obj != null && typeof obj === 'object' && !Array.isArray(obj);\n}\n", "// File generated from our OpenAPI spec by Stainless.\n\nimport * as Core from \"./core\";\n\nexport class APIResource {\n  protected _client: Core.APIClient;\n\n  constructor(client: Core.APIClient) {\n    this._client = client;\n  }\n}\n", "// File generated from our OpenAPI spec by Stainless.\n\nimport * as Core from \"../core\";\nimport { APIPromise } from \"../core\";\nimport { APIResource } from \"../resource\";\nimport * as CompletionsAPI from \"./completions\";\nimport { Stream } from \"../streaming\";\n\nexport class Completions extends APIResource {\n  /**\n   * [Legacy] Create a Text Completion.\n   *\n   * The Text Completions API is a legacy API. We recommend using the\n   * [Messages API](https://docs.anthropic.com/claude/reference/messages_post) going\n   * forward.\n   *\n   * Future models and features will not be compatible with Text Completions. See our\n   * [migration guide](https://docs.anthropic.com/claude/reference/migrating-from-text-completions-to-messages)\n   * for guidance in migrating from Text Completions to Messages.\n   */\n  create(body: CompletionCreateParamsNonStreaming, options?: Core.RequestOptions): APIPromise<Completion>;\n  create(\n    body: CompletionCreateParamsStreaming,\n    options?: Core.RequestOptions,\n  ): APIPromise<Stream<Completion>>;\n  create(\n    body: CompletionCreateParamsBase,\n    options?: Core.RequestOptions,\n  ): APIPromise<Stream<Completion> | Completion>;\n  create(\n    body: CompletionCreateParams,\n    options?: Core.RequestOptions,\n  ): APIPromise<Completion> | APIPromise<Stream<Completion>> {\n    return this._client.post('/v1/complete', {\n      body,\n      timeout: 600000,\n      ...options,\n      stream: body.stream ?? false,\n    }) as APIPromise<Completion> | APIPromise<Stream<Completion>>;\n  }\n}\n\nexport interface Completion {\n  /**\n   * Unique object identifier.\n   *\n   * The format and length of IDs may change over time.\n   */\n  id: string;\n\n  /**\n   * The resulting completion up to and excluding the stop sequences.\n   */\n  completion: string;\n\n  /**\n   * The model that handled the request.\n   */\n  model: string;\n\n  /**\n   * The reason that we stopped.\n   *\n   * This may be one the following values:\n   *\n   * - `\"stop_sequence\"`: we reached a stop sequence — either provided by you via the\n   *   `stop_sequences` parameter, or a stop sequence built into the model\n   * - `\"max_tokens\"`: we exceeded `max_tokens_to_sample` or the model's maximum\n   */\n  stop_reason: string | null;\n\n  /**\n   * Object type.\n   *\n   * For Text Completions, this is always `\"completion\"`.\n   */\n  type: 'completion';\n}\n\nexport type CompletionCreateParams = CompletionCreateParamsNonStreaming | CompletionCreateParamsStreaming;\n\nexport interface CompletionCreateParamsBase {\n  /**\n   * The maximum number of tokens to generate before stopping.\n   *\n   * Note that our models may stop _before_ reaching this maximum. This parameter\n   * only specifies the absolute maximum number of tokens to generate.\n   */\n  max_tokens_to_sample: number;\n\n  /**\n   * The model that will complete your prompt.\n   *\n   * See [models](https://docs.anthropic.com/claude/docs/models-overview) for\n   * additional details and options.\n   */\n  model: (string & {}) | 'claude-2.0' | 'claude-2.1' | 'claude-instant-1.2';\n\n  /**\n   * The prompt that you want Claude to complete.\n   *\n   * For proper response generation you will need to format your prompt using\n   * alternating `\\n\\nHuman:` and `\\n\\nAssistant:` conversational turns. For example:\n   *\n   * ```\n   * \"\\n\\nHuman: {userQuestion}\\n\\nAssistant:\"\n   * ```\n   *\n   * See\n   * [prompt validation](https://anthropic.readme.io/claude/reference/prompt-validation)\n   * and our guide to\n   * [prompt design](https://docs.anthropic.com/claude/docs/introduction-to-prompt-design)\n   * for more details.\n   */\n  prompt: string;\n\n  /**\n   * An object describing metadata about the request.\n   */\n  metadata?: CompletionCreateParams.Metadata;\n\n  /**\n   * Sequences that will cause the model to stop generating.\n   *\n   * Our models stop on `\"\\n\\nHuman:\"`, and may include additional built-in stop\n   * sequences in the future. By providing the stop_sequences parameter, you may\n   * include additional strings that will cause the model to stop generating.\n   */\n  stop_sequences?: Array<string>;\n\n  /**\n   * Whether to incrementally stream the response using server-sent events.\n   *\n   * See\n   * [streaming](https://docs.anthropic.com/claude/reference/text-completions-streaming)\n   * for details.\n   */\n  stream?: boolean;\n\n  /**\n   * Amount of randomness injected into the response.\n   *\n   * Defaults to `1.0`. Ranges from `0.0` to `1.0`. Use `temperature` closer to `0.0`\n   * for analytical / multiple choice, and closer to `1.0` for creative and\n   * generative tasks.\n   *\n   * Note that even with `temperature` of `0.0`, the results will not be fully\n   * deterministic.\n   */\n  temperature?: number;\n\n  /**\n   * Only sample from the top K options for each subsequent token.\n   *\n   * Used to remove \"long tail\" low probability responses.\n   * [Learn more technical details here](https://towardsdatascience.com/how-to-sample-from-language-models-682bceb97277).\n   *\n   * Recommended for advanced use cases only. You usually only need to use\n   * `temperature`.\n   */\n  top_k?: number;\n\n  /**\n   * Use nucleus sampling.\n   *\n   * In nucleus sampling, we compute the cumulative distribution over all the options\n   * for each subsequent token in decreasing probability order and cut it off once it\n   * reaches a particular probability specified by `top_p`. You should either alter\n   * `temperature` or `top_p`, but not both.\n   *\n   * Recommended for advanced use cases only. You usually only need to use\n   * `temperature`.\n   */\n  top_p?: number;\n}\n\nexport namespace CompletionCreateParams {\n  /**\n   * An object describing metadata about the request.\n   */\n  export interface Metadata {\n    /**\n     * An external identifier for the user who is associated with the request.\n     *\n     * This should be a uuid, hash value, or other opaque identifier. Anthropic may use\n     * this id to help detect abuse. Do not include any identifying information such as\n     * name, email address, or phone number.\n     */\n    user_id?: string | null;\n  }\n\n  export type CompletionCreateParamsNonStreaming = CompletionsAPI.CompletionCreateParamsNonStreaming;\n  export type CompletionCreateParamsStreaming = CompletionsAPI.CompletionCreateParamsStreaming;\n}\n\nexport interface CompletionCreateParamsNonStreaming extends CompletionCreateParamsBase {\n  /**\n   * Whether to incrementally stream the response using server-sent events.\n   *\n   * See\n   * [streaming](https://docs.anthropic.com/claude/reference/text-completions-streaming)\n   * for details.\n   */\n  stream?: false;\n}\n\nexport interface CompletionCreateParamsStreaming extends CompletionCreateParamsBase {\n  /**\n   * Whether to incrementally stream the response using server-sent events.\n   *\n   * See\n   * [streaming](https://docs.anthropic.com/claude/reference/text-completions-streaming)\n   * for details.\n   */\n  stream: true;\n}\n\nexport namespace Completions {\n  export import Completion = CompletionsAPI.Completion;\n  export import CompletionCreateParams = CompletionsAPI.CompletionCreateParams;\n  export import CompletionCreateParamsNonStreaming = CompletionsAPI.CompletionCreateParamsNonStreaming;\n  export import CompletionCreateParamsStreaming = CompletionsAPI.CompletionCreateParamsStreaming;\n}\n", "import * as Core from \"../core\";\nimport { AnthropicError, APIUserAbortError } from \"../error\";\nimport {\n  ContentBlock,\n  Messages,\n  Message,\n  MessageStreamEvent,\n  MessageParam,\n  MessageCreateParams,\n  MessageStreamParams,\n} from \"../resources/messages\";\nimport { type ReadableStream } from \"../_shims/index\";\nimport { Stream } from \"../streaming\";\n\nexport interface MessageStreamEvents {\n  connect: () => void;\n  streamEvent: (event: MessageStreamEvent, snapshot: Message) => void;\n  text: (textDelta: string, textSnapshot: string) => void;\n  message: (message: Message) => void;\n  contentBlock: (content: ContentBlock) => void;\n  finalMessage: (message: Message) => void;\n  error: (error: AnthropicError) => void;\n  abort: (error: APIUserAbortError) => void;\n  end: () => void;\n}\n\ntype MessageStreamEventListeners<Event extends keyof MessageStreamEvents> = {\n  listener: MessageStreamEvents[Event];\n  once?: boolean;\n}[];\n\nexport class MessageStream implements AsyncIterable<MessageStreamEvent> {\n  messages: MessageParam[] = [];\n  receivedMessages: Message[] = [];\n  #currentMessageSnapshot: Message | undefined;\n\n  controller: AbortController = new AbortController();\n\n  #connectedPromise: Promise<void>;\n  #resolveConnectedPromise: () => void = () => {};\n  #rejectConnectedPromise: (error: AnthropicError) => void = () => {};\n\n  #endPromise: Promise<void>;\n  #resolveEndPromise: () => void = () => {};\n  #rejectEndPromise: (error: AnthropicError) => void = () => {};\n\n  #listeners: { [Event in keyof MessageStreamEvents]?: MessageStreamEventListeners<Event> } = {};\n\n  #ended = false;\n  #errored = false;\n  #aborted = false;\n  #catchingPromiseCreated = false;\n\n  constructor() {\n    this.#connectedPromise = new Promise<void>((resolve, reject) => {\n      this.#resolveConnectedPromise = resolve;\n      this.#rejectConnectedPromise = reject;\n    });\n\n    this.#endPromise = new Promise<void>((resolve, reject) => {\n      this.#resolveEndPromise = resolve;\n      this.#rejectEndPromise = reject;\n    });\n\n    // Don't let these promises cause unhandled rejection errors.\n    // we will manually cause an unhandled rejection error later\n    // if the user hasn't registered any error listener or called\n    // any promise-returning method.\n    this.#connectedPromise.catch(() => {});\n    this.#endPromise.catch(() => {});\n  }\n\n  /**\n   * Intended for use on the frontend, consuming a stream produced with\n   * `.toReadableStream()` on the backend.\n   *\n   * Note that messages sent to the model do not appear in `.on('message')`\n   * in this context.\n   */\n  static fromReadableStream(stream: ReadableStream): MessageStream {\n    const runner = new MessageStream();\n    runner._run(() => runner._fromReadableStream(stream));\n    return runner;\n  }\n\n  static createMessage(\n    messages: Messages,\n    params: MessageStreamParams,\n    options?: Core.RequestOptions,\n  ): MessageStream {\n    const runner = new MessageStream();\n    for (const message of params.messages) {\n      runner._addMessageParam(message);\n    }\n    runner._run(() =>\n      runner._createMessage(\n        messages,\n        { ...params, stream: true },\n        { ...options, headers: { ...options?.headers, 'X-Stainless-Helper-Method': 'stream' } },\n      ),\n    );\n    return runner;\n  }\n\n  protected _run(executor: () => Promise<any>) {\n    executor().then(() => {\n      this._emitFinal();\n      this._emit('end');\n    }, this.#handleError);\n  }\n\n  protected _addMessageParam(message: MessageParam) {\n    this.messages.push(message);\n  }\n\n  protected _addMessage(message: Message, emit = true) {\n    this.receivedMessages.push(message);\n    if (emit) {\n      this._emit('message', message);\n    }\n  }\n\n  protected async _createMessage(\n    messages: Messages,\n    params: MessageCreateParams,\n    options?: Core.RequestOptions,\n  ): Promise<void> {\n    const signal = options?.signal;\n    if (signal) {\n      if (signal.aborted) this.controller.abort();\n      signal.addEventListener('abort', () => this.controller.abort());\n    }\n    this.#beginRequest();\n    const stream = await messages.create(\n      { ...params, stream: true },\n      { ...options, signal: this.controller.signal },\n    );\n    this._connected();\n    for await (const event of stream) {\n      this.#addStreamEvent(event);\n    }\n    if (stream.controller.signal?.aborted) {\n      throw new APIUserAbortError();\n    }\n    this.#endRequest();\n  }\n\n  protected _connected() {\n    if (this.ended) return;\n    this.#resolveConnectedPromise();\n    this._emit('connect');\n  }\n\n  get ended(): boolean {\n    return this.#ended;\n  }\n\n  get errored(): boolean {\n    return this.#errored;\n  }\n\n  get aborted(): boolean {\n    return this.#aborted;\n  }\n\n  abort() {\n    this.controller.abort();\n  }\n\n  /**\n   * Adds the listener function to the end of the listeners array for the event.\n   * No checks are made to see if the listener has already been added. Multiple calls passing\n   * the same combination of event and listener will result in the listener being added, and\n   * called, multiple times.\n   * @returns this MessageStream, so that calls can be chained\n   */\n  on<Event extends keyof MessageStreamEvents>(event: Event, listener: MessageStreamEvents[Event]): this {\n    const listeners: MessageStreamEventListeners<Event> =\n      this.#listeners[event] || (this.#listeners[event] = []);\n    listeners.push({ listener });\n    return this;\n  }\n\n  /**\n   * Removes the specified listener from the listener array for the event.\n   * off() will remove, at most, one instance of a listener from the listener array. If any single\n   * listener has been added multiple times to the listener array for the specified event, then\n   * off() must be called multiple times to remove each instance.\n   * @returns this MessageStream, so that calls can be chained\n   */\n  off<Event extends keyof MessageStreamEvents>(event: Event, listener: MessageStreamEvents[Event]): this {\n    const listeners = this.#listeners[event];\n    if (!listeners) return this;\n    const index = listeners.findIndex((l) => l.listener === listener);\n    if (index >= 0) listeners.splice(index, 1);\n    return this;\n  }\n\n  /**\n   * Adds a one-time listener function for the event. The next time the event is triggered,\n   * this listener is removed and then invoked.\n   * @returns this MessageStream, so that calls can be chained\n   */\n  once<Event extends keyof MessageStreamEvents>(event: Event, listener: MessageStreamEvents[Event]): this {\n    const listeners: MessageStreamEventListeners<Event> =\n      this.#listeners[event] || (this.#listeners[event] = []);\n    listeners.push({ listener, once: true });\n    return this;\n  }\n\n  /**\n   * This is similar to `.once()`, but returns a Promise that resolves the next time\n   * the event is triggered, instead of calling a listener callback.\n   * @returns a Promise that resolves the next time given event is triggered,\n   * or rejects if an error is emitted.  (If you request the 'error' event,\n   * returns a promise that resolves with the error).\n   *\n   * Example:\n   *\n   *   const message = await stream.emitted('message') // rejects if the stream errors\n   */\n  emitted<Event extends keyof MessageStreamEvents>(\n    event: Event,\n  ): Promise<\n    Parameters<MessageStreamEvents[Event]> extends [infer Param] ? Param\n    : Parameters<MessageStreamEvents[Event]> extends [] ? void\n    : Parameters<MessageStreamEvents[Event]>\n  > {\n    return new Promise((resolve, reject) => {\n      this.#catchingPromiseCreated = true;\n      if (event !== 'error') this.once('error', reject);\n      this.once(event, resolve as any);\n    });\n  }\n\n  async done(): Promise<void> {\n    this.#catchingPromiseCreated = true;\n    await this.#endPromise;\n  }\n\n  get currentMessage(): Message | undefined {\n    return this.#currentMessageSnapshot;\n  }\n\n  #getFinalMessage(): Message {\n    if (this.receivedMessages.length === 0) {\n      throw new AnthropicError('stream ended without producing a Message with role=assistant');\n    }\n    return this.receivedMessages.at(-1)!;\n  }\n\n  /**\n   * @returns a promise that resolves with the the final assistant Message response,\n   * or rejects if an error occurred or the stream ended prematurely without producing a Message.\n   */\n  async finalMessage(): Promise<Message> {\n    await this.done();\n    return this.#getFinalMessage();\n  }\n\n  #getFinalText(): string {\n    if (this.receivedMessages.length === 0) {\n      throw new AnthropicError('stream ended without producing a Message with role=assistant');\n    }\n    const textBlocks = this.receivedMessages\n      .at(-1)!\n      .content.filter((block) => block.type === 'text')\n      .map((block) => block.text);\n    if (textBlocks.length === 0) {\n      throw new AnthropicError('stream ended without producing a content block with type=text');\n    }\n    return textBlocks.join(' ');\n  }\n\n  /**\n   * @returns a promise that resolves with the the final assistant Message's text response, concatenated\n   * together if there are more than one text blocks.\n   * Rejects if an error occurred or the stream ended prematurely without producing a Message.\n   */\n  async finalText(): Promise<string> {\n    await this.done();\n    return this.#getFinalText();\n  }\n\n  #handleError = (error: unknown) => {\n    this.#errored = true;\n    if (error instanceof Error && error.name === 'AbortError') {\n      error = new APIUserAbortError();\n    }\n    if (error instanceof APIUserAbortError) {\n      this.#aborted = true;\n      return this._emit('abort', error);\n    }\n    if (error instanceof AnthropicError) {\n      return this._emit('error', error);\n    }\n    if (error instanceof Error) {\n      const anthropicError: AnthropicError = new AnthropicError(error.message);\n      // @ts-ignore\n      anthropicError.cause = error;\n      return this._emit('error', anthropicError);\n    }\n    return this._emit('error', new AnthropicError(String(error)));\n  };\n\n  protected _emit<Event extends keyof MessageStreamEvents>(\n    event: Event,\n    ...args: Parameters<MessageStreamEvents[Event]>\n  ) {\n    // make sure we don't emit any MessageStreamEvents after end\n    if (this.#ended) return;\n\n    if (event === 'end') {\n      this.#ended = true;\n      this.#resolveEndPromise();\n    }\n\n    const listeners: MessageStreamEventListeners<Event> | undefined = this.#listeners[event];\n    if (listeners) {\n      this.#listeners[event] = listeners.filter((l) => !l.once) as any;\n      listeners.forEach(({ listener }: any) => listener(...args));\n    }\n\n    if (event === 'abort') {\n      const error = args[0] as APIUserAbortError;\n      if (!this.#catchingPromiseCreated && !listeners?.length) {\n        Promise.reject(error);\n      }\n      this.#rejectConnectedPromise(error);\n      this.#rejectEndPromise(error);\n      this._emit('end');\n      return;\n    }\n\n    if (event === 'error') {\n      // NOTE: _emit('error', error) should only be called from #handleError().\n\n      const error = args[0] as AnthropicError;\n      if (!this.#catchingPromiseCreated && !listeners?.length) {\n        // Trigger an unhandled rejection if the user hasn't registered any error handlers.\n        // If you are seeing stack traces here, make sure to handle errors via either:\n        // - runner.on('error', () => ...)\n        // - await runner.done()\n        // - await runner.final...()\n        // - etc.\n        Promise.reject(error);\n      }\n      this.#rejectConnectedPromise(error);\n      this.#rejectEndPromise(error);\n      this._emit('end');\n    }\n  }\n\n  protected _emitFinal() {\n    const finalMessage = this.receivedMessages.at(-1);\n    if (finalMessage) {\n      this._emit('finalMessage', this.#getFinalMessage());\n    }\n  }\n\n  #beginRequest() {\n    if (this.ended) return;\n    this.#currentMessageSnapshot = undefined;\n  }\n  #addStreamEvent(event: MessageStreamEvent) {\n    if (this.ended) return;\n    const messageSnapshot = this.#accumulateMessage(event);\n    this._emit('streamEvent', event, messageSnapshot);\n\n    switch (event.type) {\n      case 'content_block_delta': {\n        if (event.delta.type === 'text_delta') {\n          this._emit('text', event.delta.text, messageSnapshot.content.at(-1)!.text || '');\n        }\n        break;\n      }\n      case 'message_stop': {\n        this._addMessageParam(messageSnapshot);\n        this._addMessage(messageSnapshot, true);\n        break;\n      }\n      case 'content_block_stop': {\n        this._emit('contentBlock', messageSnapshot.content.at(-1)!);\n        break;\n      }\n      case 'message_start': {\n        this.#currentMessageSnapshot = messageSnapshot;\n        break;\n      }\n      case 'content_block_start':\n      case 'message_delta':\n        break;\n    }\n  }\n  #endRequest(): Message {\n    if (this.ended) {\n      throw new AnthropicError(`stream has ended, this shouldn't happen`);\n    }\n    const snapshot = this.#currentMessageSnapshot;\n    if (!snapshot) {\n      throw new AnthropicError(`request ended without sending any chunks`);\n    }\n    this.#currentMessageSnapshot = undefined;\n    return snapshot;\n  }\n\n  protected async _fromReadableStream(\n    readableStream: ReadableStream,\n    options?: Core.RequestOptions,\n  ): Promise<void> {\n    const signal = options?.signal;\n    if (signal) {\n      if (signal.aborted) this.controller.abort();\n      signal.addEventListener('abort', () => this.controller.abort());\n    }\n    this.#beginRequest();\n    this._connected();\n    const stream = Stream.fromReadableStream<MessageStreamEvent>(readableStream, this.controller);\n    for await (const event of stream) {\n      this.#addStreamEvent(event);\n    }\n    if (stream.controller.signal?.aborted) {\n      throw new APIUserAbortError();\n    }\n    this.#endRequest();\n  }\n\n  /**\n   * Mutates this.#currentMessage with the current event. Handling the accumulation of multiple messages\n   * will be needed to be handled by the caller, this method will throw if you try to accumulate for multiple\n   * messages.\n   */\n  #accumulateMessage(event: MessageStreamEvent): Message {\n    let snapshot = this.#currentMessageSnapshot;\n\n    if (event.type === 'message_start') {\n      if (snapshot) {\n        throw new AnthropicError(`Unexpected event order, got ${event.type} before receiving \"message_stop\"`);\n      }\n      return event.message;\n    }\n\n    if (!snapshot) {\n      throw new AnthropicError(`Unexpected event order, got ${event.type} before \"message_start\"`);\n    }\n\n    switch (event.type) {\n      case 'message_stop':\n        return snapshot;\n      case 'message_delta':\n        snapshot.stop_reason = event.delta.stop_reason;\n        snapshot.stop_sequence = event.delta.stop_sequence;\n        return snapshot;\n      case 'content_block_start':\n        snapshot.content.push(event.content_block);\n        return snapshot;\n      case 'content_block_delta': {\n        const snapshotContent = snapshot.content.at(event.index);\n        if (snapshotContent?.type === 'text' && event.delta.type === 'text_delta') {\n          snapshotContent.text += event.delta.text;\n        }\n        return snapshot;\n      }\n      case 'content_block_stop':\n        return snapshot;\n    }\n  }\n\n  [Symbol.asyncIterator](): AsyncIterator<MessageStreamEvent> {\n    const pushQueue: MessageStreamEvent[] = [];\n    const readQueue: {\n      resolve: (chunk: MessageStreamEvent | undefined) => void;\n      reject: (error: unknown) => void;\n    }[] = [];\n    let done = false;\n\n    this.on('streamEvent', (event) => {\n      const reader = readQueue.shift();\n      if (reader) {\n        reader.resolve(event);\n      } else {\n        pushQueue.push(event);\n      }\n    });\n\n    this.on('end', () => {\n      done = true;\n      for (const reader of readQueue) {\n        reader.resolve(undefined);\n      }\n      readQueue.length = 0;\n    });\n\n    this.on('abort', (err) => {\n      done = true;\n      for (const reader of readQueue) {\n        reader.reject(err);\n      }\n      readQueue.length = 0;\n    });\n\n    this.on('error', (err) => {\n      done = true;\n      for (const reader of readQueue) {\n        reader.reject(err);\n      }\n      readQueue.length = 0;\n    });\n\n    return {\n      next: async (): Promise<IteratorResult<MessageStreamEvent>> => {\n        if (!pushQueue.length) {\n          if (done) {\n            return { value: undefined, done: true };\n          }\n          return new Promise<MessageStreamEvent | undefined>((resolve, reject) =>\n            readQueue.push({ resolve, reject }),\n          ).then((chunk) => (chunk ? { value: chunk, done: false } : { value: undefined, done: true }));\n        }\n        const chunk = pushQueue.shift()!;\n        return { value: chunk, done: false };\n      },\n      return: async () => {\n        this.abort();\n        return { value: undefined, done: true };\n      },\n    };\n  }\n\n  toReadableStream(): ReadableStream {\n    const stream = new Stream(this[Symbol.asyncIterator].bind(this), this.controller);\n    return stream.toReadableStream();\n  }\n}\n", "// File generated from our OpenAPI spec by Stainless.\n\nimport * as Core from \"../core\";\nimport { APIPromise } from \"../core\";\nimport { APIResource } from \"../resource\";\nimport { MessageStream } from \"../lib/MessageStream\";\nexport { MessageStream } from \"../lib/MessageStream\";\nimport * as MessagesAPI from \"./messages\";\nimport { Stream } from \"../streaming\";\n\nexport class Messages extends APIResource {\n  /**\n   * Create a Message.\n   *\n   * Send a structured list of input messages with text and/or image content, and the\n   * model will generate the next message in the conversation.\n   *\n   * The Messages API can be used for for either single queries or stateless\n   * multi-turn conversations.\n   */\n  create(body: MessageCreateParamsNonStreaming, options?: Core.RequestOptions): APIPromise<Message>;\n  create(\n    body: MessageCreateParamsStreaming,\n    options?: Core.RequestOptions,\n  ): APIPromise<Stream<MessageStreamEvent>>;\n  create(\n    body: MessageCreateParamsBase,\n    options?: Core.RequestOptions,\n  ): APIPromise<Stream<MessageStreamEvent> | Message>;\n  create(\n    body: MessageCreateParams,\n    options?: Core.RequestOptions,\n  ): APIPromise<Message> | APIPromise<Stream<MessageStreamEvent>> {\n    return this._client.post('/v1/messages', {\n      body,\n      timeout: 600000,\n      ...options,\n      stream: body.stream ?? false,\n    }) as APIPromise<Message> | APIPromise<Stream<MessageStreamEvent>>;\n  }\n\n  /**\n   * Create a Message stream\n   */\n  stream(body: MessageStreamParams, options?: Core.RequestOptions): MessageStream {\n    return MessageStream.createMessage(this, body, options);\n  }\n}\n\nexport interface ContentBlock {\n  text: string;\n\n  type: 'text';\n}\n\nexport interface ContentBlockDeltaEvent {\n  delta: TextDelta;\n\n  index: number;\n\n  type: 'content_block_delta';\n}\n\nexport interface ContentBlockStartEvent {\n  content_block: ContentBlock;\n\n  index: number;\n\n  type: 'content_block_start';\n}\n\nexport interface ContentBlockStopEvent {\n  index: number;\n\n  type: 'content_block_stop';\n}\n\nexport interface ImageBlockParam {\n  source: ImageBlockParam.Source;\n\n  type?: 'image';\n}\n\nexport namespace ImageBlockParam {\n  export interface Source {\n    data: string;\n\n    media_type: 'image/jpeg' | 'image/png' | 'image/gif' | 'image/webp';\n\n    type?: 'base64';\n  }\n}\n\nexport interface Message {\n  /**\n   * Unique object identifier.\n   *\n   * The format and length of IDs may change over time.\n   */\n  id: string;\n\n  /**\n   * Content generated by the model.\n   *\n   * This is an array of content blocks, each of which has a `type` that determines\n   * its shape. Currently, the only `type` in responses is `\"text\"`.\n   *\n   * Example:\n   *\n   * ```json\n   * [{ \"type\": \"text\", \"text\": \"Hi, I'm Claude.\" }]\n   * ```\n   *\n   * If the request input `messages` ended with an `assistant` turn, then the\n   * response `content` will continue directly from that last turn. You can use this\n   * to constrain the model's output.\n   *\n   * For example, if the input `messages` were:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"role\": \"user\",\n   *     \"content\": \"What's the Greek name for Sun? (A) Sol (B) Helios (C) Sun\"\n   *   },\n   *   { \"role\": \"assistant\", \"content\": \"The best answer is (\" }\n   * ]\n   * ```\n   *\n   * Then the response `content` might be:\n   *\n   * ```json\n   * [{ \"type\": \"text\", \"text\": \"B)\" }]\n   * ```\n   */\n  content: Array<ContentBlock>;\n\n  /**\n   * The model that handled the request.\n   */\n  model: string;\n\n  /**\n   * Conversational role of the generated message.\n   *\n   * This will always be `\"assistant\"`.\n   */\n  role: 'assistant';\n\n  /**\n   * The reason that we stopped.\n   *\n   * This may be one the following values:\n   *\n   * - `\"end_turn\"`: the model reached a natural stopping point\n   * - `\"max_tokens\"`: we exceeded the requested `max_tokens` or the model's maximum\n   * - `\"stop_sequence\"`: one of your provided custom `stop_sequences` was generated\n   *\n   * Note that these values are different than those in `/v1/complete`, where\n   * `end_turn` and `stop_sequence` were not differentiated.\n   *\n   * In non-streaming mode this value is always non-null. In streaming mode, it is\n   * null in the `message_start` event and non-null otherwise.\n   */\n  stop_reason: 'end_turn' | 'max_tokens' | 'stop_sequence' | null;\n\n  /**\n   * Which custom stop sequence was generated, if any.\n   *\n   * This value will be a non-null string if one of your custom stop sequences was\n   * generated.\n   */\n  stop_sequence: string | null;\n\n  /**\n   * Object type.\n   *\n   * For Messages, this is always `\"message\"`.\n   */\n  type: 'message';\n\n  /**\n   * Billing and rate-limit usage.\n   *\n   * Anthropic's API bills and rate-limits by token counts, as tokens represent the\n   * underlying cost to our systems.\n   *\n   * Under the hood, the API transforms requests into a format suitable for the\n   * model. The model's output then goes through a parsing stage before becoming an\n   * API response. As a result, the token counts in `usage` will not match one-to-one\n   * with the exact visible content of an API request or response.\n   *\n   * For example, `output_tokens` will be non-zero, even for an empty string response\n   * from Claude.\n   */\n  usage: Usage;\n}\n\nexport interface MessageDeltaEvent {\n  delta: MessageDeltaEvent.Delta;\n\n  type: 'message_delta';\n\n  /**\n   * Billing and rate-limit usage.\n   *\n   * Anthropic's API bills and rate-limits by token counts, as tokens represent the\n   * underlying cost to our systems.\n   *\n   * Under the hood, the API transforms requests into a format suitable for the\n   * model. The model's output then goes through a parsing stage before becoming an\n   * API response. As a result, the token counts in `usage` will not match one-to-one\n   * with the exact visible content of an API request or response.\n   *\n   * For example, `output_tokens` will be non-zero, even for an empty string response\n   * from Claude.\n   */\n  usage: MessageDeltaUsage;\n}\n\nexport namespace MessageDeltaEvent {\n  export interface Delta {\n    stop_reason: 'end_turn' | 'max_tokens' | 'stop_sequence' | null;\n\n    stop_sequence: string | null;\n  }\n}\n\nexport interface MessageDeltaUsage {\n  /**\n   * The cumulative number of output tokens which were used.\n   */\n  output_tokens: number;\n}\n\nexport interface MessageParam {\n  content: string | Array<TextBlock | ImageBlockParam>;\n\n  role: 'user' | 'assistant';\n}\n\nexport interface MessageStartEvent {\n  message: Message;\n\n  type: 'message_start';\n}\n\nexport interface MessageStopEvent {\n  type: 'message_stop';\n}\n\nexport type MessageStreamEvent =\n  | MessageStartEvent\n  | MessageDeltaEvent\n  | MessageStopEvent\n  | ContentBlockStartEvent\n  | ContentBlockDeltaEvent\n  | ContentBlockStopEvent;\n\nexport interface TextBlock {\n  text: string;\n\n  type?: 'text';\n}\n\nexport interface TextDelta {\n  text: string;\n\n  type: 'text_delta';\n}\n\nexport interface Usage {\n  /**\n   * The number of input tokens which were used.\n   */\n  input_tokens: number;\n\n  /**\n   * The number of output tokens which were used.\n   */\n  output_tokens: number;\n}\n\nexport type MessageCreateParams = MessageCreateParamsNonStreaming | MessageCreateParamsStreaming;\n\nexport interface MessageCreateParamsBase {\n  /**\n   * The maximum number of tokens to generate before stopping.\n   *\n   * Note that our models may stop _before_ reaching this maximum. This parameter\n   * only specifies the absolute maximum number of tokens to generate.\n   *\n   * Different models have different maximum values for this parameter. See\n   * [models](https://docs.anthropic.com/claude/docs/models-overview) for details.\n   */\n  max_tokens: number;\n\n  /**\n   * Input messages.\n   *\n   * Our models are trained to operate on alternating `user` and `assistant`\n   * conversational turns. When creating a new `Message`, you specify the prior\n   * conversational turns with the `messages` parameter, and the model then generates\n   * the next `Message` in the conversation.\n   *\n   * Each input message must be an object with a `role` and `content`. You can\n   * specify a single `user`-role message, or you can include multiple `user` and\n   * `assistant` messages. The first message must always use the `user` role.\n   *\n   * If the final message uses the `assistant` role, the response content will\n   * continue immediately from the content in that message. This can be used to\n   * constrain part of the model's response.\n   *\n   * Example with a single `user` message:\n   *\n   * ```json\n   * [{ \"role\": \"user\", \"content\": \"Hello, Claude\" }]\n   * ```\n   *\n   * Example with multiple conversational turns:\n   *\n   * ```json\n   * [\n   *   { \"role\": \"user\", \"content\": \"Hello there.\" },\n   *   { \"role\": \"assistant\", \"content\": \"Hi, I'm Claude. How can I help you?\" },\n   *   { \"role\": \"user\", \"content\": \"Can you explain LLMs in plain English?\" }\n   * ]\n   * ```\n   *\n   * Example with a partially-filled response from Claude:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"role\": \"user\",\n   *     \"content\": \"What's the Greek name for Sun? (A) Sol (B) Helios (C) Sun\"\n   *   },\n   *   { \"role\": \"assistant\", \"content\": \"The best answer is (\" }\n   * ]\n   * ```\n   *\n   * Each input message `content` may be either a single `string` or an array of\n   * content blocks, where each block has a specific `type`. Using a `string` for\n   * `content` is shorthand for an array of one content block of type `\"text\"`. The\n   * following input messages are equivalent:\n   *\n   * ```json\n   * { \"role\": \"user\", \"content\": \"Hello, Claude\" }\n   * ```\n   *\n   * ```json\n   * { \"role\": \"user\", \"content\": [{ \"type\": \"text\", \"text\": \"Hello, Claude\" }] }\n   * ```\n   *\n   * Starting with Claude 3 models, you can also send image content blocks:\n   *\n   * ```json\n   * {\n   *   \"role\": \"user\",\n   *   \"content\": [\n   *     {\n   *       \"type\": \"image\",\n   *       \"source\": {\n   *         \"type\": \"base64\",\n   *         \"media_type\": \"image/jpeg\",\n   *         \"data\": \"/9j/4AAQSkZJRg...\"\n   *       }\n   *     },\n   *     { \"type\": \"text\", \"text\": \"What is in this image?\" }\n   *   ]\n   * }\n   * ```\n   *\n   * We currently support the `base64` source type for images, and the `image/jpeg`,\n   * `image/png`, `image/gif`, and `image/webp` media types.\n   *\n   * See [examples](https://docs.anthropic.com/claude/reference/messages-examples)\n   * for more input examples.\n   *\n   * Note that if you want to include a\n   * [system prompt](https://docs.anthropic.com/claude/docs/system-prompts), you can\n   * use the top-level `system` parameter — there is no `\"system\"` role for input\n   * messages in the Messages API.\n   */\n  messages: Array<MessageParam>;\n\n  /**\n   * The model that will complete your prompt.\n   *\n   * See [models](https://docs.anthropic.com/claude/docs/models-overview) for\n   * additional details and options.\n   */\n  model:\n    | (string & {})\n    | 'claude-3-opus-20240229'\n    | 'claude-3-sonnet-20240229'\n    | \"claude-2.1'\"\n    | 'claude-2.0'\n    | 'claude-instant-1.2';\n\n  /**\n   * An object describing metadata about the request.\n   */\n  metadata?: MessageCreateParams.Metadata;\n\n  /**\n   * Custom text sequences that will cause the model to stop generating.\n   *\n   * Our models will normally stop when they have naturally completed their turn,\n   * which will result in a response `stop_reason` of `\"end_turn\"`.\n   *\n   * If you want the model to stop generating when it encounters custom strings of\n   * text, you can use the `stop_sequences` parameter. If the model encounters one of\n   * the custom sequences, the response `stop_reason` value will be `\"stop_sequence\"`\n   * and the response `stop_sequence` value will contain the matched stop sequence.\n   */\n  stop_sequences?: Array<string>;\n\n  /**\n   * Whether to incrementally stream the response using server-sent events.\n   *\n   * See [streaming](https://docs.anthropic.com/claude/reference/messages-streaming)\n   * for details.\n   */\n  stream?: boolean;\n\n  /**\n   * System prompt.\n   *\n   * A system prompt is a way of providing context and instructions to Claude, such\n   * as specifying a particular goal or role. See our\n   * [guide to system prompts](https://docs.anthropic.com/claude/docs/system-prompts).\n   */\n  system?: string;\n\n  /**\n   * Amount of randomness injected into the response.\n   *\n   * Defaults to `1.0`. Ranges from `0.0` to `1.0`. Use `temperature` closer to `0.0`\n   * for analytical / multiple choice, and closer to `1.0` for creative and\n   * generative tasks.\n   *\n   * Note that even with `temperature` of `0.0`, the results will not be fully\n   * deterministic.\n   */\n  temperature?: number;\n\n  /**\n   * Only sample from the top K options for each subsequent token.\n   *\n   * Used to remove \"long tail\" low probability responses.\n   * [Learn more technical details here](https://towardsdatascience.com/how-to-sample-from-language-models-682bceb97277).\n   *\n   * Recommended for advanced use cases only. You usually only need to use\n   * `temperature`.\n   */\n  top_k?: number;\n\n  /**\n   * Use nucleus sampling.\n   *\n   * In nucleus sampling, we compute the cumulative distribution over all the options\n   * for each subsequent token in decreasing probability order and cut it off once it\n   * reaches a particular probability specified by `top_p`. You should either alter\n   * `temperature` or `top_p`, but not both.\n   *\n   * Recommended for advanced use cases only. You usually only need to use\n   * `temperature`.\n   */\n  top_p?: number;\n}\n\nexport namespace MessageCreateParams {\n  /**\n   * An object describing metadata about the request.\n   */\n  export interface Metadata {\n    /**\n     * An external identifier for the user who is associated with the request.\n     *\n     * This should be a uuid, hash value, or other opaque identifier. Anthropic may use\n     * this id to help detect abuse. Do not include any identifying information such as\n     * name, email address, or phone number.\n     */\n    user_id?: string | null;\n  }\n\n  export type MessageCreateParamsNonStreaming = MessagesAPI.MessageCreateParamsNonStreaming;\n  export type MessageCreateParamsStreaming = MessagesAPI.MessageCreateParamsStreaming;\n}\n\nexport interface MessageCreateParamsNonStreaming extends MessageCreateParamsBase {\n  /**\n   * Whether to incrementally stream the response using server-sent events.\n   *\n   * See [streaming](https://docs.anthropic.com/claude/reference/messages-streaming)\n   * for details.\n   */\n  stream?: false;\n}\n\nexport interface MessageCreateParamsStreaming extends MessageCreateParamsBase {\n  /**\n   * Whether to incrementally stream the response using server-sent events.\n   *\n   * See [streaming](https://docs.anthropic.com/claude/reference/messages-streaming)\n   * for details.\n   */\n  stream: true;\n}\n\nexport interface MessageStreamParams {\n  /**\n   * The maximum number of tokens to generate before stopping.\n   *\n   * Note that our models may stop _before_ reaching this maximum. This parameter\n   * only specifies the absolute maximum number of tokens to generate.\n   *\n   * Different models have different maximum values for this parameter. See\n   * [models](https://docs.anthropic.com/claude/docs/models-overview) for details.\n   */\n  max_tokens: number;\n\n  /**\n   * Input messages.\n   *\n   * Our models are trained to operate on alternating `user` and `assistant`\n   * conversational turns. When creating a new `Message`, you specify the prior\n   * conversational turns with the `messages` parameter, and the model then generates\n   * the next `Message` in the conversation.\n   *\n   * Each input message must be an object with a `role` and `content`. You can\n   * specify a single `user`-role message, or you can include multiple `user` and\n   * `assistant` messages. The first message must always use the `user` role.\n   *\n   * If the final message uses the `assistant` role, the response content will\n   * continue immediately from the content in that message. This can be used to\n   * constrain part of the model's response.\n   *\n   * Example with a single `user` message:\n   *\n   * ```json\n   * [{ \"role\": \"user\", \"content\": \"Hello, Claude\" }]\n   * ```\n   *\n   * Example with multiple conversational turns:\n   *\n   * ```json\n   * [\n   *   { \"role\": \"user\", \"content\": \"Hello there.\" },\n   *   { \"role\": \"assistant\", \"content\": \"Hi, I'm Claude. How can I help you?\" },\n   *   { \"role\": \"user\", \"content\": \"Can you explain LLMs in plain English?\" }\n   * ]\n   * ```\n   *\n   * Example with a partially-filled response from Claude:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"role\": \"user\",\n   *     \"content\": \"What's the Greek name for Sun? (A) Sol (B) Helios (C) Sun\"\n   *   },\n   *   { \"role\": \"assistant\", \"content\": \"The best answer is (\" }\n   * ]\n   * ```\n   *\n   * Each input message `content` may be either a single `string` or an array of\n   * content blocks, where each block has a specific `type`. Using a `string` for\n   * `content` is shorthand for an array of one content block of type `\"text\"`. The\n   * following input messages are equivalent:\n   *\n   * ```json\n   * { \"role\": \"user\", \"content\": \"Hello, Claude\" }\n   * ```\n   *\n   * ```json\n   * { \"role\": \"user\", \"content\": [{ \"type\": \"text\", \"text\": \"Hello, Claude\" }] }\n   * ```\n   *\n   * Starting with Claude 3 models, you can also send image content blocks:\n   *\n   * ```json\n   * {\n   *   \"role\": \"user\",\n   *   \"content\": [\n   *     {\n   *       \"type\": \"image\",\n   *       \"source\": {\n   *         \"type\": \"base64\",\n   *         \"media_type\": \"image/jpeg\",\n   *         \"data\": \"/9j/4AAQSkZJRg...\"\n   *       }\n   *     },\n   *     { \"type\": \"text\", \"text\": \"What is in this image?\" }\n   *   ]\n   * }\n   * ```\n   *\n   * We currently support the `base64` source type for images, and the `image/jpeg`,\n   * `image/png`, `image/gif`, and `image/webp` media types.\n   *\n   * See [examples](https://docs.anthropic.com/claude/reference/messages-examples)\n   * for more input examples.\n   *\n   * Note that if you want to include a\n   * [system prompt](https://docs.anthropic.com/claude/docs/system-prompts), you can\n   * use the top-level `system` parameter — there is no `\"system\"` role for input\n   * messages in the Messages API.\n   */\n  messages: Array<MessageParam>;\n\n  /**\n   * The model that will complete your prompt.\n   *\n   * See [models](https://docs.anthropic.com/claude/docs/models-overview) for\n   * additional details and options.\n   */\n  model:\n    | (string & {})\n    | 'claude-3-opus-20240229'\n    | 'claude-3-sonnet-20240229'\n    | \"claude-2.1'\"\n    | 'claude-2.0'\n    | 'claude-instant-1.2';\n\n  /**\n   * An object describing metadata about the request.\n   */\n  metadata?: MessageStreamParams.Metadata;\n\n  /**\n   * Custom text sequences that will cause the model to stop generating.\n   *\n   * Our models will normally stop when they have naturally completed their turn,\n   * which will result in a response `stop_reason` of `\"end_turn\"`.\n   *\n   * If you want the model to stop generating when it encounters custom strings of\n   * text, you can use the `stop_sequences` parameter. If the model encounters one of\n   * the custom sequences, the response `stop_reason` value will be `\"stop_sequence\"`\n   * and the response `stop_sequence` value will contain the matched stop sequence.\n   */\n  stop_sequences?: Array<string>;\n\n  /**\n   * System prompt.\n   *\n   * A system prompt is a way of providing context and instructions to Claude, such\n   * as specifying a particular goal or role. See our\n   * [guide to system prompts](https://docs.anthropic.com/claude/docs/system-prompts).\n   */\n  system?: string;\n\n  /**\n   * Amount of randomness injected into the response.\n   *\n   * Defaults to `1.0`. Ranges from `0.0` to `1.0`. Use `temperature` closer to `0.0`\n   * for analytical / multiple choice, and closer to `1.0` for creative and\n   * generative tasks.\n   *\n   * Note that even with `temperature` of `0.0`, the results will not be fully\n   * deterministic.\n   */\n  temperature?: number;\n\n  /**\n   * Only sample from the top K options for each subsequent token.\n   *\n   * Used to remove \"long tail\" low probability responses.\n   * [Learn more technical details here](https://towardsdatascience.com/how-to-sample-from-language-models-682bceb97277).\n   *\n   * Recommended for advanced use cases only. You usually only need to use\n   * `temperature`.\n   */\n  top_k?: number;\n\n  /**\n   * Use nucleus sampling.\n   *\n   * In nucleus sampling, we compute the cumulative distribution over all the options\n   * for each subsequent token in decreasing probability order and cut it off once it\n   * reaches a particular probability specified by `top_p`. You should either alter\n   * `temperature` or `top_p`, but not both.\n   *\n   * Recommended for advanced use cases only. You usually only need to use\n   * `temperature`.\n   */\n  top_p?: number;\n}\n\nexport namespace MessageStreamParams {\n  /**\n   * An object describing metadata about the request.\n   */\n  export interface Metadata {\n    /**\n     * An external identifier for the user who is associated with the request.\n     *\n     * This should be a uuid, hash value, or other opaque identifier. Anthropic may use\n     * this id to help detect abuse. Do not include any identifying information such as\n     * name, email address, or phone number.\n     */\n    user_id?: string | null;\n  }\n}\n\nexport namespace Messages {\n  export import ContentBlock = MessagesAPI.ContentBlock;\n  export import ContentBlockDeltaEvent = MessagesAPI.ContentBlockDeltaEvent;\n  export import ContentBlockStartEvent = MessagesAPI.ContentBlockStartEvent;\n  export import ContentBlockStopEvent = MessagesAPI.ContentBlockStopEvent;\n  export import ImageBlockParam = MessagesAPI.ImageBlockParam;\n  export import Message = MessagesAPI.Message;\n  export import MessageDeltaEvent = MessagesAPI.MessageDeltaEvent;\n  export import MessageDeltaUsage = MessagesAPI.MessageDeltaUsage;\n  export import MessageParam = MessagesAPI.MessageParam;\n  export import MessageStartEvent = MessagesAPI.MessageStartEvent;\n  export import MessageStopEvent = MessagesAPI.MessageStopEvent;\n  export import MessageStreamEvent = MessagesAPI.MessageStreamEvent;\n  export import TextBlock = MessagesAPI.TextBlock;\n  export import TextDelta = MessagesAPI.TextDelta;\n  export import Usage = MessagesAPI.Usage;\n  export import MessageCreateParams = MessagesAPI.MessageCreateParams;\n  export import MessageCreateParamsNonStreaming = MessagesAPI.MessageCreateParamsNonStreaming;\n  export import MessageCreateParamsStreaming = MessagesAPI.MessageCreateParamsStreaming;\n  export import MessageStreamParams = MessagesAPI.MessageStreamParams;\n}\n", "// File generated from our OpenAPI spec by Stainless.\n\nimport * as Core from './core';\nimport * as Errors from './error';\nimport { type Agent } from './_shims/index';\nimport * as Uploads from './uploads';\nimport * as API from \"./resources/index\";\n\nexport interface ClientOptions {\n  /**\n   * Defaults to process.env['ANTHROPIC_API_KEY'].\n   */\n  apiKey?: string | null | undefined;\n\n  /**\n   * Defaults to process.env['ANTHROPIC_AUTH_TOKEN'].\n   */\n  authToken?: string | null | undefined;\n\n  /**\n   * Override the default base URL for the API, e.g., \"https://api.example.com/v2/\"\n   *\n   * Defaults to process.env['ANTHROPIC_BASE_URL'].\n   */\n  baseURL?: string | null | undefined;\n\n  /**\n   * The maximum amount of time (in milliseconds) that the client should wait for a response\n   * from the server before timing out a single request.\n   *\n   * Note that request timeouts are retried by default, so in a worst-case scenario you may wait\n   * much longer than this timeout before the promise succeeds or fails.\n   */\n  timeout?: number;\n\n  /**\n   * An HTTP agent used to manage HTTP(S) connections.\n   *\n   * If not provided, an agent will be constructed by default in the Node.js environment,\n   * otherwise no agent is used.\n   */\n  httpAgent?: Agent;\n\n  /**\n   * Specify a custom `fetch` function implementation.\n   *\n   * If not provided, we use `node-fetch` on Node.js and otherwise expect that `fetch` is\n   * defined globally.\n   */\n  fetch?: Core.Fetch | undefined;\n\n  /**\n   * The maximum number of times that the client will retry a request in case of a\n   * temporary failure, like a network error or a 5XX error from the server.\n   *\n   * @default 2\n   */\n  maxRetries?: number;\n\n  /**\n   * Default headers to include with every request to the API.\n   *\n   * These can be removed in individual requests by explicitly setting the\n   * header to `undefined` or `null` in request options.\n   */\n  defaultHeaders?: Core.Headers;\n\n  /**\n   * Default query parameters to include with every request to the API.\n   *\n   * These can be removed in individual requests by explicitly setting the\n   * param to `undefined` in request options.\n   */\n  defaultQuery?: Core.DefaultQuery;\n}\n\n/** API Client for interfacing with the Anthropic API. */\nexport class Anthropic extends Core.APIClient {\n  apiKey: string | null;\n  authToken: string | null;\n\n  private _options: ClientOptions;\n\n  /**\n   * API Client for interfacing with the Anthropic API.\n   *\n   * @param {string | null | undefined} [opts.apiKey=process.env['ANTHROPIC_API_KEY'] ?? null]\n   * @param {string | null | undefined} [opts.authToken=process.env['ANTHROPIC_AUTH_TOKEN'] ?? null]\n   * @param {string} [opts.baseURL=process.env['ANTHROPIC_BASE_URL'] ?? https://api.anthropic.com] - Override the default base URL for the API.\n   * @param {number} [opts.timeout=10 minutes] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.\n   * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.\n   * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.\n   * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.\n   * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.\n   * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.\n   */\n  constructor({\n    baseURL = Core.readEnv('ANTHROPIC_BASE_URL'),\n    apiKey = Core.readEnv('ANTHROPIC_API_KEY') ?? null,\n    authToken = Core.readEnv('ANTHROPIC_AUTH_TOKEN') ?? null,\n    ...opts\n  }: ClientOptions = {}) {\n    const options: ClientOptions = {\n      apiKey,\n      authToken,\n      ...opts,\n      baseURL: baseURL || `https://api.anthropic.com`,\n    };\n\n    super({\n      baseURL: options.baseURL!,\n      timeout: options.timeout ?? 600000 /* 10 minutes */,\n      httpAgent: options.httpAgent,\n      maxRetries: options.maxRetries,\n      fetch: options.fetch,\n    });\n    this._options = options;\n\n    this.apiKey = apiKey;\n    this.authToken = authToken;\n  }\n\n  completions: API.Completions = new API.Completions(this);\n  messages: API.Messages = new API.Messages(this);\n\n  protected override defaultQuery(): Core.DefaultQuery | undefined {\n    return this._options.defaultQuery;\n  }\n\n  protected override defaultHeaders(opts: Core.FinalRequestOptions): Core.Headers {\n    return {\n      ...super.defaultHeaders(opts),\n      'anthropic-version': '2023-06-01',\n      ...this._options.defaultHeaders,\n    };\n  }\n\n  protected override validateHeaders(headers: Core.Headers, customHeaders: Core.Headers) {\n    if (this.apiKey && headers['x-api-key']) {\n      return;\n    }\n    if (customHeaders['x-api-key'] === null) {\n      return;\n    }\n\n    if (this.authToken && headers['authorization']) {\n      return;\n    }\n    if (customHeaders['authorization'] === null) {\n      return;\n    }\n\n    throw new Error(\n      'Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the \"X-Api-Key\" or \"Authorization\" headers to be explicitly omitted',\n    );\n  }\n\n  protected override authHeaders(opts: Core.FinalRequestOptions): Core.Headers {\n    const apiKeyAuth = this.apiKeyAuth(opts);\n    const bearerAuth = this.bearerAuth(opts);\n\n    if (apiKeyAuth != null && !Core.isEmptyObj(apiKeyAuth)) {\n      return apiKeyAuth;\n    }\n\n    if (bearerAuth != null && !Core.isEmptyObj(bearerAuth)) {\n      return bearerAuth;\n    }\n    return {};\n  }\n\n  protected apiKeyAuth(opts: Core.FinalRequestOptions): Core.Headers {\n    if (this.apiKey == null) {\n      return {};\n    }\n    return { 'X-Api-Key': this.apiKey };\n  }\n\n  protected bearerAuth(opts: Core.FinalRequestOptions): Core.Headers {\n    if (this.authToken == null) {\n      return {};\n    }\n    return { Authorization: `Bearer ${this.authToken}` };\n  }\n\n  static Anthropic = this;\n  static HUMAN_PROMPT = '\\n\\nHuman:';\n  static AI_PROMPT = '\\n\\nAssistant:';\n\n  static AnthropicError = Errors.AnthropicError;\n  static APIError = Errors.APIError;\n  static APIConnectionError = Errors.APIConnectionError;\n  static APIConnectionTimeoutError = Errors.APIConnectionTimeoutError;\n  static APIUserAbortError = Errors.APIUserAbortError;\n  static NotFoundError = Errors.NotFoundError;\n  static ConflictError = Errors.ConflictError;\n  static RateLimitError = Errors.RateLimitError;\n  static BadRequestError = Errors.BadRequestError;\n  static AuthenticationError = Errors.AuthenticationError;\n  static InternalServerError = Errors.InternalServerError;\n  static PermissionDeniedError = Errors.PermissionDeniedError;\n  static UnprocessableEntityError = Errors.UnprocessableEntityError;\n}\n\nexport const { HUMAN_PROMPT, AI_PROMPT } = Anthropic;\n\nexport const {\n  AnthropicError,\n  APIError,\n  APIConnectionError,\n  APIConnectionTimeoutError,\n  APIUserAbortError,\n  NotFoundError,\n  ConflictError,\n  RateLimitError,\n  BadRequestError,\n  AuthenticationError,\n  InternalServerError,\n  PermissionDeniedError,\n  UnprocessableEntityError,\n} = Errors;\n\nexport import toFile = Uploads.toFile;\nexport import fileFromPath = Uploads.fileFromPath;\n\nexport namespace Anthropic {\n  // Helper functions\n  export import toFile = Uploads.toFile;\n  export import fileFromPath = Uploads.fileFromPath;\n\n  export import RequestOptions = Core.RequestOptions;\n\n  export import Completions = API.Completions;\n  export import Completion = API.Completion;\n  export import CompletionCreateParams = API.CompletionCreateParams;\n  export import CompletionCreateParamsNonStreaming = API.CompletionCreateParamsNonStreaming;\n  export import CompletionCreateParamsStreaming = API.CompletionCreateParamsStreaming;\n\n  export import Messages = API.Messages;\n  export import ContentBlock = API.ContentBlock;\n  export import ContentBlockDeltaEvent = API.ContentBlockDeltaEvent;\n  export import ContentBlockStartEvent = API.ContentBlockStartEvent;\n  export import ContentBlockStopEvent = API.ContentBlockStopEvent;\n  export import ImageBlockParam = API.ImageBlockParam;\n  export import Message = API.Message;\n  export import MessageDeltaEvent = API.MessageDeltaEvent;\n  export import MessageDeltaUsage = API.MessageDeltaUsage;\n  export import MessageParam = API.MessageParam;\n  export import MessageStartEvent = API.MessageStartEvent;\n  export import MessageStopEvent = API.MessageStopEvent;\n  export import MessageStreamEvent = API.MessageStreamEvent;\n  export import TextBlock = API.TextBlock;\n  export import TextDelta = API.TextDelta;\n  export import Usage = API.Usage;\n  export import MessageCreateParams = API.MessageCreateParams;\n  export import MessageCreateParamsNonStreaming = API.MessageCreateParamsNonStreaming;\n  export import MessageCreateParamsStreaming = API.MessageCreateParamsStreaming;\n  export import MessageStreamParams = API.MessageStreamParams;\n}\n\nexport default Anthropic;\n"], "mappings": ";;;;;AAAO,IAAM,UAAU;;;AC0BhB,IAAI,OAAO;AACX,IAAI,OAAkC;AACtC,IAAIA,SAAoC;AACxC,IAAIC,WAAwC;AAC5C,IAAIC,YAA0C;AAC9C,IAAIC,WAAwC;AAC5C,IAAIC,YAA0C;AAC9C,IAAIC,QAAkC;AACtC,IAAIC,QAAkC;AACtC,IAAIC,kBAAsD;AAC1D,IAAI,6BAA8E;AAClF,IAAI,kBAAwD;AAC5D,IAAI,eAAkD;AACtD,IAAI,iBAAsD;AAE3D,SAAU,SAAS,OAAc,UAA6B,EAAE,MAAM,MAAK,GAAE;AACjF,MAAI,MAAM;AACR,UAAM,IAAI,MACR,8CAA8C,MAAM,IAAI,2DAA2D;;AAGvH,MAAI,MAAM;AACR,UAAM,IAAI,MACR,2CAA2C,MAAM,IAAI,+CAA+C,IAAI,KAAK;;AAGjH,SAAO,QAAQ;AACf,SAAO,MAAM;AACb,EAAAP,SAAQ,MAAM;AACd,EAAAC,WAAU,MAAM;AAChB,EAAAC,YAAW,MAAM;AACjB,EAAAC,WAAU,MAAM;AAChB,EAAAC,YAAW,MAAM;AACjB,EAAAC,QAAO,MAAM;AACb,EAAAC,QAAO,MAAM;AACb,EAAAC,kBAAiB,MAAM;AACvB,+BAA6B,MAAM;AACnC,oBAAkB,MAAM;AACxB,iBAAe,MAAM;AACrB,mBAAiB,MAAM;AACzB;;;AC/DM,IAAO,gBAAP,MAAoB;EACxB,YAAmB,MAAS;AAAT,SAAA,OAAA;EAAY;EAC/B,KAAK,OAAO,WAAW,IAAC;AACtB,WAAO;EACT;;;;ACAI,SAAU,WAAW,EAAE,iBAAgB,IAAqC,CAAA,GAAE;AAClF,QAAM,iBACJ,mBACE,kCACA;;;;AAKJ,MAAI,QAAQ,UAAU,WAAW;AACjC,MAAI;AAEF,aAAS;AAET,eAAW;AAEX,gBAAY;AAEZ,eAAW;WACJ,OAAO;AACd,UAAM,IAAI,MACR,iEACG,MAAc,OACjB,KAAK,cAAc,EAAE;;AAIzB,SAAO;IACL,MAAM;IACN,OAAO;IACP,SAAS;IACT,UAAU;IACV,SAAS;IACT;;MAEE,OAAO,aAAa,cAAc,WAChC,MAAM,SAAQ;;QAEZ,cAAA;AACE,gBAAM,IAAI,MACR,qFAAqF,cAAc,EAAE;QAEzG;;;IAGN,MACE,OAAO,SAAS,cAAc,OAC5B,MAAM,KAAI;MACR,cAAA;AACE,cAAM,IAAI,MACR,iFAAiF,cAAc,EAAE;MAErG;;IAGN;;MAEE,OAAO,SAAS,cAAc,OAC5B,MAAM,KAAI;;QAER,cAAA;AACE,gBAAM,IAAI,MACR,iFAAiF,cAAc,EAAE;QAErG;;;IAGN;;MAEE,OAAO,mBAAmB,cAAc,iBACtC,MAAM,eAAc;;QAElB,cAAA;AACE,gBAAM,IAAI,MACR,uFAAuF,cAAc,EAAE;QAE3G;;;IAGN,4BAA4B,OAE1B,MACA,UACgC;MAChC,GAAG;MACH,MAAM,IAAI,cAAc,IAAI;;IAE9B,iBAAiB,CAAC,QAAgB;IAClC,cAAc,MAAK;AACjB,YAAM,IAAI,MACR,iKAAiK;IAErK;IACA,gBAAgB,CAAC,UAAe;;AAEpC;;;ACjGA,IAAI,CAAO,KAAM,CAAM,SAAc,WAAW,GAAG,EAAE,MAAM,KAAK,CAAC;;;ACLjE;;;;;;;;;;;;;;;;AAIM,IAAO,iBAAP,cAA8B,MAAK;;AAEnC,IAAO,WAAP,MAAO,kBAAiB,eAAc;EAK1C,YACE,QACA,OACA,SACA,SAA4B;AAE5B,UAAM,GAAG,UAAS,YAAY,QAAQ,OAAO,OAAO,CAAC,EAAE;AACvD,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,QAAQ;EACf;EAEQ,OAAO,YAAY,QAA4B,OAAY,SAA2B;AAC5F,UAAM,OACJ,+BAAO,WACL,OAAO,MAAM,YAAY,WACvB,MAAM,UACN,KAAK,UAAU,MAAM,OAAO,IAC9B,QAAQ,KAAK,UAAU,KAAK,IAC5B;AAEJ,QAAI,UAAU,KAAK;AACjB,aAAO,GAAG,MAAM,IAAI,GAAG;;AAEzB,QAAI,QAAQ;AACV,aAAO,GAAG,MAAM;;AAElB,QAAI,KAAK;AACP,aAAO;;AAET,WAAO;EACT;EAEA,OAAO,SACL,QACA,eACA,SACA,SAA4B;AAE5B,QAAI,CAAC,QAAQ;AACX,aAAO,IAAI,mBAAmB,EAAE,OAAO,YAAY,aAAa,EAAC,CAAE;;AAGrE,UAAM,QAAQ;AAEd,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,gBAAgB,QAAQ,OAAO,SAAS,OAAO;;AAG5D,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,oBAAoB,QAAQ,OAAO,SAAS,OAAO;;AAGhE,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,sBAAsB,QAAQ,OAAO,SAAS,OAAO;;AAGlE,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,cAAc,QAAQ,OAAO,SAAS,OAAO;;AAG1D,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,cAAc,QAAQ,OAAO,SAAS,OAAO;;AAG1D,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,yBAAyB,QAAQ,OAAO,SAAS,OAAO;;AAGrE,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,eAAe,QAAQ,OAAO,SAAS,OAAO;;AAG3D,QAAI,UAAU,KAAK;AACjB,aAAO,IAAI,oBAAoB,QAAQ,OAAO,SAAS,OAAO;;AAGhE,WAAO,IAAI,UAAS,QAAQ,OAAO,SAAS,OAAO;EACrD;;AAGI,IAAO,oBAAP,cAAiC,SAAQ;EAG7C,YAAY,EAAE,QAAO,IAA2B,CAAA,GAAE;AAChD,UAAM,QAAW,QAAW,WAAW,wBAAwB,MAAS;AAHxD,SAAA,SAAoB;EAItC;;AAGI,IAAO,qBAAP,cAAkC,SAAQ;EAG9C,YAAY,EAAE,SAAS,MAAK,GAAmD;AAC7E,UAAM,QAAW,QAAW,WAAW,qBAAqB,MAAS;AAHrD,SAAA,SAAoB;AAMpC,QAAI;AAAO,WAAK,QAAQ;EAC1B;;AAGI,IAAO,4BAAP,cAAyC,mBAAkB;EAC/D,YAAY,EAAE,QAAO,IAA2B,CAAA,GAAE;AAChD,UAAM,EAAE,SAAS,WAAW,qBAAoB,CAAE;EACpD;;AAGI,IAAO,kBAAP,cAA+B,SAAQ;EAA7C,cAAA;;AACoB,SAAA,SAAc;EAClC;;AAEM,IAAO,sBAAP,cAAmC,SAAQ;EAAjD,cAAA;;AACoB,SAAA,SAAc;EAClC;;AAEM,IAAO,wBAAP,cAAqC,SAAQ;EAAnD,cAAA;;AACoB,SAAA,SAAc;EAClC;;AAEM,IAAO,gBAAP,cAA6B,SAAQ;EAA3C,cAAA;;AACoB,SAAA,SAAc;EAClC;;AAEM,IAAO,gBAAP,cAA6B,SAAQ;EAA3C,cAAA;;AACoB,SAAA,SAAc;EAClC;;AAEM,IAAO,2BAAP,cAAwC,SAAQ;EAAtD,cAAA;;AACoB,SAAA,SAAc;EAClC;;AAEM,IAAO,iBAAP,cAA8B,SAAQ;EAA5C,cAAA;;AACoB,SAAA,SAAc;EAClC;;AAEM,IAAO,sBAAP,cAAmC,SAAQ;;;;ACnI3C,IAAO,SAAP,MAAO,QAAM;EAGjB,YACU,UACR,YAA2B;AADnB,SAAA,WAAA;AAGR,SAAK,aAAa;EACpB;EAEA,OAAO,gBAAsB,UAAoB,YAA2B;AAC1E,QAAI,WAAW;AACf,UAAM,UAAU,IAAI,WAAU;AAE9B,oBAAgB,eAAY;AAC1B,UAAI,CAAC,SAAS,MAAM;AAClB,mBAAW,MAAK;AAChB,cAAM,IAAI,eAAe,mDAAmD;;AAG9E,YAAM,cAAc,IAAI,YAAW;AAEnC,YAAM,OAAO,4BAAmC,SAAS,IAAI;AAC7D,uBAAiB,SAAS,MAAM;AAC9B,mBAAW,QAAQ,YAAY,OAAO,KAAK,GAAG;AAC5C,gBAAM,MAAM,QAAQ,OAAO,IAAI;AAC/B,cAAI;AAAK,kBAAM;;;AAInB,iBAAW,QAAQ,YAAY,MAAK,GAAI;AACtC,cAAM,MAAM,QAAQ,OAAO,IAAI;AAC/B,YAAI;AAAK,gBAAM;;IAEnB;AAEA,oBAAgB,WAAQ;AACtB,UAAI,UAAU;AACZ,cAAM,IAAI,MAAM,0EAA0E;;AAE5F,iBAAW;AACX,UAAI,OAAO;AACX,UAAI;AACF,yBAAiB,OAAO,aAAY,GAAI;AACtC,cAAI,IAAI,UAAU,cAAc;AAC9B,gBAAI;AACF,oBAAM,KAAK,MAAM,IAAI,IAAI;qBAClB,GAAG;AACV,sBAAQ,MAAM,sCAAsC,IAAI,IAAI;AAC5D,sBAAQ,MAAM,eAAe,IAAI,GAAG;AACpC,oBAAM;;;AAIV,cACE,IAAI,UAAU,mBACd,IAAI,UAAU,mBACd,IAAI,UAAU,kBACd,IAAI,UAAU,yBACd,IAAI,UAAU,yBACd,IAAI,UAAU,sBACd;AACA,gBAAI;AACF,oBAAM,KAAK,MAAM,IAAI,IAAI;qBAClB,GAAG;AACV,sBAAQ,MAAM,sCAAsC,IAAI,IAAI;AAC5D,sBAAQ,MAAM,eAAe,IAAI,GAAG;AACpC,oBAAM;;;AAIV,cAAI,IAAI,UAAU,QAAQ;AACxB;;AAGF,cAAI,IAAI,UAAU,SAAS;AACzB,kBAAM,UAAU,IAAI;AACpB,kBAAM,UAAU,SAAS,OAAO;AAChC,kBAAM,aAAa,UAAU,SAAY;AAEzC,kBAAM,SAAS,SAAS,QAAW,SAAS,YAAY,sBAAsB,SAAS,OAAO,CAAC;;;AAGnG,eAAO;eACA,GAAG;AAEV,YAAI,aAAa,SAAS,EAAE,SAAS;AAAc;AACnD,cAAM;;AAGN,YAAI,CAAC;AAAM,qBAAW,MAAK;;IAE/B;AAEA,WAAO,IAAI,QAAO,UAAU,UAAU;EACxC;;;;;EAMA,OAAO,mBAAyB,gBAAgC,YAA2B;AACzF,QAAI,WAAW;AAEf,oBAAgB,YAAS;AACvB,YAAM,cAAc,IAAI,YAAW;AAEnC,YAAM,OAAO,4BAAmC,cAAc;AAC9D,uBAAiB,SAAS,MAAM;AAC9B,mBAAW,QAAQ,YAAY,OAAO,KAAK,GAAG;AAC5C,gBAAM;;;AAIV,iBAAW,QAAQ,YAAY,MAAK,GAAI;AACtC,cAAM;;IAEV;AAEA,oBAAgB,WAAQ;AACtB,UAAI,UAAU;AACZ,cAAM,IAAI,MAAM,0EAA0E;;AAE5F,iBAAW;AACX,UAAI,OAAO;AACX,UAAI;AACF,yBAAiB,QAAQ,UAAS,GAAI;AACpC,cAAI;AAAM;AACV,cAAI;AAAM,kBAAM,KAAK,MAAM,IAAI;;AAEjC,eAAO;eACA,GAAG;AAEV,YAAI,aAAa,SAAS,EAAE,SAAS;AAAc;AACnD,cAAM;;AAGN,YAAI,CAAC;AAAM,qBAAW,MAAK;;IAE/B;AAEA,WAAO,IAAI,QAAO,UAAU,UAAU;EACxC;EAEA,CAAC,OAAO,aAAa,IAAC;AACpB,WAAO,KAAK,SAAQ;EACtB;;;;;EAMA,MAAG;AACD,UAAM,OAA6C,CAAA;AACnD,UAAM,QAA8C,CAAA;AACpD,UAAM,WAAW,KAAK,SAAQ;AAE9B,UAAM,cAAc,CAAC,UAAoE;AACvF,aAAO;QACL,MAAM,MAAK;AACT,cAAI,MAAM,WAAW,GAAG;AACtB,kBAAM,SAAS,SAAS,KAAI;AAC5B,iBAAK,KAAK,MAAM;AAChB,kBAAM,KAAK,MAAM;;AAEnB,iBAAO,MAAM,MAAK;QACpB;;IAEJ;AAEA,WAAO;MACL,IAAI,QAAO,MAAM,YAAY,IAAI,GAAG,KAAK,UAAU;MACnD,IAAI,QAAO,MAAM,YAAY,KAAK,GAAG,KAAK,UAAU;;EAExD;;;;;;EAOA,mBAAgB;AACd,UAAM,OAAO;AACb,QAAI;AACJ,UAAM,UAAU,IAAI,YAAW;AAE/B,WAAO,IAAIC,gBAAe;MACxB,MAAM,QAAK;AACT,eAAO,KAAK,OAAO,aAAa,EAAC;MACnC;MACA,MAAM,KAAK,MAAI;AACb,YAAI;AACF,gBAAM,EAAE,OAAO,KAAI,IAAK,MAAM,KAAK,KAAI;AACvC,cAAI;AAAM,mBAAO,KAAK,MAAK;AAE3B,gBAAM,QAAQ,QAAQ,OAAO,KAAK,UAAU,KAAK,IAAI,IAAI;AAEzD,eAAK,QAAQ,KAAK;iBACX,KAAK;AACZ,eAAK,MAAM,GAAG;;MAElB;MACA,MAAM,SAAM;;AACV,gBAAMC,MAAA,KAAK,WAAL,gBAAAA,IAAA;MACR;KACD;EACH;;AAGF,IAAM,aAAN,MAAgB;EAKd,cAAA;AACE,SAAK,QAAQ;AACb,SAAK,OAAO,CAAA;AACZ,SAAK,SAAS,CAAA;EAChB;EAEA,OAAO,MAAY;AACjB,QAAI,KAAK,SAAS,IAAI,GAAG;AACvB,aAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;;AAG1C,QAAI,CAAC,MAAM;AAET,UAAI,CAAC,KAAK,SAAS,CAAC,KAAK,KAAK;AAAQ,eAAO;AAE7C,YAAM,MAAuB;QAC3B,OAAO,KAAK;QACZ,MAAM,KAAK,KAAK,KAAK,IAAI;QACzB,KAAK,KAAK;;AAGZ,WAAK,QAAQ;AACb,WAAK,OAAO,CAAA;AACZ,WAAK,SAAS,CAAA;AAEd,aAAO;;AAGT,SAAK,OAAO,KAAK,IAAI;AAErB,QAAI,KAAK,WAAW,GAAG,GAAG;AACxB,aAAO;;AAGT,QAAI,CAAC,WAAW,GAAG,KAAK,IAAI,UAAU,MAAM,GAAG;AAE/C,QAAI,MAAM,WAAW,GAAG,GAAG;AACzB,cAAQ,MAAM,UAAU,CAAC;;AAG3B,QAAI,cAAc,SAAS;AACzB,WAAK,QAAQ;eACJ,cAAc,QAAQ;AAC/B,WAAK,KAAK,KAAK,KAAK;;AAGtB,WAAO;EACT;;AASF,IAAM,cAAN,MAAM,aAAW;EASf,cAAA;AACE,SAAK,SAAS,CAAA;AACd,SAAK,aAAa;EACpB;EAEA,OAAO,OAAY;AACjB,QAAI,OAAO,KAAK,WAAW,KAAK;AAEhC,QAAI,KAAK,YAAY;AACnB,aAAO,OAAO;AACd,WAAK,aAAa;;AAEpB,QAAI,KAAK,SAAS,IAAI,GAAG;AACvB,WAAK,aAAa;AAClB,aAAO,KAAK,MAAM,GAAG,EAAE;;AAGzB,QAAI,CAAC,MAAM;AACT,aAAO,CAAA;;AAGT,UAAM,kBAAkB,aAAY,cAAc,IAAI,KAAK,KAAK,SAAS,CAAC,KAAK,EAAE;AACjF,QAAI,QAAQ,KAAK,MAAM,aAAY,cAAc;AAIjD,QAAI,iBAAiB;AACnB,YAAM,IAAG;;AAGX,QAAI,MAAM,WAAW,KAAK,CAAC,iBAAiB;AAC1C,WAAK,OAAO,KAAK,MAAM,CAAC,CAAE;AAC1B,aAAO,CAAA;;AAGT,QAAI,KAAK,OAAO,SAAS,GAAG;AAC1B,cAAQ,CAAC,KAAK,OAAO,KAAK,EAAE,IAAI,MAAM,CAAC,GAAG,GAAG,MAAM,MAAM,CAAC,CAAC;AAC3D,WAAK,SAAS,CAAA;;AAGhB,QAAI,CAAC,iBAAiB;AACpB,WAAK,SAAS,CAAC,MAAM,IAAG,KAAM,EAAE;;AAGlC,WAAO;EACT;EAEA,WAAW,OAAY;AACrB,QAAI,SAAS;AAAM,aAAO;AAC1B,QAAI,OAAO,UAAU;AAAU,aAAO;AAGtC,QAAI,OAAO,WAAW,aAAa;AACjC,UAAI,iBAAiB,QAAQ;AAC3B,eAAO,MAAM,SAAQ;;AAEvB,UAAI,iBAAiB,YAAY;AAC/B,eAAO,OAAO,KAAK,KAAK,EAAE,SAAQ;;AAGpC,YAAM,IAAI,eACR,wCAAwC,MAAM,YAAY,IAAI,mIAAmI;;AAKrM,QAAI,OAAO,gBAAgB,aAAa;AACtC,UAAI,iBAAiB,cAAc,iBAAiB,aAAa;AAC/D,aAAK,gBAAL,KAAK,cAAgB,IAAI,YAAY,MAAM;AAC3C,eAAO,KAAK,YAAY,OAAO,KAAK;;AAGtC,YAAM,IAAI,eACR,oDACG,MAAc,YAAY,IAC7B,gDAAgD;;AAIpD,UAAM,IAAI,eACR,gGAAgG;EAEpG;EAEA,QAAK;AACH,QAAI,CAAC,KAAK,OAAO,UAAU,CAAC,KAAK,YAAY;AAC3C,aAAO,CAAA;;AAGT,UAAM,QAAQ,CAAC,KAAK,OAAO,KAAK,EAAE,CAAC;AACnC,SAAK,SAAS,CAAA;AACd,SAAK,aAAa;AAClB,WAAO;EACT;;AApGO,YAAA,gBAAgB,oBAAI,IAAI,CAAC,MAAM,MAAM,MAAQ,MAAQ,KAAQ,KAAQ,KAAQ,KAAQ,UAAU,QAAQ,CAAC;AACxG,YAAA,iBAAiB;AAiH1B,SAAS,UAAU,KAAa,WAAiB;AAC/C,QAAM,QAAQ,IAAI,QAAQ,SAAS;AACnC,MAAI,UAAU,IAAI;AAChB,WAAO,CAAC,IAAI,UAAU,GAAG,KAAK,GAAG,WAAW,IAAI,UAAU,QAAQ,UAAU,MAAM,CAAC;;AAGrF,SAAO,CAAC,KAAK,IAAI,EAAE;AACrB;AAQM,SAAU,4BAA+B,QAAW;AACxD,MAAI,OAAO,OAAO,aAAa;AAAG,WAAO;AAEzC,QAAM,SAAS,OAAO,UAAS;AAC/B,SAAO;IACL,MAAM,OAAI;AACR,UAAI;AACF,cAAM,SAAS,MAAM,OAAO,KAAI;AAChC,YAAI,iCAAQ;AAAM,iBAAO,YAAW;AACpC,eAAO;eACA,GAAG;AACV,eAAO,YAAW;AAClB,cAAM;;IAEV;IACA,MAAM,SAAM;AACV,YAAM,gBAAgB,OAAO,OAAM;AACnC,aAAO,YAAW;AAClB,YAAM;AACN,aAAO,EAAE,MAAM,MAAM,OAAO,OAAS;IACvC;IACA,CAAC,OAAO,aAAa,IAAC;AACpB,aAAO;IACT;;AAEJ;;;AC5XO,IAAM,iBAAiB,CAAC,UAC7B,SAAS,QACT,OAAO,UAAU,YACjB,OAAO,MAAM,QAAQ,YACrB,OAAO,MAAM,SAAS;AAajB,IAAM,aAAa,CAAC,UACzB,SAAS,QACT,OAAO,UAAU,YACjB,OAAO,MAAM,SAAS,YACtB,OAAO,MAAM,SAAS,YACtB,OAAO,MAAM,SAAS,cACtB,OAAO,MAAM,UAAU,cACvB,OAAO,MAAM,gBAAgB;AAiB/B,eAAsB,OACpB,OACA,MACA,UAAuC,CAAA,GAAE;;AAGzC,UAAQ,MAAM;AAEd,MAAI,eAAe,KAAK,GAAG;AACzB,UAAM,OAAO,MAAM,MAAM,KAAI;AAC7B,aAAA,OAAS,IAAI,IAAI,MAAM,GAAG,EAAE,SAAS,MAAM,OAAO,EAAE,IAAG,KAAM;AAE7D,WAAO,IAAIC,MAAK,CAAC,IAAW,GAAG,MAAM,OAAO;;AAG9C,QAAM,OAAO,MAAM,SAAS,KAAK;AAEjC,WAAA,OAAS,QAAQ,KAAK,KAAK;AAE3B,MAAI,CAAC,QAAQ,MAAM;AACjB,UAAM,QAAQC,MAAA,KAAK,CAAC,MAAN,gBAAAA,IAAiB;AAC/B,QAAI,OAAO,SAAS,UAAU;AAC5B,gBAAU,EAAE,GAAG,SAAS,KAAI;;;AAIhC,SAAO,IAAID,MAAK,MAAM,MAAM,OAAO;AACrC;AAEA,eAAe,SAAS,OAAkB;;AACxC,MAAI,QAAyB,CAAA;AAC7B,MACE,OAAO,UAAU,YACjB,YAAY,OAAO,KAAK;EACxB,iBAAiB,aACjB;AACA,UAAM,KAAK,KAAK;aACP,WAAW,KAAK,GAAG;AAC5B,UAAM,KAAK,MAAM,MAAM,YAAW,CAAE;aAEpC,wBAAwB,KAAK,GAC7B;AACA,qBAAiB,SAAS,OAAO;AAC/B,YAAM,KAAK,KAAiB;;SAEzB;AACL,UAAM,IAAI,MACR,yBAAyB,OAAO,KAAK,mBAAkBC,MAAA,+BAAO,gBAAP,gBAAAA,IACnD,IAAI,YAAY,cAAc,KAAK,CAAC,EAAE;;AAI9C,SAAO;AACT;AAEA,SAAS,cAAc,OAAU;AAC/B,QAAM,QAAQ,OAAO,oBAAoB,KAAK;AAC9C,SAAO,IAAI,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;AAClD;AAEA,SAAS,QAAQ,OAAU;;AACzB,SACE,yBAAyB,MAAM,IAAI,KACnC,yBAAyB,MAAM,QAAQ;IAEvCA,MAAA,yBAAyB,MAAM,IAAI,MAAnC,gBAAAA,IAAsC,MAAM,SAAS;AAEzD;AAEA,IAAM,2BAA2B,CAAC,MAAoD;AACpF,MAAI,OAAO,MAAM;AAAU,WAAO;AAClC,MAAI,OAAO,WAAW,eAAe,aAAa;AAAQ,WAAO,OAAO,CAAC;AACzE,SAAO;AACT;AAEA,IAAM,0BAA0B,CAAC,UAC/B,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,MAAM,OAAO,aAAa,MAAM;AAEhF,IAAM,kBAAkB,CAAC,SAC9B,QAAQ,OAAO,SAAS,YAAY,KAAK,QAAQ,KAAK,OAAO,WAAW,MAAM;;;;;;;;;;;;;;;AC7IhF,eAAe,qBAAwB,OAAuB;AAC5D,QAAM,EAAE,SAAQ,IAAK;AACrB,MAAI,MAAM,QAAQ,QAAQ;AACxB,UAAM,YAAY,SAAS,QAAQ,SAAS,KAAK,SAAS,SAAS,SAAS,IAAI;AAKhF,QAAI,MAAM,QAAQ,eAAe;AAC/B,aAAO,MAAM,QAAQ,cAAc,gBAAgB,UAAU,MAAM,UAAU;;AAG/E,WAAO,OAAO,gBAAgB,UAAU,MAAM,UAAU;;AAI1D,MAAI,SAAS,WAAW,KAAK;AAC3B,WAAO;;AAGT,MAAI,MAAM,QAAQ,kBAAkB;AAClC,WAAO;;AAGT,QAAM,cAAc,SAAS,QAAQ,IAAI,cAAc;AACvD,QAAM,UACJ,2CAAa,SAAS,yBAAuB,2CAAa,SAAS;AACrE,MAAI,QAAQ;AACV,UAAM,OAAO,MAAM,SAAS,KAAI;AAEhC,UAAM,YAAY,SAAS,QAAQ,SAAS,KAAK,SAAS,SAAS,IAAI;AAEvE,WAAO;;AAGT,QAAM,OAAO,MAAM,SAAS,KAAI;AAChC,QAAM,YAAY,SAAS,QAAQ,SAAS,KAAK,SAAS,SAAS,IAAI;AAGvE,SAAO;AACT;AAMM,IAAO,aAAP,MAAO,oBAAsB,QAAU;EAG3C,YACU,iBACA,gBAAgE,sBAAoB;AAE5F,UAAM,CAAC,YAAW;AAIhB,cAAQ,IAAW;IACrB,CAAC;AARO,SAAA,kBAAA;AACA,SAAA,gBAAA;EAQV;EAEA,YAAe,WAAyB;AACtC,WAAO,IAAI,YAAW,KAAK,iBAAiB,OAAO,UAAU,UAAU,MAAM,KAAK,cAAc,KAAK,CAAC,CAAC;EACzG;;;;;;;;;;;;;;EAeA,aAAU;AACR,WAAO,KAAK,gBAAgB,KAAK,CAAC,MAAM,EAAE,QAAQ;EACpD;;;;;;;;;;;;;;EAcA,MAAM,eAAY;AAChB,UAAM,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,IAAI,CAAC,KAAK,MAAK,GAAI,KAAK,WAAU,CAAE,CAAC;AAC5E,WAAO,EAAE,MAAM,SAAQ;EACzB;EAEQ,QAAK;AACX,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,gBAAgB,KAAK,gBAAgB,KAAK,KAAK,aAAa;;AAEnE,WAAO,KAAK;EACd;EAES,KACP,aACA,YAAmF;AAEnF,WAAO,KAAK,MAAK,EAAG,KAAK,aAAa,UAAU;EAClD;EAES,MACP,YAAiF;AAEjF,WAAO,KAAK,MAAK,EAAG,MAAM,UAAU;EACtC;EAES,QAAQ,WAA2C;AAC1D,WAAO,KAAK,MAAK,EAAG,QAAQ,SAAS;EACvC;;AAGI,IAAgB,YAAhB,MAAyB;EAS7B,YAAY;IACV;IACA,aAAa;IACb,UAAU;;IACV;IACA,OAAO;EAAc,GAOtB;AACC,SAAK,UAAU;AACf,SAAK,aAAa,wBAAwB,cAAc,UAAU;AAClE,SAAK,UAAU,wBAAwB,WAAW,OAAO;AACzD,SAAK,YAAY;AAEjB,SAAK,QAAQ,kBAAkBC;EACjC;EAEU,YAAY,MAAyB;AAC7C,WAAO,CAAA;EACT;;;;;;;;;EAUU,eAAe,MAAyB;AAChD,WAAO;MACL,QAAQ;MACR,gBAAgB;MAChB,cAAc,KAAK,aAAY;MAC/B,GAAG,mBAAkB;MACrB,GAAG,KAAK,YAAY,IAAI;;EAE5B;;;;EAOU,gBAAgB,SAAkB,eAAsB;EAAG;EAE3D,wBAAqB;AAC7B,WAAO,wBAAwB,MAAK,CAAE;EACxC;EAEA,IAAc,MAAc,MAA0C;AACpE,WAAO,KAAK,cAAc,OAAO,MAAM,IAAI;EAC7C;EAEA,KAAe,MAAc,MAA0C;AACrE,WAAO,KAAK,cAAc,QAAQ,MAAM,IAAI;EAC9C;EAEA,MAAgB,MAAc,MAA0C;AACtE,WAAO,KAAK,cAAc,SAAS,MAAM,IAAI;EAC/C;EAEA,IAAc,MAAc,MAA0C;AACpE,WAAO,KAAK,cAAc,OAAO,MAAM,IAAI;EAC7C;EAEA,OAAiB,MAAc,MAA0C;AACvE,WAAO,KAAK,cAAc,UAAU,MAAM,IAAI;EAChD;EAEQ,cACN,QACA,MACA,MAA0C;AAE1C,WAAO,KAAK,QAAQ,QAAQ,QAAQ,IAAI,EAAE,KAAK,CAACC,WAAU,EAAE,QAAQ,MAAM,GAAGA,MAAI,EAAG,CAAC;EACvF;EAEA,WACE,MACA,MACA,MAA0B;AAE1B,WAAO,KAAK,eAAe,MAAM,EAAE,QAAQ,OAAO,MAAM,GAAG,KAAI,CAAE;EACnE;EAEQ,uBAAuB,MAAa;AAC1C,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,OAAO,WAAW,aAAa;AACjC,eAAO,OAAO,WAAW,MAAM,MAAM,EAAE,SAAQ;;AAGjD,UAAI,OAAO,gBAAgB,aAAa;AACtC,cAAM,UAAU,IAAI,YAAW;AAC/B,cAAM,UAAU,QAAQ,OAAO,IAAI;AACnC,eAAO,QAAQ,OAAO,SAAQ;;;AAIlC,WAAO;EACT;EAEA,aAAkB,SAAiC;;AACjD,UAAM,EAAE,QAAQ,MAAM,OAAO,UAAmB,CAAA,EAAE,IAAK;AAEvD,UAAM,OACJ,gBAAgB,QAAQ,IAAI,IAAI,QAAQ,KAAK,OAC3C,QAAQ,OAAO,KAAK,UAAU,QAAQ,MAAM,MAAM,CAAC,IACnD;AACJ,UAAM,gBAAgB,KAAK,uBAAuB,IAAI;AAEtD,UAAM,MAAM,KAAK,SAAS,MAAO,KAAK;AACtC,QAAI,aAAa;AAAS,8BAAwB,WAAW,QAAQ,OAAO;AAC5E,UAAM,UAAU,QAAQ,WAAW,KAAK;AACxC,UAAM,YAAY,QAAQ,aAAa,KAAK,aAAa,gBAAgB,GAAG;AAC5E,UAAM,kBAAkB,UAAU;AAClC,QACE,SAAQC,MAAA,uCAAmB,YAAnB,gBAAAA,IAA4B,aAAY,YAChD,mBAAoB,UAAkB,QAAQ,WAAW,IACzD;AAKC,gBAAkB,QAAQ,UAAU;;AAGvC,QAAI,KAAK,qBAAqB,WAAW,OAAO;AAC9C,UAAI,CAAC,QAAQ;AAAgB,gBAAQ,iBAAiB,KAAK,sBAAqB;AAChF,cAAQ,KAAK,iBAAiB,IAAI,QAAQ;;AAG5C,UAAM,aAAa,KAAK,aAAa,EAAE,SAAS,SAAS,cAAa,CAAE;AAExE,UAAM,MAAmB;MACvB;MACA,GAAI,QAAQ,EAAE,KAAiB;MAC/B,SAAS;MACT,GAAI,aAAa,EAAE,OAAO,UAAS;;;MAGnC,QAAQ,QAAQ,UAAU;;AAG5B,WAAO,EAAE,KAAK,KAAK,QAAO;EAC5B;EAEQ,aAAa,EACnB,SACA,SACA,cAAa,GAKd;AACC,UAAM,aAAqC,CAAA;AAC3C,QAAI,eAAe;AACjB,iBAAW,gBAAgB,IAAI;;AAGjC,UAAM,iBAAiB,KAAK,eAAe,OAAO;AAClD,oBAAgB,YAAY,cAAc;AAC1C,oBAAgB,YAAY,OAAO;AAGnC,QAAI,gBAAgB,QAAQ,IAAI,KAAK,SAAc,QAAQ;AACzD,aAAO,WAAW,cAAc;;AAGlC,SAAK,gBAAgB,YAAY,OAAO;AAExC,WAAO;EACT;;;;EAKU,MAAM,eAAe,SAA4B;EAAkB;;;;;;;EAQnE,MAAM,eACd,SACA,EAAE,KAAK,QAAO,GAAiD;EAC/C;EAER,aAAa,SAAuC;AAC5D,WACE,CAAC,UAAU,CAAA,IACT,OAAO,YAAY,UACnB,OAAO,YAAY,MAAM,KAAK,OAA6B,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,CAAC,IACzF,EAAE,GAAG,QAAO;EAElB;EAEU,gBACR,QACA,OACA,SACA,SAA4B;AAE5B,WAAO,SAAS,SAAS,QAAQ,OAAO,SAAS,OAAO;EAC1D;EAEA,QACE,SACA,mBAAkC,MAAI;AAEtC,WAAO,IAAI,WAAW,KAAK,YAAY,SAAS,gBAAgB,CAAC;EACnE;EAEQ,MAAM,YACZ,cACA,kBAA+B;;AAE/B,UAAM,UAAU,MAAM;AACtB,QAAI,oBAAoB,MAAM;AAC5B,yBAAmB,QAAQ,cAAc,KAAK;;AAGhD,UAAM,KAAK,eAAe,OAAO;AAEjC,UAAM,EAAE,KAAK,KAAK,QAAO,IAAK,KAAK,aAAa,OAAO;AAEvD,UAAM,KAAK,eAAe,KAAK,EAAE,KAAK,QAAO,CAAE;AAE/C,UAAM,WAAW,KAAK,SAAS,IAAI,OAAO;AAE1C,SAAIA,MAAA,QAAQ,WAAR,gBAAAA,IAAgB,SAAS;AAC3B,YAAM,IAAI,kBAAiB;;AAG7B,UAAM,aAAa,IAAI,gBAAe;AACtC,UAAM,WAAW,MAAM,KAAK,iBAAiB,KAAK,KAAK,SAAS,UAAU,EAAE,MAAM,WAAW;AAE7F,QAAI,oBAAoB,OAAO;AAC7B,WAAI,aAAQ,WAAR,mBAAgB,SAAS;AAC3B,cAAM,IAAI,kBAAiB;;AAE7B,UAAI,kBAAkB;AACpB,eAAO,KAAK,aAAa,SAAS,gBAAgB;;AAEpD,UAAI,SAAS,SAAS,cAAc;AAClC,cAAM,IAAI,0BAAyB;;AAErC,YAAM,IAAI,mBAAmB,EAAE,OAAO,SAAQ,CAAE;;AAGlD,UAAM,kBAAkB,sBAAsB,SAAS,OAAO;AAE9D,QAAI,CAAC,SAAS,IAAI;AAChB,UAAI,oBAAoB,KAAK,YAAY,QAAQ,GAAG;AAClD,cAAMC,gBAAe,aAAa,gBAAgB;AAClD,cAAM,oBAAoBA,aAAY,KAAK,SAAS,QAAQ,KAAK,eAAe;AAChF,eAAO,KAAK,aAAa,SAAS,kBAAkB,eAAe;;AAGrE,YAAM,UAAU,MAAM,SAAS,KAAI,EAAG,MAAM,CAAC,MAAM,YAAY,CAAC,EAAE,OAAO;AACzE,YAAM,UAAU,SAAS,OAAO;AAChC,YAAM,aAAa,UAAU,SAAY;AACzC,YAAM,eAAe,mBAAmB,kCAAkC;AAE1E,YAAM,oBAAoB,YAAY,KAAK,SAAS,QAAQ,KAAK,iBAAiB,UAAU;AAE5F,YAAM,MAAM,KAAK,gBAAgB,SAAS,QAAQ,SAAS,YAAY,eAAe;AACtF,YAAM;;AAGR,WAAO,EAAE,UAAU,SAAS,WAAU;EACxC;EAEA,eACE,MACA,SAA4B;AAE5B,UAAM,UAAU,KAAK,YAAY,SAAS,IAAI;AAC9C,WAAO,IAAI,YAA6B,MAAM,SAAS,IAAI;EAC7D;EAEA,SAAc,MAAc,OAA6B;AACvD,UAAM,MACJ,cAAc,IAAI,IAChB,IAAI,IAAI,IAAI,IACZ,IAAI,IAAI,KAAK,WAAW,KAAK,QAAQ,SAAS,GAAG,KAAK,KAAK,WAAW,GAAG,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK;AAEtG,UAAM,eAAe,KAAK,aAAY;AACtC,QAAI,CAAC,WAAW,YAAY,GAAG;AAC7B,cAAQ,EAAE,GAAG,cAAc,GAAG,MAAK;;AAGrC,QAAI,OAAO,UAAU,YAAY,SAAS,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC/D,UAAI,SAAS,KAAK,eAAe,KAAgC;;AAGnE,WAAO,IAAI,SAAQ;EACrB;EAEU,eAAe,OAA8B;AACrD,WAAO,OAAO,QAAQ,KAAK,EACxB,OAAO,CAAC,CAAC,GAAG,KAAK,MAAM,OAAO,UAAU,WAAW,EACnD,IAAI,CAAC,CAAC,KAAK,KAAK,MAAK;AACpB,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW;AACxF,eAAO,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,KAAK,CAAC;;AAEhE,UAAI,UAAU,MAAM;AAClB,eAAO,GAAG,mBAAmB,GAAG,CAAC;;AAEnC,YAAM,IAAI,eACR,yBAAyB,OAAO,KAAK,mQAAmQ;IAE5S,CAAC,EACA,KAAK,GAAG;EACb;EAEA,MAAM,iBACJ,KACA,MACA,IACA,YAA2B;AAE3B,UAAM,EAAE,QAAQ,GAAG,QAAO,IAAK,QAAQ,CAAA;AACvC,QAAI;AAAQ,aAAO,iBAAiB,SAAS,MAAM,WAAW,MAAK,CAAE;AAErE,UAAM,UAAU,WAAW,MAAM,WAAW,MAAK,GAAI,EAAE;AAEvD,WACE,KAAK,iBAAgB,EAElB,MAAM,KAAK,QAAW,KAAK,EAAE,QAAQ,WAAW,QAAe,GAAG,QAAO,CAAE,EAC3E,QAAQ,MAAK;AACZ,mBAAa,OAAO;IACtB,CAAC;EAEP;EAEU,mBAAgB;AACxB,WAAO,EAAE,OAAO,KAAK,MAAK;EAC5B;EAEQ,YAAY,UAAkB;AAEpC,UAAM,oBAAoB,SAAS,QAAQ,IAAI,gBAAgB;AAG/D,QAAI,sBAAsB;AAAQ,aAAO;AACzC,QAAI,sBAAsB;AAAS,aAAO;AAG1C,QAAI,SAAS,WAAW;AAAK,aAAO;AAGpC,QAAI,SAAS,WAAW;AAAK,aAAO;AAGpC,QAAI,SAAS,WAAW;AAAK,aAAO;AAGpC,QAAI,SAAS,UAAU;AAAK,aAAO;AAEnC,WAAO;EACT;EAEQ,MAAM,aACZ,SACA,kBACA,iBAAqC;AAErC,QAAI;AAGJ,UAAM,yBAAyB,mDAAkB;AACjD,QAAI,wBAAwB;AAC1B,YAAM,YAAY,WAAW,sBAAsB;AACnD,UAAI,CAAC,OAAO,MAAM,SAAS,GAAG;AAC5B,wBAAgB;;;AAKpB,UAAM,mBAAmB,mDAAkB;AAC3C,QAAI,oBAAoB,CAAC,eAAe;AACtC,YAAM,iBAAiB,WAAW,gBAAgB;AAClD,UAAI,CAAC,OAAO,MAAM,cAAc,GAAG;AACjC,wBAAgB,iBAAiB;aAC5B;AACL,wBAAgB,KAAK,MAAM,gBAAgB,IAAI,KAAK,IAAG;;;AAM3D,QAAI,EAAE,iBAAiB,KAAK,iBAAiB,gBAAgB,KAAK,MAAO;AACvE,YAAM,aAAa,QAAQ,cAAc,KAAK;AAC9C,sBAAgB,KAAK,mCAAmC,kBAAkB,UAAU;;AAEtF,UAAM,MAAM,aAAa;AAEzB,WAAO,KAAK,YAAY,SAAS,mBAAmB,CAAC;EACvD;EAEQ,mCAAmC,kBAA0B,YAAkB;AACrF,UAAM,oBAAoB;AAC1B,UAAM,gBAAgB;AAEtB,UAAM,aAAa,aAAa;AAGhC,UAAM,eAAe,KAAK,IAAI,oBAAoB,KAAK,IAAI,GAAG,UAAU,GAAG,aAAa;AAGxF,UAAM,SAAS,IAAI,KAAK,OAAM,IAAK;AAEnC,WAAO,eAAe,SAAS;EACjC;EAEQ,eAAY;AAClB,WAAO,GAAG,KAAK,YAAY,IAAI,OAAO,OAAO;EAC/C;;AAKI,IAAgB,eAAhB,MAA4B;EAOhC,YAAY,QAAmB,UAAoB,MAAe,SAA4B;AAN9F,yBAAA,IAAA,MAAA,MAAA;AAOE,2BAAA,MAAI,sBAAW,QAAM,GAAA;AACrB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,OAAO;EACd;EAUA,cAAW;AACT,UAAM,QAAQ,KAAK,kBAAiB;AACpC,QAAI,CAAC,MAAM;AAAQ,aAAO;AAC1B,WAAO,KAAK,aAAY,KAAM;EAChC;EAEA,MAAM,cAAW;AACf,UAAM,WAAW,KAAK,aAAY;AAClC,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,eACR,uFAAuF;;AAG3F,UAAM,cAAc,EAAE,GAAG,KAAK,QAAO;AACrC,QAAI,YAAY,YAAY,OAAO,YAAY,UAAU,UAAU;AACjE,kBAAY,QAAQ,EAAE,GAAG,YAAY,OAAO,GAAG,SAAS,OAAM;eACrD,SAAS,UAAU;AAC5B,YAAM,SAAS,CAAC,GAAG,OAAO,QAAQ,YAAY,SAAS,CAAA,CAAE,GAAG,GAAG,SAAS,IAAI,aAAa,QAAO,CAAE;AAClG,iBAAW,CAAC,KAAK,KAAK,KAAK,QAAQ;AACjC,iBAAS,IAAI,aAAa,IAAI,KAAK,KAAY;;AAEjD,kBAAY,QAAQ;AACpB,kBAAY,OAAO,SAAS,IAAI,SAAQ;;AAE1C,WAAO,MAAM,uBAAA,MAAI,sBAAA,GAAA,EAAS,eAAe,KAAK,aAAoB,WAAW;EAC/E;EAEA,OAAO,YAAS;AAEd,QAAI,OAA2B;AAC/B,UAAM;AACN,WAAO,KAAK,YAAW,GAAI;AACzB,aAAO,MAAM,KAAK,YAAW;AAC7B,YAAM;;EAEV;EAEA,SAAO,uBAAA,oBAAA,QAAA,GAAC,OAAO,cAAa,IAAC;AAC3B,qBAAiB,QAAQ,KAAK,UAAS,GAAI;AACzC,iBAAW,QAAQ,KAAK,kBAAiB,GAAI;AAC3C,cAAM;;;EAGZ;;AAYI,IAAO,cAAP,cAII,WAAqB;EAG7B,YACE,QACA,SACA,MAA4E;AAE5E,UACE,SACA,OAAO,UAAU,IAAI,KAAK,QAAQ,MAAM,UAAU,MAAM,qBAAqB,KAAK,GAAG,MAAM,OAAO,CAAC;EAEvG;;;;;;;;EASA,QAAQ,OAAO,aAAa,IAAC;AAC3B,UAAM,OAAO,MAAM;AACnB,qBAAiB,QAAQ,MAAM;AAC7B,YAAM;;EAEV;;AAGK,IAAM,wBAAwB,CACnC,YAC0B;AAC1B,SAAO,IAAI,MACT,OAAO;;IAEL,QAAQ,QAAO;EAAE,GAEnB;IACE,IAAI,QAAQ,MAAI;AACd,YAAM,MAAM,KAAK,SAAQ;AACzB,aAAO,OAAO,IAAI,YAAW,CAAE,KAAK,OAAO,GAAG;IAChD;GACD;AAEL;AAoFA,IAAM,wBAAwB,MAAyB;AACrD,MAAI,OAAO,SAAS,eAAe,KAAK,SAAS,MAAM;AACrD,WAAO;MACL,oBAAoB;MACpB,+BAA+B;MAC/B,kBAAkB,kBAAkB,KAAK,MAAM,EAAE;MACjD,oBAAoB,cAAc,KAAK,MAAM,IAAI;MACjD,uBAAuB;MACvB,+BAA+B,KAAK;;;AAGxC,MAAI,OAAO,gBAAgB,aAAa;AACtC,WAAO;MACL,oBAAoB;MACpB,+BAA+B;MAC/B,kBAAkB;MAClB,oBAAoB,SAAS,WAAW;MACxC,uBAAuB;MACvB,+BAA+B,QAAQ;;;AAI3C,MAAI,OAAO,UAAU,SAAS,KAAK,OAAO,YAAY,cAAc,UAAU,CAAC,MAAM,oBAAoB;AACvG,WAAO;MACL,oBAAoB;MACpB,+BAA+B;MAC/B,kBAAkB,kBAAkB,QAAQ,QAAQ;MACpD,oBAAoB,cAAc,QAAQ,IAAI;MAC9C,uBAAuB;MACvB,+BAA+B,QAAQ;;;AAI3C,QAAM,cAAc,eAAc;AAClC,MAAI,aAAa;AACf,WAAO;MACL,oBAAoB;MACpB,+BAA+B;MAC/B,kBAAkB;MAClB,oBAAoB;MACpB,uBAAuB,WAAW,YAAY,OAAO;MACrD,+BAA+B,YAAY;;;AAK/C,SAAO;IACL,oBAAoB;IACpB,+BAA+B;IAC/B,kBAAkB;IAClB,oBAAoB;IACpB,uBAAuB;IACvB,+BAA+B;;AAEnC;AAUA,SAAS,iBAAc;AACrB,MAAI,OAAO,cAAc,eAAe,CAAC,WAAW;AAClD,WAAO;;AAIT,QAAM,kBAAkB;IACtB,EAAE,KAAK,QAAiB,SAAS,uCAAsC;IACvE,EAAE,KAAK,MAAe,SAAS,uCAAsC;IACrE,EAAE,KAAK,MAAe,SAAS,6CAA4C;IAC3E,EAAE,KAAK,UAAmB,SAAS,yCAAwC;IAC3E,EAAE,KAAK,WAAoB,SAAS,0CAAyC;IAC7E,EAAE,KAAK,UAAmB,SAAS,oEAAmE;;AAIxG,aAAW,EAAE,KAAK,QAAO,KAAM,iBAAiB;AAC9C,UAAM,QAAQ,QAAQ,KAAK,UAAU,SAAS;AAC9C,QAAI,OAAO;AACT,YAAM,QAAQ,MAAM,CAAC,KAAK;AAC1B,YAAM,QAAQ,MAAM,CAAC,KAAK;AAC1B,YAAM,QAAQ,MAAM,CAAC,KAAK;AAE1B,aAAO,EAAE,SAAS,KAAK,SAAS,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,GAAE;;;AAIhE,SAAO;AACT;AAEA,IAAM,gBAAgB,CAAC,SAAsB;AAK3C,MAAI,SAAS;AAAO,WAAO;AAC3B,MAAI,SAAS,YAAY,SAAS;AAAO,WAAO;AAChD,MAAI,SAAS;AAAO,WAAO;AAC3B,MAAI,SAAS,aAAa,SAAS;AAAS,WAAO;AACnD,MAAI;AAAM,WAAO,SAAS,IAAI;AAC9B,SAAO;AACT;AAEA,IAAM,oBAAoB,CAAC,aAAkC;AAO3D,aAAW,SAAS,YAAW;AAM/B,MAAI,SAAS,SAAS,KAAK;AAAG,WAAO;AACrC,MAAI,aAAa;AAAW,WAAO;AACnC,MAAI,aAAa;AAAU,WAAO;AAClC,MAAI,aAAa;AAAS,WAAO;AACjC,MAAI,aAAa;AAAW,WAAO;AACnC,MAAI,aAAa;AAAW,WAAO;AACnC,MAAI,aAAa;AAAS,WAAO;AACjC,MAAI;AAAU,WAAO,SAAS,QAAQ;AACtC,SAAO;AACT;AAEA,IAAI;AACJ,IAAM,qBAAqB,MAAK;AAC9B,SAAQ,qBAAA,mBAAqB,sBAAqB;AACpD;AAEO,IAAM,WAAW,CAAC,SAAgB;AACvC,MAAI;AACF,WAAO,KAAK,MAAM,IAAI;WACf,KAAK;AACZ,WAAO;;AAEX;AAGA,IAAM,yBAAyB,IAAI,OAAO,mBAAmB,GAAG;AAChE,IAAM,gBAAgB,CAAC,QAAwB;AAC7C,SAAO,uBAAuB,KAAK,GAAG;AACxC;AAEO,IAAM,QAAQ,CAAC,OAAe,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AAErF,IAAM,0BAA0B,CAAC,MAAc,MAAsB;AACnE,MAAI,OAAO,MAAM,YAAY,CAAC,OAAO,UAAU,CAAC,GAAG;AACjD,UAAM,IAAI,eAAe,GAAG,IAAI,qBAAqB;;AAEvD,MAAI,IAAI,GAAG;AACT,UAAM,IAAI,eAAe,GAAG,IAAI,6BAA6B;;AAE/D,SAAO;AACT;AAEO,IAAM,cAAc,CAAC,QAAmB;AAC7C,MAAI,eAAe;AAAO,WAAO;AACjC,SAAO,IAAI,MAAM,GAAG;AACtB;AAcO,IAAM,UAAU,CAAC,QAAmC;;AACzD,MAAI,OAAO,YAAY,aAAa;AAClC,aAAO,MAAAC,MAAA,QAAQ,QAAR,gBAAAA,IAAc,SAAd,mBAAoB,WAAU;;AAEvC,MAAI,OAAO,SAAS,aAAa;AAC/B,YAAO,sBAAK,QAAL,mBAAU,QAAV,4BAAgB,SAAhB,mBAAsB;;AAE/B,SAAO;AACT;AA4CM,SAAU,WAAW,KAA8B;AACvD,MAAI,CAAC;AAAK,WAAO;AACjB,aAAW,MAAM;AAAK,WAAO;AAC7B,SAAO;AACT;AAGM,SAAU,OAAO,KAAa,KAAW;AAC7C,SAAO,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG;AACtD;AAQA,SAAS,gBAAgB,eAAwB,YAAmB;AAClE,aAAW,KAAK,YAAY;AAC1B,QAAI,CAAC,OAAO,YAAY,CAAC;AAAG;AAC5B,UAAM,WAAW,EAAE,YAAW;AAC9B,QAAI,CAAC;AAAU;AAEf,UAAM,MAAM,WAAW,CAAC;AAExB,QAAI,QAAQ,MAAM;AAChB,aAAO,cAAc,QAAQ;eACpB,QAAQ,QAAW;AAC5B,oBAAc,QAAQ,IAAI;;;AAGhC;AAEM,SAAU,MAAM,WAAmB,MAAW;AAClD,MAAI,OAAO,YAAY,eAAe,QAAQ,IAAI,OAAO,MAAM,QAAQ;AACrE,YAAQ,IAAI,mBAAmB,MAAM,IAAI,GAAG,IAAI;;AAEpD;AAKA,IAAM,QAAQ,MAAK;AACjB,SAAO,uCAAuC,QAAQ,SAAS,CAAC,MAAK;AACnE,UAAM,IAAK,KAAK,OAAM,IAAK,KAAM;AACjC,UAAM,IAAI,MAAM,MAAM,IAAK,IAAI,IAAO;AACtC,WAAO,EAAE,SAAS,EAAE;EACtB,CAAC;AACH;;;AC/jCM,IAAO,cAAP,MAAkB;EAGtB,YAAY,QAAsB;AAChC,SAAK,UAAU;EACjB;;;;ACDI,IAAO,cAAP,cAA2B,YAAW;EAqB1C,OACE,MACA,SAA6B;AAE7B,WAAO,KAAK,QAAQ,KAAK,gBAAgB;MACvC;MACA,SAAS;MACT,GAAG;MACH,QAAQ,KAAK,UAAU;KACxB;EACH;;AAkLF,0BAAiBC,cAAW;AAK5B,GALiB,gBAAA,cAAW,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1LtB,IAAO,gBAAP,MAAO,eAAa;EAsBxB,cAAA;;AArBA,SAAA,WAA2B,CAAA;AAC3B,SAAA,mBAA8B,CAAA;AAC9B,0CAAA,IAAA,MAAA,MAAA;AAEA,SAAA,aAA8B,IAAI,gBAAe;AAEjD,oCAAA,IAAA,MAAA,MAAA;AACA,2CAAA,IAAA,MAAuC,MAAK;IAAE,CAAC;AAC/C,0CAAA,IAAA,MAA2D,MAAK;IAAE,CAAC;AAEnE,8BAAA,IAAA,MAAA,MAAA;AACA,qCAAA,IAAA,MAAiC,MAAK;IAAE,CAAC;AACzC,oCAAA,IAAA,MAAqD,MAAK;IAAE,CAAC;AAE7D,6BAAA,IAAA,MAA4F,CAAA,CAAE;AAE9F,yBAAA,IAAA,MAAS,KAAK;AACd,2BAAA,IAAA,MAAW,KAAK;AAChB,2BAAA,IAAA,MAAW,KAAK;AAChB,0CAAA,IAAA,MAA0B,KAAK;AAyO/B,+BAAA,IAAA,MAAe,CAAC,UAAkB;AAChC,MAAAC,wBAAA,MAAI,wBAAY,MAAI,GAAA;AACpB,UAAI,iBAAiB,SAAS,MAAM,SAAS,cAAc;AACzD,gBAAQ,IAAI,kBAAiB;;AAE/B,UAAI,iBAAiB,mBAAmB;AACtC,QAAAA,wBAAA,MAAI,wBAAY,MAAI,GAAA;AACpB,eAAO,KAAK,MAAM,SAAS,KAAK;;AAElC,UAAI,iBAAiB,gBAAgB;AACnC,eAAO,KAAK,MAAM,SAAS,KAAK;;AAElC,UAAI,iBAAiB,OAAO;AAC1B,cAAM,iBAAiC,IAAI,eAAe,MAAM,OAAO;AAEvE,uBAAe,QAAQ;AACvB,eAAO,KAAK,MAAM,SAAS,cAAc;;AAE3C,aAAO,KAAK,MAAM,SAAS,IAAI,eAAe,OAAO,KAAK,CAAC,CAAC;IAC9D,CAAC;AAzPC,IAAAA,wBAAA,MAAI,iCAAqB,IAAI,QAAc,CAAC,SAAS,WAAU;AAC7D,MAAAA,wBAAA,MAAI,wCAA4B,SAAO,GAAA;AACvC,MAAAA,wBAAA,MAAI,uCAA2B,QAAM,GAAA;IACvC,CAAC,GAAC,GAAA;AAEF,IAAAA,wBAAA,MAAI,2BAAe,IAAI,QAAc,CAAC,SAAS,WAAU;AACvD,MAAAA,wBAAA,MAAI,kCAAsB,SAAO,GAAA;AACjC,MAAAA,wBAAA,MAAI,iCAAqB,QAAM,GAAA;IACjC,CAAC,GAAC,GAAA;AAMF,IAAAC,wBAAA,MAAI,iCAAA,GAAA,EAAmB,MAAM,MAAK;IAAE,CAAC;AACrC,IAAAA,wBAAA,MAAI,2BAAA,GAAA,EAAa,MAAM,MAAK;IAAE,CAAC;EACjC;;;;;;;;EASA,OAAO,mBAAmB,QAAsB;AAC9C,UAAM,SAAS,IAAI,eAAa;AAChC,WAAO,KAAK,MAAM,OAAO,oBAAoB,MAAM,CAAC;AACpD,WAAO;EACT;EAEA,OAAO,cACL,UACA,QACA,SAA6B;AAE7B,UAAM,SAAS,IAAI,eAAa;AAChC,eAAW,WAAW,OAAO,UAAU;AACrC,aAAO,iBAAiB,OAAO;;AAEjC,WAAO,KAAK,MACV,OAAO,eACL,UACA,EAAE,GAAG,QAAQ,QAAQ,KAAI,GACzB,EAAE,GAAG,SAAS,SAAS,EAAE,GAAG,mCAAS,SAAS,6BAA6B,SAAQ,EAAE,CAAE,CACxF;AAEH,WAAO;EACT;EAEU,KAAK,UAA4B;AACzC,aAAQ,EAAG,KAAK,MAAK;AACnB,WAAK,WAAU;AACf,WAAK,MAAM,KAAK;IAClB,GAAGA,wBAAA,MAAI,4BAAA,GAAA,CAAa;EACtB;EAEU,iBAAiB,SAAqB;AAC9C,SAAK,SAAS,KAAK,OAAO;EAC5B;EAEU,YAAY,SAAkB,OAAO,MAAI;AACjD,SAAK,iBAAiB,KAAK,OAAO;AAClC,QAAI,MAAM;AACR,WAAK,MAAM,WAAW,OAAO;;EAEjC;EAEU,MAAM,eACd,UACA,QACA,SAA6B;;AAE7B,UAAM,SAAS,mCAAS;AACxB,QAAI,QAAQ;AACV,UAAI,OAAO;AAAS,aAAK,WAAW,MAAK;AACzC,aAAO,iBAAiB,SAAS,MAAM,KAAK,WAAW,MAAK,CAAE;;AAEhE,IAAAA,wBAAA,MAAI,0BAAA,KAAA,2BAAA,EAAc,KAAlB,IAAI;AACJ,UAAM,SAAS,MAAM,SAAS,OAC5B,EAAE,GAAG,QAAQ,QAAQ,KAAI,GACzB,EAAE,GAAG,SAAS,QAAQ,KAAK,WAAW,OAAM,CAAE;AAEhD,SAAK,WAAU;AACf,qBAAiB,SAAS,QAAQ;AAChC,MAAAA,wBAAA,MAAI,0BAAA,KAAA,6BAAA,EAAgB,KAApB,MAAqB,KAAK;;AAE5B,SAAIC,MAAA,OAAO,WAAW,WAAlB,gBAAAA,IAA0B,SAAS;AACrC,YAAM,IAAI,kBAAiB;;AAE7B,IAAAD,wBAAA,MAAI,0BAAA,KAAA,yBAAA,EAAY,KAAhB,IAAI;EACN;EAEU,aAAU;AAClB,QAAI,KAAK;AAAO;AAChB,IAAAA,wBAAA,MAAI,wCAAA,GAAA,EAAyB,KAA7B,IAAI;AACJ,SAAK,MAAM,SAAS;EACtB;EAEA,IAAI,QAAK;AACP,WAAOA,wBAAA,MAAI,sBAAA,GAAA;EACb;EAEA,IAAI,UAAO;AACT,WAAOA,wBAAA,MAAI,wBAAA,GAAA;EACb;EAEA,IAAI,UAAO;AACT,WAAOA,wBAAA,MAAI,wBAAA,GAAA;EACb;EAEA,QAAK;AACH,SAAK,WAAW,MAAK;EACvB;;;;;;;;EASA,GAA4C,OAAc,UAAoC;AAC5F,UAAM,YACJA,wBAAA,MAAI,0BAAA,GAAA,EAAY,KAAK,MAAMA,wBAAA,MAAI,0BAAA,GAAA,EAAY,KAAK,IAAI,CAAA;AACtD,cAAU,KAAK,EAAE,SAAQ,CAAE;AAC3B,WAAO;EACT;;;;;;;;EASA,IAA6C,OAAc,UAAoC;AAC7F,UAAM,YAAYA,wBAAA,MAAI,0BAAA,GAAA,EAAY,KAAK;AACvC,QAAI,CAAC;AAAW,aAAO;AACvB,UAAM,QAAQ,UAAU,UAAU,CAAC,MAAM,EAAE,aAAa,QAAQ;AAChE,QAAI,SAAS;AAAG,gBAAU,OAAO,OAAO,CAAC;AACzC,WAAO;EACT;;;;;;EAOA,KAA8C,OAAc,UAAoC;AAC9F,UAAM,YACJA,wBAAA,MAAI,0BAAA,GAAA,EAAY,KAAK,MAAMA,wBAAA,MAAI,0BAAA,GAAA,EAAY,KAAK,IAAI,CAAA;AACtD,cAAU,KAAK,EAAE,UAAU,MAAM,KAAI,CAAE;AACvC,WAAO;EACT;;;;;;;;;;;;EAaA,QACE,OAAY;AAMZ,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,MAAAD,wBAAA,MAAI,uCAA2B,MAAI,GAAA;AACnC,UAAI,UAAU;AAAS,aAAK,KAAK,SAAS,MAAM;AAChD,WAAK,KAAK,OAAO,OAAc;IACjC,CAAC;EACH;EAEA,MAAM,OAAI;AACR,IAAAA,wBAAA,MAAI,uCAA2B,MAAI,GAAA;AACnC,UAAMC,wBAAA,MAAI,2BAAA,GAAA;EACZ;EAEA,IAAI,iBAAc;AAChB,WAAOA,wBAAA,MAAI,uCAAA,GAAA;EACb;;;;;EAaA,MAAM,eAAY;AAChB,UAAM,KAAK,KAAI;AACf,WAAOA,wBAAA,MAAI,0BAAA,KAAA,8BAAA,EAAiB,KAArB,IAAI;EACb;;;;;;EAqBA,MAAM,YAAS;AACb,UAAM,KAAK,KAAI;AACf,WAAOA,wBAAA,MAAI,0BAAA,KAAA,2BAAA,EAAc,KAAlB,IAAI;EACb;EAuBU,MACR,UACG,MAA4C;AAG/C,QAAIA,wBAAA,MAAI,sBAAA,GAAA;AAAS;AAEjB,QAAI,UAAU,OAAO;AACnB,MAAAD,wBAAA,MAAI,sBAAU,MAAI,GAAA;AAClB,MAAAC,wBAAA,MAAI,kCAAA,GAAA,EAAmB,KAAvB,IAAI;;AAGN,UAAM,YAA4DA,wBAAA,MAAI,0BAAA,GAAA,EAAY,KAAK;AACvF,QAAI,WAAW;AACb,MAAAA,wBAAA,MAAI,0BAAA,GAAA,EAAY,KAAK,IAAI,UAAU,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI;AACxD,gBAAU,QAAQ,CAAC,EAAE,SAAQ,MAAY,SAAS,GAAG,IAAI,CAAC;;AAG5D,QAAI,UAAU,SAAS;AACrB,YAAM,QAAQ,KAAK,CAAC;AACpB,UAAI,CAACA,wBAAA,MAAI,uCAAA,GAAA,KAA4B,EAAC,uCAAW,SAAQ;AACvD,gBAAQ,OAAO,KAAK;;AAEtB,MAAAA,wBAAA,MAAI,uCAAA,GAAA,EAAwB,KAA5B,MAA6B,KAAK;AAClC,MAAAA,wBAAA,MAAI,iCAAA,GAAA,EAAkB,KAAtB,MAAuB,KAAK;AAC5B,WAAK,MAAM,KAAK;AAChB;;AAGF,QAAI,UAAU,SAAS;AAGrB,YAAM,QAAQ,KAAK,CAAC;AACpB,UAAI,CAACA,wBAAA,MAAI,uCAAA,GAAA,KAA4B,EAAC,uCAAW,SAAQ;AAOvD,gBAAQ,OAAO,KAAK;;AAEtB,MAAAA,wBAAA,MAAI,uCAAA,GAAA,EAAwB,KAA5B,MAA6B,KAAK;AAClC,MAAAA,wBAAA,MAAI,iCAAA,GAAA,EAAkB,KAAtB,MAAuB,KAAK;AAC5B,WAAK,MAAM,KAAK;;EAEpB;EAEU,aAAU;AAClB,UAAM,eAAe,KAAK,iBAAiB,GAAG,EAAE;AAChD,QAAI,cAAc;AAChB,WAAK,MAAM,gBAAgBA,wBAAA,MAAI,0BAAA,KAAA,8BAAA,EAAiB,KAArB,IAAI,CAAmB;;EAEtD;EAgDU,MAAM,oBACd,gBACA,SAA6B;;AAE7B,UAAM,SAAS,mCAAS;AACxB,QAAI,QAAQ;AACV,UAAI,OAAO;AAAS,aAAK,WAAW,MAAK;AACzC,aAAO,iBAAiB,SAAS,MAAM,KAAK,WAAW,MAAK,CAAE;;AAEhE,IAAAA,wBAAA,MAAI,0BAAA,KAAA,2BAAA,EAAc,KAAlB,IAAI;AACJ,SAAK,WAAU;AACf,UAAM,SAAS,OAAO,mBAAuC,gBAAgB,KAAK,UAAU;AAC5F,qBAAiB,SAAS,QAAQ;AAChC,MAAAA,wBAAA,MAAI,0BAAA,KAAA,6BAAA,EAAgB,KAApB,MAAqB,KAAK;;AAE5B,SAAIC,MAAA,OAAO,WAAW,WAAlB,gBAAAA,IAA0B,SAAS;AACrC,YAAM,IAAI,kBAAiB;;AAE7B,IAAAD,wBAAA,MAAI,0BAAA,KAAA,yBAAA,EAAY,KAAhB,IAAI;EACN;EA2CA,EAAA,wCAAA,oBAAA,QAAA,GAAA,kCAAA,oBAAA,QAAA,GAAA,yCAAA,oBAAA,QAAA,GAAA,wCAAA,oBAAA,QAAA,GAAA,4BAAA,oBAAA,QAAA,GAAA,mCAAA,oBAAA,QAAA,GAAA,kCAAA,oBAAA,QAAA,GAAA,2BAAA,oBAAA,QAAA,GAAA,uBAAA,oBAAA,QAAA,GAAA,yBAAA,oBAAA,QAAA,GAAA,yBAAA,oBAAA,QAAA,GAAA,wCAAA,oBAAA,QAAA,GAAA,6BAAA,oBAAA,QAAA,GAAA,2BAAA,oBAAA,QAAA,GAAA,iCAAA,SAAAE,kCAAA;AA/NE,QAAI,KAAK,iBAAiB,WAAW,GAAG;AACtC,YAAM,IAAI,eAAe,8DAA8D;;AAEzF,WAAO,KAAK,iBAAiB,GAAG,EAAE;EACpC,GAAC,8BAAA,SAAAC,+BAAA;AAYC,QAAI,KAAK,iBAAiB,WAAW,GAAG;AACtC,YAAM,IAAI,eAAe,8DAA8D;;AAEzF,UAAM,aAAa,KAAK,iBACrB,GAAG,EAAE,EACL,QAAQ,OAAO,CAAC,UAAU,MAAM,SAAS,MAAM,EAC/C,IAAI,CAAC,UAAU,MAAM,IAAI;AAC5B,QAAI,WAAW,WAAW,GAAG;AAC3B,YAAM,IAAI,eAAe,+DAA+D;;AAE1F,WAAO,WAAW,KAAK,GAAG;EAC5B,GAAC,8BAAA,SAAAC,+BAAA;AAyFC,QAAI,KAAK;AAAO;AAChB,IAAAL,wBAAA,MAAI,uCAA2B,QAAS,GAAA;EAC1C,GAAC,gCAAA,SAAAM,+BACe,OAAyB;AACvC,QAAI,KAAK;AAAO;AAChB,UAAM,kBAAkBL,wBAAA,MAAI,0BAAA,KAAA,gCAAA,EAAmB,KAAvB,MAAwB,KAAK;AACrD,SAAK,MAAM,eAAe,OAAO,eAAe;AAEhD,YAAQ,MAAM,MAAM;MAClB,KAAK,uBAAuB;AAC1B,YAAI,MAAM,MAAM,SAAS,cAAc;AACrC,eAAK,MAAM,QAAQ,MAAM,MAAM,MAAM,gBAAgB,QAAQ,GAAG,EAAE,EAAG,QAAQ,EAAE;;AAEjF;;MAEF,KAAK,gBAAgB;AACnB,aAAK,iBAAiB,eAAe;AACrC,aAAK,YAAY,iBAAiB,IAAI;AACtC;;MAEF,KAAK,sBAAsB;AACzB,aAAK,MAAM,gBAAgB,gBAAgB,QAAQ,GAAG,EAAE,CAAE;AAC1D;;MAEF,KAAK,iBAAiB;AACpB,QAAAD,wBAAA,MAAI,uCAA2B,iBAAe,GAAA;AAC9C;;MAEF,KAAK;MACL,KAAK;AACH;;EAEN,GAAC,4BAAA,SAAAO,6BAAA;AAEC,QAAI,KAAK,OAAO;AACd,YAAM,IAAI,eAAe,yCAAyC;;AAEpE,UAAM,WAAWN,wBAAA,MAAI,uCAAA,GAAA;AACrB,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,eAAe,0CAA0C;;AAErE,IAAAD,wBAAA,MAAI,uCAA2B,QAAS,GAAA;AACxC,WAAO;EACT,GAAC,mCAAA,SAAAQ,kCA4BkB,OAAyB;AAC1C,QAAI,WAAWP,wBAAA,MAAI,uCAAA,GAAA;AAEnB,QAAI,MAAM,SAAS,iBAAiB;AAClC,UAAI,UAAU;AACZ,cAAM,IAAI,eAAe,+BAA+B,MAAM,IAAI,kCAAkC;;AAEtG,aAAO,MAAM;;AAGf,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,eAAe,+BAA+B,MAAM,IAAI,yBAAyB;;AAG7F,YAAQ,MAAM,MAAM;MAClB,KAAK;AACH,eAAO;MACT,KAAK;AACH,iBAAS,cAAc,MAAM,MAAM;AACnC,iBAAS,gBAAgB,MAAM,MAAM;AACrC,eAAO;MACT,KAAK;AACH,iBAAS,QAAQ,KAAK,MAAM,aAAa;AACzC,eAAO;MACT,KAAK,uBAAuB;AAC1B,cAAM,kBAAkB,SAAS,QAAQ,GAAG,MAAM,KAAK;AACvD,aAAI,mDAAiB,UAAS,UAAU,MAAM,MAAM,SAAS,cAAc;AACzE,0BAAgB,QAAQ,MAAM,MAAM;;AAEtC,eAAO;;MAET,KAAK;AACH,eAAO;;EAEb,GAEC,OAAO,cAAa,IAAC;AACpB,UAAM,YAAkC,CAAA;AACxC,UAAM,YAGA,CAAA;AACN,QAAI,OAAO;AAEX,SAAK,GAAG,eAAe,CAAC,UAAS;AAC/B,YAAM,SAAS,UAAU,MAAK;AAC9B,UAAI,QAAQ;AACV,eAAO,QAAQ,KAAK;aACf;AACL,kBAAU,KAAK,KAAK;;IAExB,CAAC;AAED,SAAK,GAAG,OAAO,MAAK;AAClB,aAAO;AACP,iBAAW,UAAU,WAAW;AAC9B,eAAO,QAAQ,MAAS;;AAE1B,gBAAU,SAAS;IACrB,CAAC;AAED,SAAK,GAAG,SAAS,CAAC,QAAO;AACvB,aAAO;AACP,iBAAW,UAAU,WAAW;AAC9B,eAAO,OAAO,GAAG;;AAEnB,gBAAU,SAAS;IACrB,CAAC;AAED,SAAK,GAAG,SAAS,CAAC,QAAO;AACvB,aAAO;AACP,iBAAW,UAAU,WAAW;AAC9B,eAAO,OAAO,GAAG;;AAEnB,gBAAU,SAAS;IACrB,CAAC;AAED,WAAO;MACL,MAAM,YAAwD;AAC5D,YAAI,CAAC,UAAU,QAAQ;AACrB,cAAI,MAAM;AACR,mBAAO,EAAE,OAAO,QAAW,MAAM,KAAI;;AAEvC,iBAAO,IAAI,QAAwC,CAAC,SAAS,WAC3D,UAAU,KAAK,EAAE,SAAS,OAAM,CAAE,CAAC,EACnC,KAAK,CAACQ,WAAWA,SAAQ,EAAE,OAAOA,QAAO,MAAM,MAAK,IAAK,EAAE,OAAO,QAAW,MAAM,KAAI,CAAG;;AAE9F,cAAM,QAAQ,UAAU,MAAK;AAC7B,eAAO,EAAE,OAAO,OAAO,MAAM,MAAK;MACpC;MACA,QAAQ,YAAW;AACjB,aAAK,MAAK;AACV,eAAO,EAAE,OAAO,QAAW,MAAM,KAAI;MACvC;;EAEJ;EAEA,mBAAgB;AACd,UAAM,SAAS,IAAI,OAAO,KAAK,OAAO,aAAa,EAAE,KAAK,IAAI,GAAG,KAAK,UAAU;AAChF,WAAO,OAAO,iBAAgB;EAChC;;;;AC1gBI,IAAO,WAAP,cAAwB,YAAW;EAmBvC,OACE,MACA,SAA6B;AAE7B,WAAO,KAAK,QAAQ,KAAK,gBAAgB;MACvC;MACA,SAAS;MACT,GAAG;MACH,QAAQ,KAAK,UAAU;KACxB;EACH;;;;EAKA,OAAO,MAA2B,SAA6B;AAC7D,WAAO,cAAc,cAAc,MAAM,MAAM,OAAO;EACxD;;AAopBF,0BAAiBC,WAAQ;AAoBzB,GApBiB,aAAA,WAAQ,CAAA,EAAA;;;;ACrnBnB,IAAO,YAAP,cAA8B,UAAS;;;;;;;;;;;;;;EAmB3C,YAAY,EACV,UAAe,QAAQ,oBAAoB,GAC3C,SAAc,QAAQ,mBAAmB,KAAK,MAC9C,YAAiB,QAAQ,sBAAsB,KAAK,MACpD,GAAG,KAAI,IACU,CAAA,GAAE;AACnB,UAAM,UAAyB;MAC7B;MACA;MACA,GAAG;MACH,SAAS,WAAW;;AAGtB,UAAM;MACJ,SAAS,QAAQ;MACjB,SAAS,QAAQ,WAAW;MAC5B,WAAW,QAAQ;MACnB,YAAY,QAAQ;MACpB,OAAO,QAAQ;KAChB;AAOH,SAAA,cAA+B,IAAQ,YAAY,IAAI;AACvD,SAAA,WAAyB,IAAQ,SAAS,IAAI;AAP5C,SAAK,WAAW;AAEhB,SAAK,SAAS;AACd,SAAK,YAAY;EACnB;EAKmB,eAAY;AAC7B,WAAO,KAAK,SAAS;EACvB;EAEmB,eAAe,MAA8B;AAC9D,WAAO;MACL,GAAG,MAAM,eAAe,IAAI;MAC5B,qBAAqB;MACrB,GAAG,KAAK,SAAS;;EAErB;EAEmB,gBAAgB,SAAuB,eAA2B;AACnF,QAAI,KAAK,UAAU,QAAQ,WAAW,GAAG;AACvC;;AAEF,QAAI,cAAc,WAAW,MAAM,MAAM;AACvC;;AAGF,QAAI,KAAK,aAAa,QAAQ,eAAe,GAAG;AAC9C;;AAEF,QAAI,cAAc,eAAe,MAAM,MAAM;AAC3C;;AAGF,UAAM,IAAI,MACR,2KAA2K;EAE/K;EAEmB,YAAY,MAA8B;AAC3D,UAAM,aAAa,KAAK,WAAW,IAAI;AACvC,UAAM,aAAa,KAAK,WAAW,IAAI;AAEvC,QAAI,cAAc,QAAQ,CAAM,WAAW,UAAU,GAAG;AACtD,aAAO;;AAGT,QAAI,cAAc,QAAQ,CAAM,WAAW,UAAU,GAAG;AACtD,aAAO;;AAET,WAAO,CAAA;EACT;EAEU,WAAW,MAA8B;AACjD,QAAI,KAAK,UAAU,MAAM;AACvB,aAAO,CAAA;;AAET,WAAO,EAAE,aAAa,KAAK,OAAM;EACnC;EAEU,WAAW,MAA8B;AACjD,QAAI,KAAK,aAAa,MAAM;AAC1B,aAAO,CAAA;;AAET,WAAO,EAAE,eAAe,UAAU,KAAK,SAAS,GAAE;EACpD;;;AAEO,UAAA,YAAY;AACZ,UAAA,eAAe;AACf,UAAA,YAAY;AAEZ,UAAA,iBAAwB;AACxB,UAAA,WAAkB;AAClB,UAAA,qBAA4B;AAC5B,UAAA,4BAAmC;AACnC,UAAA,oBAA2B;AAC3B,UAAA,gBAAuB;AACvB,UAAA,gBAAuB;AACvB,UAAA,iBAAwB;AACxB,UAAA,kBAAyB;AACzB,UAAA,sBAA6B;AAC7B,UAAA,sBAA6B;AAC7B,UAAA,wBAA+B;AAC/B,UAAA,2BAAkC;AAGpC,IAAM,EAAE,cAAc,UAAS,IAAK;AAEpC,IAAM,EACX,gBAAAC,iBACA,UAAAC,WACA,oBAAAC,qBACA,2BAAAC,4BACA,mBAAAC,oBACA,eAAAC,gBACA,eAAAC,gBACA,gBAAAC,iBACA,iBAAAC,kBACA,qBAAAC,sBACA,qBAAAC,sBACA,uBAAAC,wBACA,0BAAAC,0BAAwB,IACtB;AAEE,IAAQC,UAAiB;AACzB,IAAQC,gBAAuB;CAErC,SAAiBC,YAAS;AAEV,EAAAA,WAAA,SAAiB;AACjB,EAAAA,WAAA,eAAuB;AAIvB,EAAAA,WAAA,cAAkB;AAMlB,EAAAA,WAAA,WAAe;AAoB/B,GAjCiB,cAAA,YAAS,CAAA,EAAA;AAmC1B,IAAA,cAAe;", "names": ["fetch", "Request", "Response", "Headers", "FormData", "Blob", "File", "ReadableStream", "ReadableStream", "_a", "File", "_a", "fetch", "opts", "_a", "retryMessage", "_a", "Completions", "__classPrivateFieldSet", "__classPrivateFieldGet", "_a", "_MessageStream_getFinalMessage", "_MessageStream_getFinalText", "_MessageStream_beginRequest", "_MessageStream_addStreamEvent", "_MessageStream_endRequest", "_MessageStream_accumulateMessage", "chunk", "Messages", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "APIError", "APIConnectionError", "APIConnectionTimeoutError", "APIUserAbortError", "NotFoundError", "ConflictError", "RateLimitError", "BadRequestError", "AuthenticationError", "InternalServerError", "PermissionDeniedError", "UnprocessableEntityError", "toFile", "fileFromPath", "Anthropic"]}