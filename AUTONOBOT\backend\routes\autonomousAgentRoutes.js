import express from 'express';
import agentController from '../controllers/agentController.js';
import { loggers } from '../services/loggerService.js';

const router = express.Router();

/**
 * Middleware para logging de requests de agentes autónomos
 */
router.use((req, res, next) => {
  loggers.main.info('Autonomous Agent API request', {
    method: req.method,
    path: req.path,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

/**
 * POST /api/autonomous/process
 * Endpoint principal para procesar instrucciones del usuario con el sistema autónomo
 */
router.post('/process', async (req, res) => {
  await agentController.processInstruction(req, res);
});

/**
 * GET /api/autonomous/status
 * Obtiene el estado actual del sistema de agentes autónomos
 */
router.get('/status', async (req, res) => {
  await agentController.getSystemStatus(req, res);
});

/**
 * POST /api/autonomous/stop
 * Detiene la ejecución actual del sistema autónomo
 */
router.post('/stop', async (req, res) => {
  await agentController.stopExecution(req, res);
});

/**
 * GET /api/autonomous/history
 * Obtiene el historial de tareas del sistema autónomo
 */
router.get('/history', async (req, res) => {
  await agentController.getTaskHistory(req, res);
});

/**
 * GET /api/autonomous/statistics
 * Obtiene estadísticas del sistema autónomo
 */
router.get('/statistics', async (req, res) => {
  await agentController.getStatistics(req, res);
});

/**
 * POST /api/autonomous/test
 * Endpoint para testing del sistema autónomo
 */
router.post('/test', async (req, res) => {
  await agentController.testSystem(req, res);
});

/**
 * GET /api/autonomous/health
 * Health check específico para el sistema de agentes autónomos
 */
router.get('/health', async (req, res) => {
  try {
    const status = agentController.orchestrator.getStatus();
    
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      agents: {
        orchestrator: status.isActive ? 'active' : 'idle',
        observer: status.agentStatus?.observer?.isActive ? 'active' : 'idle',
        search: status.agentStatus?.search?.isActive ? 'active' : 'idle',
        navigation: status.agentStatus?.navigation?.isActive ? 'active' : 'idle',
        response: status.agentStatus?.response?.isActive ? 'active' : 'idle'
      },
      currentTask: status.currentTask ? {
        id: status.currentTask.id,
        status: status.currentTask.status
      } : null,
      system: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        nodeVersion: process.version
      }
    };

    res.json({
      success: true,
      health,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    loggers.main.error('Error in autonomous agents health check', { error: error.message });

    res.status(500).json({
      success: false,
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/autonomous/capabilities
 * Obtiene las capacidades disponibles del sistema autónomo
 */
router.get('/capabilities', async (req, res) => {
  try {
    const capabilities = {
      webNavigation: {
        available: true,
        description: 'Navigate to websites and interact with web elements autonomously',
        features: [
          'Direct URL navigation',
          'Search result navigation',
          'Element interaction (click, type, hover)',
          'Form filling and submission',
          'Multi-step navigation workflows'
        ]
      },
      visualAnalysis: {
        available: true,
        description: 'Analyze screenshots using Gemini 2.0 Flash for visual understanding',
        features: [
          'Element identification and classification',
          'Content extraction and analysis',
          'Interactive element detection',
          'Page structure analysis',
          'Screenshot comparison and change detection'
        ]
      },
      webSearch: {
        available: true,
        description: 'Perform intelligent web searches and analyze results',
        features: [
          'Google search integration',
          'Result relevance scoring',
          'Content categorization',
          'Search result navigation',
          'Query optimization'
        ]
      },
      aiIntegration: {
        available: true,
        description: 'Multiple AI model integration for intelligent decision making',
        features: [
          'OpenAI GPT models',
          'Google Gemini 2.0 Flash',
          'Anthropic Claude',
          'Context-aware responses',
          'Multi-modal analysis'
        ]
      },
      automation: {
        available: true,
        description: 'Advanced browser automation capabilities',
        features: [
          'Playwright integration',
          'Cross-browser support',
          'Screenshot capture and analysis',
          'Element interaction automation',
          'Page state management',
          'Session persistence'
        ]
      },
      agentOrchestration: {
        available: true,
        description: 'Multi-agent system coordination',
        features: [
          'Task decomposition and planning',
          'Agent specialization and coordination',
          'State management across agents',
          'Event-driven communication',
          'Error handling and recovery'
        ]
      }
    };

    res.json({
      success: true,
      capabilities,
      version: '2.0.0',
      systemType: 'autonomous_multi_agent',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    loggers.main.error('Error getting autonomous capabilities', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/autonomous/configure
 * Configura parámetros del sistema de agentes autónomos
 */
router.post('/configure', async (req, res) => {
  try {
    const { configuration } = req.body;

    if (!configuration || typeof configuration !== 'object') {
      return res.status(400).json({
        success: false,
        error: 'Configuration object is required',
        timestamp: new Date().toISOString()
      });
    }

    // Validar y aplicar configuración
    const validatedConfig = validateAutonomousConfiguration(configuration);
    
    loggers.main.info('Autonomous agent system configuration updated', { 
      configuration: validatedConfig 
    });

    res.json({
      success: true,
      message: 'Autonomous system configuration updated successfully',
      appliedConfiguration: validatedConfig,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    loggers.main.error('Error configuring autonomous agents', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/autonomous/agents/:agentName/status
 * Obtiene el estado de un agente específico
 */
router.get('/agents/:agentName/status', async (req, res) => {
  try {
    const { agentName } = req.params;
    
    const validAgents = ['observer', 'search', 'navigation', 'response'];
    if (!validAgents.includes(agentName)) {
      return res.status(400).json({
        success: false,
        error: `Invalid agent name. Valid agents: ${validAgents.join(', ')}`,
        timestamp: new Date().toISOString()
      });
    }

    const agent = agentController.orchestrator.agents[agentName];
    if (!agent) {
      return res.status(404).json({
        success: false,
        error: `Agent ${agentName} not found`,
        timestamp: new Date().toISOString()
      });
    }

    const agentStatus = agent.getStatus();

    res.json({
      success: true,
      agentName,
      status: agentStatus,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    loggers.main.error('Error getting agent status', { 
      agentName: req.params.agentName, 
      error: error.message 
    });

    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Valida la configuración del sistema autónomo
 */
function validateAutonomousConfiguration(config) {
  const validatedConfig = {};

  // Validar timeouts
  if (config.timeouts) {
    validatedConfig.timeouts = {
      taskTimeout: Math.min(config.timeouts.taskTimeout || 300000, 600000),
      stepTimeout: Math.min(config.timeouts.stepTimeout || 30000, 60000),
      navigationTimeout: Math.min(config.timeouts.navigationTimeout || 10000, 30000),
      analysisTimeout: Math.min(config.timeouts.analysisTimeout || 15000, 45000)
    };
  }

  // Validar configuración de agentes
  if (config.agents) {
    validatedConfig.agents = {};
    
    Object.entries(config.agents).forEach(([agentName, agentConfig]) => {
      if (['observer', 'search', 'navigation', 'response'].includes(agentName)) {
        validatedConfig.agents[agentName] = {
          enabled: Boolean(agentConfig.enabled !== false),
          maxRetries: Math.min(agentConfig.maxRetries || 3, 5),
          priority: agentConfig.priority || 'normal',
          ...agentConfig
        };
      }
    });
  }

  // Validar configuración de análisis visual
  if (config.vision) {
    validatedConfig.vision = {
      defaultAnalysisType: config.vision.defaultAnalysisType || 'interactive',
      cacheEnabled: Boolean(config.vision.cacheEnabled !== false),
      maxCacheSize: Math.min(config.vision.maxCacheSize || 100, 500),
      temperature: Math.max(0, Math.min(config.vision.temperature || 0.1, 1))
    };
  }

  // Validar configuración del orchestrator
  if (config.orchestrator) {
    validatedConfig.orchestrator = {
      maxConcurrentTasks: Math.min(config.orchestrator.maxConcurrentTasks || 1, 3),
      defaultStrategy: config.orchestrator.defaultStrategy || 'search_and_extract',
      enableLogging: Boolean(config.orchestrator.enableLogging !== false)
    };
  }

  return validatedConfig;
}

/**
 * Middleware de manejo de errores para rutas de agentes autónomos
 */
router.use((error, req, res, next) => {
  loggers.main.error('Autonomous agent route error', {
    error: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method
  });

  res.status(500).json({
    success: false,
    error: 'Internal server error in autonomous agent system',
    timestamp: new Date().toISOString()
  });
});

export default router;
