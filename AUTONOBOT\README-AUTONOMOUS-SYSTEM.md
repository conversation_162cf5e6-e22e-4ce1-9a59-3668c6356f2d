# 🤖 AUTONOBOT - Sistema de Agentes Autónomos

## 🎯 Descripción

AUTONOBOT ha sido transformado en un **sistema multi-agente autónomo avanzado** similar a 'Do Browser', con capacidades de navegación web inteligente, análisis visual con IA, y automatización completa de tareas web.

## 🏗️ Arquitectura del Sistema

### Agentes Especializados

- **🎭 Agent Orchestrator**: Coordinador principal que gestiona todos los agentes
- **👁️ Observer Agent**: Análisis visual con Gemini 2.0 Flash
- **🔍 Search Agent**: Búsquedas web inteligentes y navegación de resultados
- **🧭 Navigation Agent**: Navegación web e interacción con elementos
- **💬 Response Agent**: Generación de respuestas inteligentes

### Servicios Core

- **🔮 Vision Service**: Integración con Gemini 2.0 Flash
- **📋 Task Manager**: Gestión del ciclo de vida de tareas
- **🧠 State Manager**: Manejo de estado global y memoria
- **📝 Logger Service**: Sistema de logging estructurado

## 🚀 Inicio Rápido

### 1. Instalación de Dependencias

```bash
cd AUTONOBOT
npm install
```

### 2. Configuración de Variables de Entorno

Crea un archivo `.env` con las siguientes variables:

```env
# APIs de IA (opcional para testing)
OPENAI_API_KEY=tu_openai_api_key
GEMINI_API_KEY=tu_gemini_api_key
ANTHROPIC_API_KEY=tu_anthropic_api_key

# Configuración del servidor
PORT=5000
NODE_ENV=development
```

### 3. Iniciar el Sistema

#### Terminal 1: Backend (Servidor de Agentes)
```bash
node simple-autonomous-server.js
```

#### Terminal 2: Frontend (Interfaz de Usuario)
```bash
npm run dev
```

### 4. Acceder a la Aplicación

- **Interfaz Principal**: http://localhost:5173
- **Página de Pruebas**: http://localhost:5173/test-frontend-api.html
- **API Backend**: http://localhost:5000

## 🎮 Cómo Usar el Sistema

### Interfaz de Usuario

1. **Abrir**: http://localhost:5173
2. **Seleccionar Modo**: Hacer clic en "Autonomous Agents" en el header
3. **Escribir Instrucción**: En lenguaje natural en el campo de texto
4. **Procesar**: Hacer clic en "Process" para ejecutar

### Ejemplos de Instrucciones

```
"Search for React best practices and summarize the key points"
"Navigate to GitHub and find trending JavaScript repositories"
"Go to Wikipedia and find information about artificial intelligence"
"Search for Node.js tutorials and extract the main concepts"
"Find information about machine learning frameworks"
```

### Opciones Avanzadas

- **Analysis Type**: Tipo de análisis visual (interactive, content, navigation, general)
- **Include Content**: Incluir análisis detallado de contenido
- **Max Retries**: Número máximo de reintentos
- **Timeout**: Tiempo límite de ejecución

## 🔧 API Endpoints

### Endpoints Principales

```http
GET  /api/autonomous/health          # Health check del sistema
GET  /api/autonomous/status          # Estado actual de agentes
GET  /api/autonomous/capabilities    # Capacidades disponibles
GET  /api/autonomous/statistics      # Estadísticas del sistema
GET  /api/autonomous/history         # Historial de tareas
POST /api/autonomous/process         # Procesar instrucción
POST /api/autonomous/stop            # Detener ejecución
POST /api/autonomous/test            # Pruebas del sistema
```

### Ejemplo de Uso de API

```javascript
// Procesar una instrucción
const response = await fetch('/api/autonomous/process', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    instruction: 'Search for React tutorials',
    options: {
      analysisType: 'interactive',
      includeContent: true,
      maxRetries: 3
    }
  })
});

const result = await response.json();
console.log(result);
```

## 🧪 Testing

### Página de Pruebas Automáticas

Visita http://localhost:5173/test-frontend-api.html para:

- ✅ Probar todos los endpoints de la API
- ✅ Verificar conectividad WebSocket
- ✅ Validar respuestas del sistema
- ✅ Monitorear logs en tiempo real

### Script de Pruebas de API

```bash
node test-api.js
```

### Servidor de Debug

```bash
node debug-server.js
```

## 📁 Estructura del Proyecto

```
AUTONOBOT/
├── backend/
│   ├── agents/                      # Agentes especializados
│   │   ├── AgentOrchestrator.js
│   │   ├── ObserverAgent.js
│   │   ├── SearchAgent.js
│   │   ├── NavigationAgent.js
│   │   ├── ResponseAgent.js
│   │   ├── TaskManager.js
│   │   └── StateManager.js
│   ├── controllers/
│   │   └── agentController.js       # Controlador principal
│   ├── routes/
│   │   └── autonomousAgentRoutes.js # Rutas de API
│   └── services/
│       ├── visionService.js         # Servicio de visión
│       └── loggerService.js         # Sistema de logging
├── src/
│   ├── components/
│   │   ├── AutonomousAgent.jsx      # Componente principal
│   │   └── AutonobotInterface.tsx   # Interfaz clásica
│   └── App.tsx                      # Aplicación principal
├── screenshots/                     # Capturas de pantalla
├── simple-autonomous-server.js      # Servidor de prueba
├── test-api.js                      # Script de pruebas
├── test-frontend-api.html           # Página de pruebas
└── debug-server.js                  # Servidor de debug
```

## 🔮 Capacidades del Sistema

### Navegación Web Autónoma
- ✅ Navegación directa a URLs
- ✅ Navegación basada en resultados de búsqueda
- ✅ Interacción con elementos (click, type, hover)
- ✅ Llenado y envío de formularios
- ✅ Flujos de navegación multi-paso

### Análisis Visual Inteligente
- ✅ Identificación y clasificación de elementos
- ✅ Extracción y análisis de contenido
- ✅ Detección de elementos interactivos
- ✅ Análisis de estructura de páginas
- ✅ Comparación de screenshots

### Búsqueda Web Inteligente
- ✅ Integración con Google Search
- ✅ Puntuación de relevancia de resultados
- ✅ Categorización de contenido
- ✅ Navegación automática a resultados

### Integración de IA Multi-Modal
- ✅ OpenAI GPT models
- ✅ Google Gemini 2.0 Flash
- ✅ Anthropic Claude (preparado)
- ✅ Respuestas conscientes del contexto

## 🛠️ Desarrollo y Personalización

### Agregar Nuevos Agentes

1. Crear archivo en `backend/agents/`
2. Extender de `EventEmitter`
3. Implementar métodos requeridos
4. Registrar en `AgentOrchestrator`

### Configurar APIs Reales

1. Obtener API keys de los servicios
2. Configurar variables de entorno
3. Actualizar servicios correspondientes
4. Probar conectividad

### Personalizar Estrategias

Editar `AgentOrchestrator.js` para:
- Modificar estrategias de ejecución
- Agregar nuevos tipos de tareas
- Personalizar flujos de trabajo

## 🐛 Troubleshooting

### Problemas Comunes

1. **Error de conexión API**: Verificar que el backend esté ejecutándose en puerto 5000
2. **WebSocket no conecta**: Verificar configuración de proxy en `vite.config.ts`
3. **Respuestas vacías**: Verificar logs del servidor para errores
4. **Frontend no carga**: Verificar que Vite esté ejecutándose en puerto 5173

### Logs de Debug

- **Backend**: Los logs aparecen en la consola del servidor
- **Frontend**: Abrir DevTools del navegador (F12) → Console
- **API**: Usar la página de pruebas para monitorear respuestas

## 📊 Estado del Proyecto

### ✅ Completado
- Arquitectura multi-agente
- API funcional completa
- Interfaz de usuario avanzada
- Sistema de pruebas
- Documentación

### 🔄 En Desarrollo
- Integración con APIs reales de IA
- Navegación real con Playwright
- Persistencia de datos

### 🔮 Planificado
- Capacidades de voz
- Análisis de video
- Automatización de workflows complejos
- Integración con herramientas externas

## 🤝 Contribución

1. Fork el proyecto
2. Crear rama de feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📄 Licencia

Este proyecto está bajo la licencia MIT. Ver `LICENSE` para más detalles.

---

🚀 **¡AUTONOBOT está listo para navegar la web de forma autónoma!**
