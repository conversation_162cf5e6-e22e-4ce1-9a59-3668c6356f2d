// Test script for autonomous agents API
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

async function testAPI() {
  console.log('🧪 Testing AUTONOBOT Autonomous Agents API\n');

  try {
    // Test 1: Health check
    console.log('1️⃣ Testing health endpoint...');
    const healthResponse = await fetch(`${BASE_URL}/api/autonomous/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData.health.status);

    // Test 2: Capabilities
    console.log('\n2️⃣ Testing capabilities endpoint...');
    const capabilitiesResponse = await fetch(`${BASE_URL}/api/autonomous/capabilities`);
    const capabilitiesData = await capabilitiesResponse.json();
    console.log('✅ Capabilities loaded:', Object.keys(capabilitiesData.capabilities).length, 'features');

    // Test 3: Status
    console.log('\n3️⃣ Testing status endpoint...');
    const statusResponse = await fetch(`${BASE_URL}/api/autonomous/status`);
    const statusData = await statusResponse.json();
    console.log('✅ Status check:', statusData.status.isActive ? 'Active' : 'Idle');

    // Test 4: Process instruction
    console.log('\n4️⃣ Testing process instruction endpoint...');
    const processResponse = await fetch(`${BASE_URL}/api/autonomous/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        instruction: 'Search for information about React best practices',
        options: {
          analysisType: 'interactive',
          includeContent: true
        }
      })
    });
    
    const processData = await processResponse.json();
    console.log('✅ Process instruction:', processData.success ? 'Success' : 'Failed');
    console.log('   Task ID:', processData.taskId);
    console.log('   Message:', processData.message);

    // Test 5: Statistics
    console.log('\n5️⃣ Testing statistics endpoint...');
    const statsResponse = await fetch(`${BASE_URL}/api/autonomous/statistics`);
    const statsData = await statsResponse.json();
    console.log('✅ Statistics loaded');
    console.log('   System uptime:', Math.round(statsData.statistics.system.uptime), 'seconds');

    console.log('\n🎉 All API tests completed successfully!');
    console.log('\n📋 Available endpoints:');
    console.log('   • GET  /api/autonomous/health');
    console.log('   • GET  /api/autonomous/status');
    console.log('   • GET  /api/autonomous/capabilities');
    console.log('   • GET  /api/autonomous/statistics');
    console.log('   • GET  /api/autonomous/history');
    console.log('   • POST /api/autonomous/process');
    console.log('   • POST /api/autonomous/stop');
    console.log('   • POST /api/autonomous/test');

    console.log('\n🌐 Frontend should be available at: http://localhost:5173');
    console.log('🔧 Backend is running at: http://localhost:5000');

  } catch (error) {
    console.error('❌ API test failed:', error.message);
    console.error('   Make sure the server is running: node simple-autonomous-server.js');
  }
}

// Run tests
testAPI();
