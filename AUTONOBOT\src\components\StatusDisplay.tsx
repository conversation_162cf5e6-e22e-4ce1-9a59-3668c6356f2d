import React from 'react';
import { Globe } from 'lucide-react';

interface StatusDisplayProps {
  currentUrl: string;
}

const StatusDisplay: React.FC<StatusDisplayProps> = ({ currentUrl }) => {
  return (
    <div className="bg-gray-50 p-6 rounded-lg shadow-md border border-gray-200">
      <div className="flex items-center mb-3">
        <Globe size={20} className="text-blue-800 mr-2" />
        <h3 className="text-lg font-semibold text-blue-800">Current URL</h3>
      </div>
      <div className="bg-blue-50 border border-blue-200 text-blue-800 p-3 rounded-lg text-sm break-words shadow-inner">
        {currentUrl}
      </div>
    </div>
  );
};

export default StatusDisplay;