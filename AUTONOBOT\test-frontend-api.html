<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AUTONOBOT API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .log {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AUTONOBOT API Test Page</h1>
        <p>This page tests the autonomous agent API endpoints through the Vite proxy.</p>
        
        <div class="test-section">
            <h3>🏥 Health Check</h3>
            <button onclick="testHealth()">Test Health Endpoint</button>
            <div id="health-result"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 System Status</h3>
            <button onclick="testStatus()">Test Status Endpoint</button>
            <div id="status-result"></div>
        </div>
        
        <div class="test-section">
            <h3>🔧 Capabilities</h3>
            <button onclick="testCapabilities()">Test Capabilities Endpoint</button>
            <div id="capabilities-result"></div>
        </div>
        
        <div class="test-section">
            <h3>🚀 Process Instruction</h3>
            <input type="text" id="instruction-input" placeholder="Enter test instruction" 
                   value="Test autonomous system functionality" style="width: 300px; padding: 8px;">
            <button onclick="testProcess()">Test Process Endpoint</button>
            <div id="process-result"></div>
        </div>
        
        <div class="test-section">
            <h3>🔗 WebSocket Test</h3>
            <button onclick="testWebSocket()">Test WebSocket Connection</button>
            <button onclick="disconnectWebSocket()">Disconnect WebSocket</button>
            <div id="websocket-result"></div>
        </div>
        
        <div class="test-section">
            <h3>📝 Test Log</h3>
            <button onclick="clearLog()">Clear Log</button>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <script>
        let ws = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logDiv.innerHTML += `[${timestamp}] ${icon} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }
        
        function showResult(elementId, data, success = true) {
            const element = document.getElementById(elementId);
            element.className = `test-section ${success ? 'success' : 'error'}`;
            element.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }
        
        async function testHealth() {
            log('Testing health endpoint...');
            try {
                const response = await fetch('/api/autonomous/health');
                const data = await response.json();
                
                if (response.ok) {
                    log('Health check successful', 'success');
                    showResult('health-result', data, true);
                } else {
                    log('Health check failed', 'error');
                    showResult('health-result', data, false);
                }
            } catch (error) {
                log(`Health check error: ${error.message}`, 'error');
                showResult('health-result', { error: error.message }, false);
            }
        }
        
        async function testStatus() {
            log('Testing status endpoint...');
            try {
                const response = await fetch('/api/autonomous/status');
                const data = await response.json();
                
                if (response.ok) {
                    log('Status check successful', 'success');
                    showResult('status-result', data, true);
                } else {
                    log('Status check failed', 'error');
                    showResult('status-result', data, false);
                }
            } catch (error) {
                log(`Status check error: ${error.message}`, 'error');
                showResult('status-result', { error: error.message }, false);
            }
        }
        
        async function testCapabilities() {
            log('Testing capabilities endpoint...');
            try {
                const response = await fetch('/api/autonomous/capabilities');
                const data = await response.json();
                
                if (response.ok) {
                    log('Capabilities check successful', 'success');
                    showResult('capabilities-result', data, true);
                } else {
                    log('Capabilities check failed', 'error');
                    showResult('capabilities-result', data, false);
                }
            } catch (error) {
                log(`Capabilities check error: ${error.message}`, 'error');
                showResult('capabilities-result', { error: error.message }, false);
            }
        }
        
        async function testProcess() {
            const instruction = document.getElementById('instruction-input').value;
            log(`Testing process endpoint with instruction: "${instruction}"`);
            
            try {
                const response = await fetch('/api/autonomous/process', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        instruction: instruction,
                        options: {
                            analysisType: 'interactive',
                            includeContent: true
                        }
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    log('Process instruction successful', 'success');
                    showResult('process-result', data, true);
                } else {
                    log('Process instruction failed', 'error');
                    showResult('process-result', data, false);
                }
            } catch (error) {
                log(`Process instruction error: ${error.message}`, 'error');
                showResult('process-result', { error: error.message }, false);
            }
        }
        
        function testWebSocket() {
            log('Testing WebSocket connection...');
            
            try {
                // Use the proxy path for WebSocket
                ws = new WebSocket('ws://localhost:5173/ws');
                
                ws.onopen = () => {
                    log('WebSocket connected successfully', 'success');
                    showResult('websocket-result', { status: 'Connected', url: 'ws://localhost:5173/ws' }, true);
                    
                    // Send a test message
                    ws.send(JSON.stringify({ type: 'ping' }));
                };
                
                ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    log(`WebSocket message received: ${data.type}`, 'success');
                    showResult('websocket-result', data, true);
                };
                
                ws.onclose = () => {
                    log('WebSocket disconnected');
                    showResult('websocket-result', { status: 'Disconnected' }, false);
                };
                
                ws.onerror = (error) => {
                    log('WebSocket error', 'error');
                    showResult('websocket-result', { error: 'WebSocket connection failed' }, false);
                };
                
            } catch (error) {
                log(`WebSocket setup error: ${error.message}`, 'error');
                showResult('websocket-result', { error: error.message }, false);
            }
        }
        
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                log('WebSocket disconnected manually');
            }
        }
        
        // Auto-run health check on page load
        window.onload = () => {
            log('🚀 AUTONOBOT API Test Page loaded');
            log('Testing basic connectivity...');
            testHealth();
        };
    </script>
</body>
</html>
