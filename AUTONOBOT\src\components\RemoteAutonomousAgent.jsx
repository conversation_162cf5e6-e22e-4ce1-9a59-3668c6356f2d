import React, { useState, useEffect, useRef } from 'react';
import { Send, Square, Activity, Eye, Search, Navigation, MessageSquare, Settings, Monitor, Play, Pause, RotateCcw } from 'lucide-react';
import RemoteDesktopViewer from './RemoteDesktopViewer';

const RemoteAutonomousAgent = () => {
  const [instruction, setInstruction] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [systemStatus, setSystemStatus] = useState(null);
  const [taskHistory, setTaskHistory] = useState([]);
  const [currentResponse, setCurrentResponse] = useState(null);
  const [agentLogs, setAgentLogs] = useState([]);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [wsConnection, setWsConnection] = useState(null);
  const [remoteDesktopConnected, setRemoteDesktopConnected] = useState(false);
  const [vncUrl, setVncUrl] = useState('http://localhost:6901');
  const [isRemoteDesktopReady, setIsRemoteDesktopReady] = useState(false);
  const logsEndRef = useRef(null);

  // Configuración avanzada
  const [advancedOptions, setAdvancedOptions] = useState({
    analysisType: 'interactive',
    includeContent: true,
    maxRetries: 3,
    timeout: 300000,
    useRemoteDesktop: true
  });

  useEffect(() => {
    // Obtener estado inicial del sistema
    fetchSystemStatus();
    fetchTaskHistory();
    
    // Configurar WebSocket para actualizaciones en tiempo real
    setupWebSocket();
    
    // Verificar estado del escritorio remoto
    checkRemoteDesktopStatus();
    
    return () => {
      if (wsConnection) {
        wsConnection.close();
      }
    };
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [agentLogs]);

  const checkRemoteDesktopStatus = async () => {
    try {
      const response = await fetch('/api/autonomous/remote-desktop/status');
      const data = await response.json();
      
      if (data.success) {
        setRemoteDesktopConnected(data.status.isConnected);
        setVncUrl(data.status.vncUrl);
        setIsRemoteDesktopReady(data.status.isReady);
        
        if (data.status.isConnected) {
          addLog('🖥️ Remote desktop connected', 'system');
        } else {
          addLog('🖥️ Remote desktop disconnected', 'error');
        }
      }
    } catch (error) {
      console.error('Error checking remote desktop status:', error);
      addLog('❌ Failed to check remote desktop status', 'error');
    }
  };

  const setupWebSocket = () => {
    try {
      const wsUrl = `ws://localhost:5173/ws`;
      console.log('🔗 Attempting WebSocket connection to:', wsUrl);
      
      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        console.log('✅ WebSocket connected successfully');
        addLog('🔗 Connected to real-time updates', 'system');
      };
      
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('📨 WebSocket message received:', data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error);
        }
      };
      
      ws.onclose = () => {
        console.log('🔌 WebSocket disconnected');
        addLog('🔌 Disconnected from real-time updates', 'system');
      };
      
      ws.onerror = (error) => {
        console.error('💥 WebSocket error:', error);
        addLog('❌ WebSocket connection error - using polling instead', 'error');
      };
      
      setWsConnection(ws);
    } catch (error) {
      console.error('💥 Failed to setup WebSocket:', error);
      addLog('❌ WebSocket setup failed - using polling instead', 'error');
    }
  };

  const handleWebSocketMessage = (data) => {
    switch (data.type) {
      case 'task:started':
        addLog(`🚀 Task started: ${data.data.task.instruction}`, 'info');
        break;
      case 'task:step_completed':
        addLog(`✅ Step completed: ${data.data.step}`, 'success');
        break;
      case 'task:completed':
        addLog(`🎉 Task completed successfully`, 'success');
        setIsProcessing(false);
        fetchTaskHistory();
        break;
      case 'observation:complete':
        addLog(`👁️ Page observation completed`, 'info');
        break;
      case 'search:complete':
        addLog(`🔍 Search completed: ${data.data.results.length} results found`, 'info');
        break;
      case 'navigation:complete':
        addLog(`🧭 Navigation completed to: ${data.data.result.finalUrl}`, 'info');
        break;
      case 'response:generated':
        setCurrentResponse(data.data.response);
        addLog(`💬 Response generated`, 'info');
        break;
      case 'remote_desktop:connected':
        setRemoteDesktopConnected(true);
        addLog(`🖥️ Remote desktop connected`, 'success');
        break;
      case 'remote_desktop:disconnected':
        setRemoteDesktopConnected(false);
        addLog(`🖥️ Remote desktop disconnected`, 'error');
        break;
      default:
        console.log('Unknown WebSocket message:', data);
    }
  };

  const fetchSystemStatus = async () => {
    try {
      console.log('📊 Fetching system status from /api/autonomous/status');
      const response = await fetch('/api/autonomous/status');
      console.log('📊 Status response:', response.status, response.statusText);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('📊 Status data:', data);
      
      if (data.success) {
        setSystemStatus(data.status);
        addLog('✅ System status updated', 'system');
      } else {
        addLog('❌ Failed to get system status', 'error');
      }
    } catch (error) {
      console.error('❌ Error fetching system status:', error);
      addLog(`❌ Failed to fetch system status: ${error.message}`, 'error');
    }
  };

  const fetchTaskHistory = async () => {
    try {
      console.log('📚 Fetching task history from /api/autonomous/history');
      const response = await fetch('/api/autonomous/history?limit=10');
      console.log('📚 History response:', response.status, response.statusText);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('📚 History data:', data);
      
      if (data.success) {
        setTaskHistory(data.history);
        addLog('✅ Task history updated', 'system');
      } else {
        addLog('❌ Failed to get task history', 'error');
      }
    } catch (error) {
      console.error('❌ Error fetching task history:', error);
      addLog(`❌ Failed to fetch task history: ${error.message}`, 'error');
    }
  };

  const processInstruction = async () => {
    if (!instruction.trim() || isProcessing) return;

    setIsProcessing(true);
    setCurrentResponse(null);
    addLog(`🎯 Processing: "${instruction}"`, 'user');

    try {
      const requestBody = {
        instruction: instruction.trim(),
        options: { ...advancedOptions, useRemoteDesktop: true }
      };

      console.log('🚀 Sending instruction to /api/autonomous/process:', requestBody);

      const response = await fetch('/api/autonomous/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      console.log('🚀 Process response:', response.status, response.statusText);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('🚀 Process data:', data);

      if (data.success) {
        addLog(`✅ Task initiated successfully - ID: ${data.taskId}`, 'success');
        setInstruction('');
        fetchSystemStatus();
      } else {
        addLog(`❌ Error: ${data.error}`, 'error');
        setIsProcessing(false);
      }
    } catch (error) {
      console.error('❌ Error processing instruction:', error);
      addLog(`❌ Network error: ${error.message}`, 'error');
      setIsProcessing(false);
    }
  };

  const stopExecution = async () => {
    try {
      const response = await fetch('/api/autonomous/stop', {
        method: 'POST'
      });

      const data = await response.json();

      if (data.success) {
        addLog('🛑 Execution stopped', 'info');
        setIsProcessing(false);
        fetchSystemStatus();
      } else {
        addLog(`❌ Error stopping execution: ${data.error}`, 'error');
      }
    } catch (error) {
      console.error('Error stopping execution:', error);
      addLog(`❌ Error stopping execution: ${error.message}`, 'error');
    }
  };

  const restartRemoteDesktop = async () => {
    try {
      addLog('🔄 Restarting remote desktop...', 'info');
      
      const response = await fetch('/api/autonomous/remote-desktop/restart', {
        method: 'POST'
      });

      const data = await response.json();

      if (data.success) {
        addLog('✅ Remote desktop restarted successfully', 'success');
        setTimeout(() => {
          checkRemoteDesktopStatus();
        }, 3000);
      } else {
        addLog(`❌ Failed to restart remote desktop: ${data.error}`, 'error');
      }
    } catch (error) {
      console.error('Error restarting remote desktop:', error);
      addLog(`❌ Error restarting remote desktop: ${error.message}`, 'error');
    }
  };

  const handleRemoteDesktopConnection = (connected) => {
    setRemoteDesktopConnected(connected);
    if (connected) {
      addLog('🖥️ Remote desktop viewer connected', 'success');
    } else {
      addLog('🖥️ Remote desktop viewer disconnected', 'error');
    }
  };

  const handleRemoteDesktopScreenshot = () => {
    addLog('📸 Screenshot captured from remote desktop', 'info');
  };

  const addLog = (message, type = 'info') => {
    const logEntry = {
      id: Date.now() + Math.random(),
      message,
      type,
      timestamp: new Date().toLocaleTimeString()
    };
    setAgentLogs(prev => [...prev.slice(-49), logEntry]);
  };

  const scrollToBottom = () => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getAgentStatusIcon = (agentName) => {
    const isActive = systemStatus?.agentStatus?.[agentName]?.isActive;
    return isActive ? '🟢' : '⚪';
  };

  const getLogIcon = (type) => {
    switch (type) {
      case 'user': return '👤';
      case 'success': return '✅';
      case 'error': return '❌';
      case 'system': return '🔧';
      default: return 'ℹ️';
    }
  };

  const getLogColor = (type) => {
    switch (type) {
      case 'user': return 'text-blue-600';
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'system': return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="max-w-full mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg">
        <h1 className="text-3xl font-bold mb-2">🖥️ AUTONOBOT Remote Desktop Agent</h1>
        <p className="text-blue-100">
          Advanced multi-agent system with real-time remote desktop visualization
        </p>
      </div>

      {/* Layout principal con dos columnas */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        
        {/* Columna izquierda: Controles y logs */}
        <div className="space-y-6">
          
          {/* System Status */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <Activity className="mr-2" size={20} />
              System Status
            </h2>
            
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center p-3 bg-gray-50 rounded">
                <div className="text-2xl mb-1">{getAgentStatusIcon('observer')}</div>
                <div className="text-sm font-medium">Observer Agent</div>
                <div className="text-xs text-gray-500">Visual Analysis</div>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded">
                <div className="text-2xl mb-1">{getAgentStatusIcon('search')}</div>
                <div className="text-sm font-medium">Search Agent</div>
                <div className="text-xs text-gray-500">Web Search</div>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded">
                <div className="text-2xl mb-1">{getAgentStatusIcon('navigation')}</div>
                <div className="text-sm font-medium">Navigation Agent</div>
                <div className="text-xs text-gray-500">Web Navigation</div>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded">
                <div className="text-2xl mb-1">{getAgentStatusIcon('response')}</div>
                <div className="text-sm font-medium">Response Agent</div>
                <div className="text-xs text-gray-500">AI Responses</div>
              </div>
            </div>

            {/* Remote Desktop Status */}
            <div className="border-t pt-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Monitor size={20} className={remoteDesktopConnected ? 'text-green-500' : 'text-red-500'} />
                  <span className="font-medium">Remote Desktop</span>
                  <span className={`text-sm px-2 py-1 rounded ${
                    remoteDesktopConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {remoteDesktopConnected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
                <button
                  onClick={restartRemoteDesktop}
                  className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                  title="Restart Remote Desktop"
                >
                  <RotateCcw size={16} />
                </button>
              </div>
            </div>
          </div>

          {/* Instruction Input */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Give Instructions</h2>
            
            <div className="space-y-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={instruction}
                  onChange={(e) => setInstruction(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && processInstruction()}
                  placeholder="Enter your instruction (e.g., 'Search for React tutorials and summarize the best practices')"
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={isProcessing}
                />
                <button
                  onClick={processInstruction}
                  disabled={!instruction.trim() || isProcessing || !remoteDesktopConnected}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isProcessing ? <Square size={20} /> : <Send size={20} />}
                  <span className="ml-2">{isProcessing ? 'Stop' : 'Process'}</span>
                </button>
              </div>

              {!remoteDesktopConnected && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="text-yellow-800 text-sm">
                    ⚠️ Remote desktop is not connected. Please wait for the connection to be established or restart the remote desktop service.
                  </p>
                </div>
              )}

              {/* Advanced Options Toggle */}
              <div className="flex items-center justify-between">
                <button
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                >
                  <Settings size={16} className="mr-1" />
                  Advanced Options
                </button>
                
                {isProcessing && (
                  <button
                    onClick={stopExecution}
                    className="px-4 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                  >
                    Stop Execution
                  </button>
                )}
              </div>

              {/* Advanced Options Panel */}
              {showAdvanced && (
                <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Analysis Type
                      </label>
                      <select
                        value={advancedOptions.analysisType}
                        onChange={(e) => setAdvancedOptions(prev => ({
                          ...prev,
                          analysisType: e.target.value
                        }))}
                        className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
                      >
                        <option value="interactive">Interactive Elements</option>
                        <option value="content">Content Analysis</option>
                        <option value="navigation">Navigation Focus</option>
                        <option value="general">General Analysis</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Max Retries
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="5"
                        value={advancedOptions.maxRetries}
                        onChange={(e) => setAdvancedOptions(prev => ({
                          ...prev,
                          maxRetries: parseInt(e.target.value)
                        }))}
                        className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
                      />
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="includeContent"
                      checked={advancedOptions.includeContent}
                      onChange={(e) => setAdvancedOptions(prev => ({
                        ...prev,
                        includeContent: e.target.checked
                      }))}
                      className="mr-2"
                    />
                    <label htmlFor="includeContent" className="text-sm text-gray-700">
                      Include detailed content analysis
                    </label>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Agent Logs */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Agent Activity Log</h2>
            
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm">
              {agentLogs.length === 0 ? (
                <div className="text-gray-500">Waiting for agent activity...</div>
              ) : (
                agentLogs.map((log) => (
                  <div key={log.id} className={`mb-1 ${getLogColor(log.type)}`}>
                    <span className="text-gray-400">[{log.timestamp}]</span>
                    <span className="ml-2">{getLogIcon(log.type)}</span>
                    <span className="ml-2">{log.message}</span>
                  </div>
                ))
              )}
              <div ref={logsEndRef} />
            </div>
          </div>
        </div>

        {/* Columna derecha: Remote Desktop Viewer */}
        <div className="space-y-6">
          <RemoteDesktopViewer
            vncUrl={vncUrl}
            onConnectionChange={handleRemoteDesktopConnection}
            onScreenshot={handleRemoteDesktopScreenshot}
            className="h-full"
          />

          {/* Current Response */}
          {currentResponse && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <MessageSquare className="mr-2" size={20} />
                Agent Response
              </h2>
              
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-blue-800">
                    {currentResponse.type === 'final' ? 'Final Result' : 'Progress Update'}
                  </span>
                  {currentResponse.success !== undefined && (
                    <span className={`text-sm px-2 py-1 rounded ${
                      currentResponse.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {currentResponse.success ? 'Success' : 'Failed'}
                    </span>
                  )}
                </div>
                
                <p className="text-gray-800 whitespace-pre-wrap">{currentResponse.message}</p>
                
                {currentResponse.summary && (
                  <div className="mt-3 pt-3 border-t border-blue-200">
                    <div className="text-sm text-blue-700">
                      <strong>Summary:</strong> {currentResponse.summary.stepsExecuted} steps executed, 
                      {currentResponse.summary.successRate}% success rate
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RemoteAutonomousAgent;
