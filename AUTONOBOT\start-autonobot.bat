@echo off
echo 🤖 Starting AUTONOBOT...
echo.

REM Change to the correct directory
cd /d "%~dp0"

echo 📁 Current directory: %CD%
echo.

REM Start the backend server in a new window
echo 🔧 Starting Backend Server...
start "AUTONOBOT Backend" cmd /k "node simple-server.js"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start the frontend development server in a new window
echo 🎨 Starting Frontend Server...
start "AUTONOBOT Frontend" cmd /k "npx vite"

REM Wait a moment for frontend to start
timeout /t 5 /nobreak >nul

echo.
echo ✅ AUTONOBOT is starting up!
echo.
echo 🌐 Frontend: http://localhost:5173
echo 🔧 Backend:  http://localhost:5000
echo 📊 Health:   http://localhost:5000/health
echo.
echo 🚀 Opening browser...
start http://localhost:5173

echo.
echo 📝 Note: Two command windows will open for the servers.
echo    Close those windows to stop AUTONOBOT.
echo.
pause
