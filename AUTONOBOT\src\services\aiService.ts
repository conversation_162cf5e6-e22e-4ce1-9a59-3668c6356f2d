import axios from 'axios';
import { GoogleGenerativeAI } from '@google/generative-ai';
import Anthropic from '@anthropic-ai/sdk';
import { searchService, SearchResult } from './searchService';

interface AIConfig {
  maxTokens: number;
  temperature: number;
  timeout: number;
  enableSearch: boolean;
  searchThreshold: number;
}

interface AIStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  searchRequests: number;
}

class AIService {
  private gemini: GoogleGenerativeAI;
  private anthropic: Anthropic;
  private currentModel: 'gemini' | 'anthropic' = 'gemini';
  private config: AIConfig;
  private stats: AIStats;

  constructor() {
    this.gemini = new GoogleGenerativeAI(import.meta.env.VITE_GEMINI_API_KEY);
    this.anthropic = new Anthropic({
      apiKey: import.meta.env.VITE_ANTHROPIC_API_KEY,
    });

    this.config = {
      maxTokens: 1024,
      temperature: 0.7,
      timeout: 30000,
      enableSearch: true,
      searchThreshold: 0.7 // Confidence threshold for triggering search
    };

    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      searchRequests: 0
    };
  }

  public async processCommand(command: string): Promise<string> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      console.log('Processing AI command:', command);

      // Check if command might benefit from web search
      const needsSearch = this.shouldPerformSearch(command);
      let searchContext = '';

      if (needsSearch && this.config.enableSearch) {
        try {
          console.log('Command requires web search, performing search...');
          const searchResults = await searchService.search(command, { maxResults: 5 });
          searchContext = this.formatSearchContext(searchResults.results);
          this.stats.searchRequests++;
          console.log('Search completed, integrating results into AI response');
        } catch (searchError) {
          console.warn('Search failed, proceeding without search context:', searchError);
        }
      }

      // Process with AI
      let response: string;
      if (this.currentModel === 'gemini') {
        response = await this.processWithGemini(command, searchContext);
      } else {
        response = await this.processWithAnthropic(command, searchContext);
      }

      // Update stats
      const responseTime = Date.now() - startTime;
      this.updateStats(responseTime, true);

      return response;

    } catch (error) {
      console.error('AI processing error:', error);
      this.updateStats(Date.now() - startTime, false);

      // Try switching models
      this.switchModel();

      // Retry once with the other model
      if (this.stats.totalRequests === 1) {
        return this.processCommand(command);
      }

      return 'I apologize, but I encountered an error processing your request. Please try again.';
    }
  }

  private shouldPerformSearch(command: string): boolean {
    const searchKeywords = [
      'search', 'find', 'look up', 'what is', 'who is', 'when did', 'where is',
      'how to', 'latest', 'recent', 'current', 'news', 'today', 'now',
      'price', 'cost', 'weather', 'stock', 'market', 'information about'
    ];

    const lowerCommand = command.toLowerCase();
    return searchKeywords.some(keyword => lowerCommand.includes(keyword));
  }

  private formatSearchContext(results: SearchResult[]): string {
    if (!results || results.length === 0) {
      return '';
    }

    const context = results.slice(0, 3).map((result, index) => {
      return `[${index + 1}] ${result.title}: ${result.snippet}`;
    }).join('\n');

    return `\n\nCurrent web search results:\n${context}\n\nPlease use this information to provide an accurate and up-to-date response.`;
  }

  private async processWithGemini(command: string, searchContext: string = ''): Promise<string> {
    const model = this.gemini.getGenerativeModel({ model: "gemini-pro" });
    const fullPrompt = command + searchContext;

    const result = await Promise.race([
      model.generateContent(fullPrompt),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Gemini timeout')), this.config.timeout)
      )
    ]);

    return (result as any).response.text();
  }

  private async processWithAnthropic(command: string, searchContext: string = ''): Promise<string> {
    const fullPrompt = command + searchContext;

    const message = await Promise.race([
      this.anthropic.messages.create({
        model: 'claude-3-opus-20240229',
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        messages: [{ role: 'user', content: fullPrompt }],
      }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Anthropic timeout')), this.config.timeout)
      )
    ]);

    return (message as any).content[0].text;
  }

  private updateStats(responseTime: number, success: boolean): void {
    if (success) {
      this.stats.successfulRequests++;
    } else {
      this.stats.failedRequests++;
    }

    // Update average response time
    const totalSuccessful = this.stats.successfulRequests;
    this.stats.averageResponseTime =
      (this.stats.averageResponseTime * (totalSuccessful - 1) + responseTime) / totalSuccessful;
  }

  private switchModel(): void {
    this.currentModel = this.currentModel === 'gemini' ? 'anthropic' : 'gemini';
    console.log(`Switched to ${this.currentModel} model`);
  }

  public setModel(model: 'gemini' | 'anthropic'): void {
    this.currentModel = model;
    console.log(`AI model set to: ${model}`);
  }

  public setConfig(config: Partial<AIConfig>): void {
    this.config = { ...this.config, ...config };
    console.log('AI config updated:', this.config);
  }

  public getStats(): AIStats {
    return { ...this.stats };
  }

  public getConfig(): AIConfig {
    return { ...this.config };
  }

  public getCurrentModel(): string {
    return this.currentModel;
  }

  public resetStats(): void {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      searchRequests: 0
    };
    console.log('AI stats reset');
  }

  // Method to directly search and get AI-processed results
  public async searchAndAnalyze(query: string): Promise<string> {
    try {
      console.log('Performing search and analysis for:', query);

      const searchResults = await searchService.search(query, { maxResults: 5 });
      this.stats.searchRequests++;

      if (searchResults.results.length === 0) {
        return 'No search results found for your query.';
      }

      const analysisPrompt = `Please analyze and summarize the following search results for the query "${query}":\n\n` +
        searchResults.results.map((result, index) =>
          `${index + 1}. ${result.title}\n${result.snippet}\nSource: ${result.url}\n`
        ).join('\n') +
        '\n\nProvide a comprehensive summary and answer based on these results.';

      return await this.processCommand(analysisPrompt);

    } catch (error) {
      console.error('Search and analysis failed:', error);
      return 'I encountered an error while searching and analyzing the information. Please try again.';
    }
  }
}

export const aiService = new AIService();