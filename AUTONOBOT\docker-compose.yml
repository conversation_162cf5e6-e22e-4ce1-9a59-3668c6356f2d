version: '3.8'

services:
  autonobot-desktop:
    build:
      context: ./docker
      dockerfile: Dockerfile
    container_name: autonobot-remote-desktop
    ports:
      - "5901:5901"   # VNC Server
      - "6901:6901"   # noVNC Web Interface
      - "8080:8080"   # WebSocket Bridge
      - "4444:4444"   # Selenium Grid (if needed)
    environment:
      - VNC_RESOLUTION=1920x1080
      - VNC_COL_DEPTH=24
      - DISPLAY=:1
    volumes:
      - ./screenshots:/home/<USER>/screenshots
      - ./logs:/var/log/supervisor
    restart: unless-stopped
    shm_size: 2gb
    networks:
      - autonobot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6901"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  autonobot-backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: autonobot-backend
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - REMOTE_DESKTOP_URL=ws://autonobot-desktop:8080
      - VNC_URL=http://autonobot-desktop:6901
    volumes:
      - ./screenshots:/app/screenshots
      - ./logs:/app/logs
    depends_on:
      - autonobot-desktop
    restart: unless-stopped
    networks:
      - autonobot-network

  autonobot-frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: autonobot-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_BACKEND_URL=http://localhost:5000
      - REACT_APP_VNC_URL=http://localhost:6901
    depends_on:
      - autonobot-backend
    restart: unless-stopped
    networks:
      - autonobot-network

networks:
  autonobot-network:
    driver: bridge

volumes:
  screenshots:
  logs:
