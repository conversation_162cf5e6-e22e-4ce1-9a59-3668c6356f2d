import React, { useState, useEffect, useRef } from 'react';
import { Monitor, Maximize2, Minimize2, RotateCcw, Settings, Wifi, WifiOff } from 'lucide-react';

const RemoteDesktopViewer = ({ 
  vncUrl = 'http://localhost:6901',
  onConnectionChange,
  onScreenshot,
  className = ''
}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [scale, setScale] = useState(0.8);
  const [quality, setQuality] = useState(6);
  const [showSettings, setShowSettings] = useState(false);
  
  const iframeRef = useRef(null);
  const containerRef = useRef(null);

  useEffect(() => {
    // Configurar el iframe con noVNC
    setupVNCConnection();
    
    // Cleanup al desmontar
    return () => {
      if (iframeRef.current) {
        iframeRef.current.src = 'about:blank';
      }
    };
  }, [vncUrl]);

  const setupVNCConnection = () => {
    setIsLoading(true);
    setError(null);
    
    try {
      if (iframeRef.current) {
        // Construir URL de noVNC con parámetros
        const vncParams = new URLSearchParams({
          autoconnect: 'true',
          resize: 'scale',
          quality: quality,
          compression: '2',
          password: 'autonobot'
        });
        
        const fullVncUrl = `${vncUrl}/vnc.html?${vncParams.toString()}`;
        iframeRef.current.src = fullVncUrl;
        
        // Simular conexión exitosa después de un delay
        setTimeout(() => {
          setIsLoading(false);
          setIsConnected(true);
          onConnectionChange?.(true);
        }, 3000);
      }
    } catch (err) {
      setError(`Failed to connect to remote desktop: ${err.message}`);
      setIsLoading(false);
      setIsConnected(false);
      onConnectionChange?.(false);
    }
  };

  const handleReconnect = () => {
    setIsConnected(false);
    setupVNCConnection();
  };

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (containerRef.current?.requestFullscreen) {
        containerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  const handleScaleChange = (newScale) => {
    setScale(newScale);
    // Aplicar escala al iframe
    if (iframeRef.current) {
      iframeRef.current.style.transform = `scale(${newScale})`;
      iframeRef.current.style.transformOrigin = 'top left';
    }
  };

  const handleQualityChange = (newQuality) => {
    setQuality(newQuality);
    // Reconectar con nueva calidad
    handleReconnect();
  };

  const captureScreenshot = () => {
    // Enviar comando para capturar screenshot
    onScreenshot?.();
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
    
    // Intentar detectar si la conexión VNC fue exitosa
    try {
      const iframe = iframeRef.current;
      if (iframe && iframe.contentWindow) {
        // Aplicar escala inicial
        iframe.style.transform = `scale(${scale})`;
        iframe.style.transformOrigin = 'top left';
        
        setIsConnected(true);
        onConnectionChange?.(true);
      }
    } catch (err) {
      console.warn('Cannot access iframe content:', err);
      // Asumir conexión exitosa si no hay errores obvios
      setIsConnected(true);
      onConnectionChange?.(true);
    }
  };

  const handleIframeError = () => {
    setError('Failed to load remote desktop');
    setIsLoading(false);
    setIsConnected(false);
    onConnectionChange?.(false);
  };

  return (
    <div 
      ref={containerRef}
      className={`relative bg-gray-900 rounded-lg overflow-hidden ${className}`}
    >
      {/* Header con controles */}
      <div className="bg-gray-800 px-4 py-2 flex items-center justify-between border-b border-gray-700">
        <div className="flex items-center space-x-3">
          <Monitor size={20} className="text-blue-400" />
          <span className="text-white font-medium">Remote Desktop</span>
          <div className="flex items-center space-x-2">
            {isConnected ? (
              <Wifi size={16} className="text-green-400" />
            ) : (
              <WifiOff size={16} className="text-red-400" />
            )}
            <span className={`text-sm ${isConnected ? 'text-green-400' : 'text-red-400'}`}>
              {isLoading ? 'Connecting...' : isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Control de escala */}
          <select
            value={scale}
            onChange={(e) => handleScaleChange(parseFloat(e.target.value))}
            className="bg-gray-700 text-white text-sm px-2 py-1 rounded border border-gray-600"
          >
            <option value={0.5}>50%</option>
            <option value={0.6}>60%</option>
            <option value={0.7}>70%</option>
            <option value={0.8}>80%</option>
            <option value={0.9}>90%</option>
            <option value={1.0}>100%</option>
          </select>
          
          {/* Botón de configuración */}
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-1 text-gray-400 hover:text-white transition-colors"
            title="Settings"
          >
            <Settings size={16} />
          </button>
          
          {/* Botón de reconexión */}
          <button
            onClick={handleReconnect}
            className="p-1 text-gray-400 hover:text-white transition-colors"
            title="Reconnect"
          >
            <RotateCcw size={16} />
          </button>
          
          {/* Botón de pantalla completa */}
          <button
            onClick={toggleFullscreen}
            className="p-1 text-gray-400 hover:text-white transition-colors"
            title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
          >
            {isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
          </button>
        </div>
      </div>

      {/* Panel de configuración */}
      {showSettings && (
        <div className="absolute top-12 right-4 bg-gray-800 border border-gray-600 rounded-lg p-4 z-10 min-w-64">
          <h3 className="text-white font-medium mb-3">VNC Settings</h3>
          
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-gray-300 mb-1">Quality</label>
              <select
                value={quality}
                onChange={(e) => handleQualityChange(parseInt(e.target.value))}
                className="w-full bg-gray-700 text-white text-sm px-2 py-1 rounded border border-gray-600"
              >
                <option value={0}>Poor</option>
                <option value={3}>Low</option>
                <option value={6}>Medium</option>
                <option value={9}>High</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-300 mb-1">Scale</label>
              <input
                type="range"
                min="0.3"
                max="1.5"
                step="0.1"
                value={scale}
                onChange={(e) => handleScaleChange(parseFloat(e.target.value))}
                className="w-full"
              />
              <span className="text-xs text-gray-400">{Math.round(scale * 100)}%</span>
            </div>
            
            <button
              onClick={captureScreenshot}
              className="w-full bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700 transition-colors"
            >
              Capture Screenshot
            </button>
          </div>
        </div>
      )}

      {/* Área del visor */}
      <div className="relative" style={{ height: '600px' }}>
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
              <p className="text-white">Connecting to remote desktop...</p>
              <p className="text-gray-400 text-sm mt-2">This may take a few moments</p>
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
            <div className="text-center">
              <div className="text-red-400 text-6xl mb-4">⚠️</div>
              <p className="text-white mb-2">Connection Failed</p>
              <p className="text-gray-400 text-sm mb-4">{error}</p>
              <button
                onClick={handleReconnect}
                className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        {/* iframe para noVNC */}
        <iframe
          ref={iframeRef}
          className="w-full h-full border-0"
          style={{
            transform: `scale(${scale})`,
            transformOrigin: 'top left',
            width: `${100 / scale}%`,
            height: `${100 / scale}%`
          }}
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          title="Remote Desktop"
          sandbox="allow-same-origin allow-scripts allow-forms allow-pointer-lock"
        />
      </div>

      {/* Información de estado */}
      <div className="bg-gray-800 px-4 py-2 border-t border-gray-700">
        <div className="flex items-center justify-between text-sm">
          <div className="text-gray-400">
            Scale: {Math.round(scale * 100)}% | Quality: {quality}/9
          </div>
          <div className="text-gray-400">
            {vncUrl}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RemoteDesktopViewer;
