const { chromium } = require('playwright');
const cheerio = require('cheerio');

let browser;
let page;

// Initialize headless browser
async function initializeBrowser() {
  if (!browser) {
    try {
      browser = await chromium.launch({ headless: true });
      page = await browser.newPage();
      return true;
    } catch (error) {
      console.error('Failed to initialize browser:', error);
      throw error;
    }
  }
  return true;
}

// Close browser
async function closeBrowser() {
  if (browser) {
    try {
      await browser.close();
      browser = null;
      page = null;
    } catch (error) {
      console.error('Error closing browser:', error);
    }
  }
}

// Navigate to URL
async function navigateTo(url) {
  if (!page) throw new Error('Browser not initialized.');
  try {
    await page.goto(url, { waitUntil: 'domcontentloaded' });
    return page.url();
  } catch (error) {
    console.error(`Navigation error to ${url}:`, error);
    throw error;
  }
}

// Get current page HTML
async function getPageHtml() {
  if (!page) throw new Error('Browser not initialized.');
  return await page.content();
}

// Extract visible text from HTML
function extractVisibleText(html) {
  const $ = cheerio.load(html);
  
  // Remove script and style elements
  $('script, style').remove();
  
  // Get text content
  return $('body').text().replace(/\s+/g, ' ').trim();
}

// Extract interactive elements
function extractInteractiveElements(html) {
  const $ = cheerio.load(html);
  const elements = [];
  
  // Find interactive elements
  $('a, button, input, select, textarea').each((i, el) => {
    const $el = $(el);
    const tagName = $el.prop('tagName').toLowerCase();
    const id = $el.attr('id') || `element_${i}`;
    let text = $el.text().trim() || $el.attr('placeholder') || $el.attr('value') || $el.attr('name') || '';
    let description = text || tagName;
    
    if (tagName === 'a') {
      const href = $el.attr('href') || '';
      description = `Link: ${description} (URL: ${href})`;
    } else if (tagName === 'input') {
      const type = $el.attr('type') || 'text';
      description = `Input: ${description} (Type: ${type})`;
    } else if (tagName === 'button') {
      description = `Button: ${description}`;
    } else if (tagName === 'select') {
      description = `Dropdown: ${description}`;
    } else if (tagName === 'textarea') {
      description = `Text area: ${description}`;
    }
    
    elements.push({ 
      id, 
      description, 
      tagName, 
      href: $el.attr('href'), 
      type: $el.attr('type'),
      name: $el.attr('name'),
      value: $el.attr('value')
    });
  });
  
  return elements;
}

// Click on element
async function clickElement(elementId) {
  if (!page) throw new Error('Browser not initialized.');
  try {
    // Try different selectors
    let clicked = false;
    
    // Try by ID
    try {
      await page.click(`#${elementId}`);
      clicked = true;
    } catch (e) {
      // ID didn't work, try other attributes
    }
    
    // Try by other selectors if ID failed
    if (!clicked) {
      // Construct a more complex selector
      const selector = `[id="${elementId}"], [name="${elementId}"], a:has-text("${elementId}"), button:has-text("${elementId}")`;
      await page.click(selector);
    }
    
    return true;
  } catch (error) {
    console.error(`Click error on element ${elementId}:`, error);
    return false;
  }
}

// Type text into input
async function typeText(elementId, text) {
  if (!page) throw new Error('Browser not initialized.');
  try {
    // Try different selectors
    let typed = false;
    
    // Try by ID
    try {
      await page.fill(`#${elementId}`, text);
      typed = true;
    } catch (e) {
      // ID didn't work, try other attributes
    }
    
    // Try by other selectors if ID failed
    if (!typed) {
      // Construct a more complex selector
      const selector = `[id="${elementId}"], [name="${elementId}"]`;
      await page.fill(selector, text);
    }
    
    return true;
  } catch (error) {
    console.error(`Type error on element ${elementId}:`, error);
    return false;
  }
}

// Wait for navigation to complete
async function waitForNavigation() {
  if (!page) throw new Error('Browser not initialized.');
  try {
    await page.waitForLoadState('domcontentloaded');
    return true;
  } catch (error) {
    console.error('Navigation wait error:', error);
    return false;
  }
}

// Take screenshot (for debugging)
async function takeScreenshot() {
  if (!page) throw new Error('Browser not initialized.');
  try {
    return await page.screenshot({ type: 'jpeg', quality: 80 });
  } catch (error) {
    console.error('Screenshot error:', error);
    return null;
  }
}

module.exports = {
  initializeBrowser,
  closeBrowser,
  navigateTo,
  getPageHtml,
  extractVisibleText,
  extractInteractiveElements,
  clickElement,
  typeText,
  waitForNavigation,
  takeScreenshot,
  getCurrentUrl: () => page ? page.url() : null
};