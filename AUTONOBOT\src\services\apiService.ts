import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api/autonobot';

// Types
interface AgentStatus {
  log: string[];
  status: 'idle' | 'running' | 'finished';
  results: string;
  currentUrl: string;
}

interface StartTaskResponse {
  message: string;
  status: string;
}

// API Functions
export const fetchAgentStatus = async (): Promise<AgentStatus> => {
  try {
    const response = await axios.get(`${API_BASE_URL}/status`);
    return response.data;
  } catch (error) {
    console.error('Error fetching agent status:', error);
    throw error;
  }
};

export const startAgentTask = async (taskInput: string): Promise<StartTaskResponse> => {
  try {
    const response = await axios.post(`${API_BASE_URL}/start-task`, { taskInput });
    return response.data;
  } catch (error) {
    console.error('Error starting agent task:', error);
    throw error;
  }
};