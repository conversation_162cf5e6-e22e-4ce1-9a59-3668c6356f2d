import express from 'express';
import cors from 'cors';

console.log('🚀 Starting debug server...');

try {
  console.log('📦 Express and CORS loaded successfully');

  console.log('🏗️ Creating Express app...');
  const app = express();
  const port = process.env.PORT || 5000;
  console.log('✅ Express app created');

  console.log('⚙️ Setting up middleware...');
  app.use(cors());
  app.use(express.json());
  console.log('✅ Middleware configured');

  console.log('🛣️ Setting up routes...');

  app.get('/', (req, res) => {
    console.log('📥 Root request received');
    res.json({
      message: 'AUTONOBOT Debug Server is running!',
      timestamp: new Date().toISOString()
    });
  });

  app.get('/test', (req, res) => {
    console.log('📥 Test request received');
    res.json({
      status: 'working',
      message: 'Test endpoint is functional',
      timestamp: new Date().toISOString()
    });
  });

  app.post('/api/autonomous/process', (req, res) => {
    console.log('📥 Autonomous process request received');
    const { instruction } = req.body;
    res.json({
      success: true,
      message: 'Debug server received instruction',
      instruction: instruction || 'No instruction provided',
      timestamp: new Date().toISOString()
    });
  });

  console.log('✅ Routes configured');

  console.log('🎧 Starting server...');
  app.listen(port, () => {
    console.log('\n🎉 SUCCESS! Debug server is running');
    console.log(`🌐 Server URL: http://localhost:${port}`);
    console.log(`🧪 Test URL: http://localhost:${port}/test`);
    console.log(`🤖 API URL: http://localhost:${port}/api/autonomous/process`);
    console.log('\n✨ Server is ready for testing!');
  });

} catch (error) {
  console.error('💥 Error starting server:', error);
  console.error('Stack trace:', error.stack);
  process.exit(1);
}
