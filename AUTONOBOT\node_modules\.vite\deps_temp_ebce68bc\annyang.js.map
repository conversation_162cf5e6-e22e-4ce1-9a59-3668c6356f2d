{"version": 3, "sources": ["../../annyang/dist/annyang.min.js"], "sourcesContent": ["\"use strict\";var _typeof=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e};\n//! annyang\n//! version : 2.6.1\n//! author  : <PERSON><PERSON> @TalAter\n//! license : MIT\n//! https://www.TalAter.com/annyang/\n//! annyang\n//! version : 2.6.1\n//! author  : <PERSON><PERSON><PERSON> @TalAter\n//! license : MIT\n//! https://www.TalAter.com/annyang/\n!function(e,n){\"function\"==typeof define&&define.amd?define([],function(){return e.annyang=n(e)}):\"object\"===(\"undefined\"==typeof module?\"undefined\":_typeof(module))&&module.exports?module.exports=n(e):e.annyang=n(e)}(\"undefined\"!=typeof window?window:void 0,function(r,i){var t,o=r.SpeechRecognition||r.webkitSpeechRecognition||r.mozSpeechRecognition||r.msSpeechRecognition||r.oSpeechRecognition;if(!o)return null;var a,c,s=[],u={start:[],error:[],end:[],soundstart:[],result:[],resultMatch:[],resultNoMatch:[],errorNetwork:[],errorPermissionBlocked:[],errorPermissionDenied:[]},f=0,l=0,d=!1,p=\"font-weight: bold; color: #00f;\",g=!1,m=!1,h=/\\s*\\((.*?)\\)\\s*/g,y=/(\\(\\?:[^)]+\\))\\?/g,b=/(\\(\\?)?:\\w+/g,v=/\\*\\w+/g,w=/[\\-{}\\[\\]+?.,\\\\\\^$|#]/g,S=function(e){for(var n=arguments.length,t=Array(1<n?n-1:0),o=1;o<n;o++)t[o-1]=arguments[o];e.forEach(function(e){e.callback.apply(e.context,t)})},e=function(){return a!==i},k=function(e,n){-1!==e.indexOf(\"%c\")||n?console.log(e,n||p):console.log(e)},x=function(){e()||t.init({},!1)},R=function(e,n,t){s.push({command:e,callback:n,originalPhrase:t}),d&&k(\"Command successfully loaded: %c\"+t,p)},P=function(e){var n;S(u.result,e);for(var t=0;t<e.length;t++){n=e[t].trim(),d&&k(\"Speech recognized: %c\"+n,p);for(var o=0,r=s.length;o<r;o++){var i=s[o],a=i.command.exec(n);if(a){var c=a.slice(1);return d&&(k(\"command matched: %c\"+i.originalPhrase,p),c.length&&k(\"with parameters\",c)),i.callback.apply(this,c),void S(u.resultMatch,n,i.originalPhrase,e)}}}S(u.resultNoMatch,e)};return t={init:function(e){var n=!(1<arguments.length&&arguments[1]!==i)||arguments[1];a&&a.abort&&a.abort(),(a=new o).maxAlternatives=5,a.continuous=\"http:\"===r.location.protocol,a.lang=\"en-US\",a.onstart=function(){m=!0,S(u.start)},a.onsoundstart=function(){S(u.soundstart)},a.onerror=function(e){switch(S(u.error,e),e.error){case\"network\":S(u.errorNetwork,e);break;case\"not-allowed\":case\"service-not-allowed\":c=!1,(new Date).getTime()-f<200?S(u.errorPermissionBlocked,e):S(u.errorPermissionDenied,e)}},a.onend=function(){if(m=!1,S(u.end),c){var e=(new Date).getTime()-f;(l+=1)%10==0&&d&&k(\"Speech Recognition is repeatedly stopping and starting. See http://is.gd/annyang_restarts for tips.\"),e<1e3?setTimeout(function(){t.start({paused:g})},1e3-e):t.start({paused:g})}},a.onresult=function(e){if(g)return d&&k(\"Speech heard, but annyang is paused\"),!1;for(var n=e.results[e.resultIndex],t=[],o=0;o<n.length;o++)t[o]=n[o].transcript;P(t)},n&&(s=[]),e.length&&this.addCommands(e)},start:function(e){x(),g=(e=e||{}).paused!==i&&!!e.paused,c=e.autoRestart===i||!!e.autoRestart,e.continuous!==i&&(a.continuous=!!e.continuous),f=(new Date).getTime();try{a.start()}catch(e){d&&k(e.message)}},abort:function(){c=!1,l=0,e()&&a.abort()},pause:function(){g=!0},resume:function(){t.start()},debug:function(){var e=!(0<arguments.length&&arguments[0]!==i)||arguments[0];d=!!e},setLanguage:function(e){x(),a.lang=e},addCommands:function(e){var n,t;for(var o in x(),e)if(e.hasOwnProperty(o))if(\"function\"==typeof(n=r[e[o]]||e[o]))R((t=(t=o).replace(w,\"\\\\$&\").replace(h,\"(?:$1)?\").replace(b,function(e,n){return n?e:\"([^\\\\s]+)\"}).replace(v,\"(.*?)\").replace(y,\"\\\\s*$1?\\\\s*\"),new RegExp(\"^\"+t+\"$\",\"i\")),n,o);else{if(!(\"object\"===(void 0===n?\"undefined\":_typeof(n))&&n.regexp instanceof RegExp)){d&&k(\"Can not register command: %c\"+o,p);continue}R(new RegExp(n.regexp.source,\"i\"),n.callback,o)}},removeCommands:function(t){t===i?s=[]:(t=Array.isArray(t)?t:[t],s=s.filter(function(e){for(var n=0;n<t.length;n++)if(t[n]===e.originalPhrase)return!1;return!0}))},addCallback:function(e,n,t){var o=r[n]||n;\"function\"==typeof o&&u[e]!==i&&u[e].push({callback:o,context:t||this})},removeCallback:function(e,n){var t=function(e){return e.callback!==n};for(var o in u)u.hasOwnProperty(o)&&(e!==i&&e!==o||(u[o]=n===i?[]:u[o].filter(t)))},isListening:function(){return m&&!g},getSpeechRecognizer:function(){return a},trigger:function(e){t.isListening()?(Array.isArray(e)||(e=[e]),P(e)):d&&k(m?\"Speech heard, but annyang is paused\":\"Cannot trigger while annyang is aborted\")}}});"], "mappings": ";;;;;AAAA;AAAA;AAAa,QAAI,UAAQ,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,WAAS,SAAS,GAAE;AAAC,aAAO,OAAO;AAAA,IAAC,IAAE,SAAS,GAAE;AAAC,aAAO,KAAG,cAAY,OAAO,UAAQ,EAAE,gBAAc,UAAQ,MAAI,OAAO,YAAU,WAAS,OAAO;AAAA,IAAC;AAWjO,KAAC,SAAS,GAAE,GAAE;AAAC,oBAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,GAAE,WAAU;AAAC,eAAO,EAAE,UAAQ,EAAE,CAAC;AAAA,MAAC,CAAC,IAAE,cAAY,eAAa,OAAO,SAAO,cAAY,QAAQ,MAAM,MAAI,OAAO,UAAQ,OAAO,UAAQ,EAAE,CAAC,IAAE,EAAE,UAAQ,EAAE,CAAC;AAAA,IAAC,EAAE,eAAa,OAAO,SAAO,SAAO,QAAO,SAAS,GAAE,GAAE;AAAC,UAAI,GAAE,IAAE,EAAE,qBAAmB,EAAE,2BAAyB,EAAE,wBAAsB,EAAE,uBAAqB,EAAE;AAAmB,UAAG,CAAC,EAAE,QAAO;AAAK,UAAI,GAAE,GAAE,IAAE,CAAC,GAAE,IAAE,EAAC,OAAM,CAAC,GAAE,OAAM,CAAC,GAAE,KAAI,CAAC,GAAE,YAAW,CAAC,GAAE,QAAO,CAAC,GAAE,aAAY,CAAC,GAAE,eAAc,CAAC,GAAE,cAAa,CAAC,GAAE,wBAAuB,CAAC,GAAE,uBAAsB,CAAC,EAAC,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,OAAG,IAAE,mCAAkC,IAAE,OAAG,IAAE,OAAG,IAAE,oBAAmB,IAAE,qBAAoB,IAAE,gBAAe,IAAE,UAAS,IAAE,0BAAyB,IAAE,SAASA,IAAE;AAAC,iBAAQ,IAAE,UAAU,QAAOC,KAAE,MAAM,IAAE,IAAE,IAAE,IAAE,CAAC,GAAEC,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAD,GAAEC,KAAE,CAAC,IAAE,UAAUA,EAAC;AAAE,QAAAF,GAAE,QAAQ,SAASA,IAAE;AAAC,UAAAA,GAAE,SAAS,MAAMA,GAAE,SAAQC,EAAC;AAAA,QAAC,CAAC;AAAA,MAAC,GAAE,IAAE,WAAU;AAAC,eAAO,MAAI;AAAA,MAAC,GAAE,IAAE,SAASD,IAAE,GAAE;AAAC,eAAKA,GAAE,QAAQ,IAAI,KAAG,IAAE,QAAQ,IAAIA,IAAE,KAAG,CAAC,IAAE,QAAQ,IAAIA,EAAC;AAAA,MAAC,GAAE,IAAE,WAAU;AAAC,UAAE,KAAG,EAAE,KAAK,CAAC,GAAE,KAAE;AAAA,MAAC,GAAE,IAAE,SAASA,IAAE,GAAEC,IAAE;AAAC,UAAE,KAAK,EAAC,SAAQD,IAAE,UAAS,GAAE,gBAAeC,GAAC,CAAC,GAAE,KAAG,EAAE,oCAAkCA,IAAE,CAAC;AAAA,MAAC,GAAE,IAAE,SAASD,IAAE;AAAC,YAAI;AAAE,UAAE,EAAE,QAAOA,EAAC;AAAE,iBAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,cAAED,GAAEC,EAAC,EAAE,KAAK,GAAE,KAAG,EAAE,0BAAwB,GAAE,CAAC;AAAE,mBAAQC,KAAE,GAAEC,KAAE,EAAE,QAAOD,KAAEC,IAAED,MAAI;AAAC,gBAAIE,KAAE,EAAEF,EAAC,GAAEG,KAAED,GAAE,QAAQ,KAAK,CAAC;AAAE,gBAAGC,IAAE;AAAC,kBAAIC,KAAED,GAAE,MAAM,CAAC;AAAE,qBAAO,MAAI,EAAE,wBAAsBD,GAAE,gBAAe,CAAC,GAAEE,GAAE,UAAQ,EAAE,mBAAkBA,EAAC,IAAGF,GAAE,SAAS,MAAM,MAAKE,EAAC,GAAE,KAAK,EAAE,EAAE,aAAY,GAAEF,GAAE,gBAAeJ,EAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAC,UAAE,EAAE,eAAcA,EAAC;AAAA,MAAC;AAAE,aAAO,IAAE,EAAC,MAAK,SAASA,IAAE;AAAC,YAAI,IAAE,EAAE,IAAE,UAAU,UAAQ,UAAU,CAAC,MAAI,MAAI,UAAU,CAAC;AAAE,aAAG,EAAE,SAAO,EAAE,MAAM,IAAG,IAAE,IAAI,KAAG,kBAAgB,GAAE,EAAE,aAAW,YAAU,EAAE,SAAS,UAAS,EAAE,OAAK,SAAQ,EAAE,UAAQ,WAAU;AAAC,cAAE,MAAG,EAAE,EAAE,KAAK;AAAA,QAAC,GAAE,EAAE,eAAa,WAAU;AAAC,YAAE,EAAE,UAAU;AAAA,QAAC,GAAE,EAAE,UAAQ,SAASA,IAAE;AAAC,kBAAO,EAAE,EAAE,OAAMA,EAAC,GAAEA,GAAE,OAAM;AAAA,YAAC,KAAI;AAAU,gBAAE,EAAE,cAAaA,EAAC;AAAE;AAAA,YAAM,KAAI;AAAA,YAAc,KAAI;AAAsB,kBAAE,QAAI,oBAAI,QAAM,QAAQ,IAAE,IAAE,MAAI,EAAE,EAAE,wBAAuBA,EAAC,IAAE,EAAE,EAAE,uBAAsBA,EAAC;AAAA,UAAC;AAAA,QAAC,GAAE,EAAE,QAAM,WAAU;AAAC,cAAG,IAAE,OAAG,EAAE,EAAE,GAAG,GAAE,GAAE;AAAC,gBAAIA,MAAG,oBAAI,QAAM,QAAQ,IAAE;AAAE,aAAC,KAAG,KAAG,MAAI,KAAG,KAAG,EAAE,qGAAqG,GAAEA,KAAE,MAAI,WAAW,WAAU;AAAC,gBAAE,MAAM,EAAC,QAAO,EAAC,CAAC;AAAA,YAAC,GAAE,MAAIA,EAAC,IAAE,EAAE,MAAM,EAAC,QAAO,EAAC,CAAC;AAAA,UAAC;AAAA,QAAC,GAAE,EAAE,WAAS,SAASA,IAAE;AAAC,cAAG,EAAE,QAAO,KAAG,EAAE,qCAAqC,GAAE;AAAG,mBAAQO,KAAEP,GAAE,QAAQA,GAAE,WAAW,GAAEC,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAEK,GAAE,QAAOL,KAAI,CAAAD,GAAEC,EAAC,IAAEK,GAAEL,EAAC,EAAE;AAAW,YAAED,EAAC;AAAA,QAAC,GAAE,MAAI,IAAE,CAAC,IAAGD,GAAE,UAAQ,KAAK,YAAYA,EAAC;AAAA,MAAC,GAAE,OAAM,SAASA,IAAE;AAAC,UAAE,GAAE,KAAGA,KAAEA,MAAG,CAAC,GAAG,WAAS,KAAG,CAAC,CAACA,GAAE,QAAO,IAAEA,GAAE,gBAAc,KAAG,CAAC,CAACA,GAAE,aAAYA,GAAE,eAAa,MAAI,EAAE,aAAW,CAAC,CAACA,GAAE,aAAY,KAAG,oBAAI,QAAM,QAAQ;AAAE,YAAG;AAAC,YAAE,MAAM;AAAA,QAAC,SAAOA,IAAE;AAAC,eAAG,EAAEA,GAAE,OAAO;AAAA,QAAC;AAAA,MAAC,GAAE,OAAM,WAAU;AAAC,YAAE,OAAG,IAAE,GAAE,EAAE,KAAG,EAAE,MAAM;AAAA,MAAC,GAAE,OAAM,WAAU;AAAC,YAAE;AAAA,MAAE,GAAE,QAAO,WAAU;AAAC,UAAE,MAAM;AAAA,MAAC,GAAE,OAAM,WAAU;AAAC,YAAIA,KAAE,EAAE,IAAE,UAAU,UAAQ,UAAU,CAAC,MAAI,MAAI,UAAU,CAAC;AAAE,YAAE,CAAC,CAACA;AAAA,MAAC,GAAE,aAAY,SAASA,IAAE;AAAC,UAAE,GAAE,EAAE,OAAKA;AAAA,MAAC,GAAE,aAAY,SAASA,IAAE;AAAC,YAAI,GAAEC;AAAE,iBAAQC,MAAK,EAAE,GAAEF,GAAE,KAAGA,GAAE,eAAeE,EAAC,EAAE,KAAG,cAAY,QAAO,IAAE,EAAEF,GAAEE,EAAC,CAAC,KAAGF,GAAEE,EAAC,GAAG,IAAGD,MAAGA,KAAEC,IAAG,QAAQ,GAAE,MAAM,EAAE,QAAQ,GAAE,SAAS,EAAE,QAAQ,GAAE,SAASF,IAAEO,IAAE;AAAC,iBAAOA,KAAEP,KAAE;AAAA,QAAW,CAAC,EAAE,QAAQ,GAAE,OAAO,EAAE,QAAQ,GAAE,aAAa,GAAE,IAAI,OAAO,MAAIC,KAAE,KAAI,GAAG,IAAG,GAAEC,EAAC;AAAA,aAAM;AAAC,cAAG,EAAE,cAAY,WAAS,IAAE,cAAY,QAAQ,CAAC,MAAI,EAAE,kBAAkB,SAAQ;AAAC,iBAAG,EAAE,iCAA+BA,IAAE,CAAC;AAAE;AAAA,UAAQ;AAAC,YAAE,IAAI,OAAO,EAAE,OAAO,QAAO,GAAG,GAAE,EAAE,UAASA,EAAC;AAAA,QAAC;AAAA,MAAC,GAAE,gBAAe,SAASD,IAAE;AAAC,QAAAA,OAAI,IAAE,IAAE,CAAC,KAAGA,KAAE,MAAM,QAAQA,EAAC,IAAEA,KAAE,CAACA,EAAC,GAAE,IAAE,EAAE,OAAO,SAASD,IAAE;AAAC,mBAAQ,IAAE,GAAE,IAAEC,GAAE,QAAO,IAAI,KAAGA,GAAE,CAAC,MAAID,GAAE,eAAe,QAAM;AAAG,iBAAM;AAAA,QAAE,CAAC;AAAA,MAAE,GAAE,aAAY,SAASA,IAAE,GAAEC,IAAE;AAAC,YAAIC,KAAE,EAAE,CAAC,KAAG;AAAE,sBAAY,OAAOA,MAAG,EAAEF,EAAC,MAAI,KAAG,EAAEA,EAAC,EAAE,KAAK,EAAC,UAASE,IAAE,SAAQD,MAAG,KAAI,CAAC;AAAA,MAAC,GAAE,gBAAe,SAASD,IAAE,GAAE;AAAC,YAAIC,KAAE,SAASD,IAAE;AAAC,iBAAOA,GAAE,aAAW;AAAA,QAAC;AAAE,iBAAQE,MAAK,EAAE,GAAE,eAAeA,EAAC,MAAIF,OAAI,KAAGA,OAAIE,OAAI,EAAEA,EAAC,IAAE,MAAI,IAAE,CAAC,IAAE,EAAEA,EAAC,EAAE,OAAOD,EAAC;AAAA,MAAG,GAAE,aAAY,WAAU;AAAC,eAAO,KAAG,CAAC;AAAA,MAAC,GAAE,qBAAoB,WAAU;AAAC,eAAO;AAAA,MAAC,GAAE,SAAQ,SAASD,IAAE;AAAC,UAAE,YAAY,KAAG,MAAM,QAAQA,EAAC,MAAIA,KAAE,CAACA,EAAC,IAAG,EAAEA,EAAC,KAAG,KAAG,EAAE,IAAE,wCAAsC,yCAAyC;AAAA,MAAC,EAAC;AAAA,IAAC,CAAC;AAAA;AAAA;", "names": ["e", "t", "o", "r", "i", "a", "c", "n"]}