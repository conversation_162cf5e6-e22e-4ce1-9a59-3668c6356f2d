import EventEmitter from 'events';
import { v4 as uuidv4 } from 'uuid';
import { loggers } from '../services/loggerService.js';

/**
 * Task Manager - Gestiona el ciclo de vida de las tareas
 * Crea, actualiza, completa y cancela tareas del sistema
 */
export class TaskManager extends EventEmitter {
  constructor() {
    super();
    this.tasks = new Map();
    this.taskHistory = [];
    this.maxHistorySize = 1000;
    
    loggers.main.info('Task Manager initialized');
  }

  /**
   * Crea una nueva tarea
   */
  async createTask(taskData) {
    try {
      const task = {
        id: uuidv4(),
        instruction: taskData.instruction,
        type: taskData.type || 'general',
        priority: taskData.priority || 'normal',
        status: 'created',
        progress: 0,
        context: taskData.context || {},
        timeout: taskData.timeout || 300000, // 5 minutos por defecto
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        startedAt: null,
        completedAt: null,
        result: null,
        error: null,
        steps: [],
        metadata: {
          estimatedDuration: taskData.estimatedDuration || null,
          complexity: taskData.complexity || 'medium',
          requiredAgents: taskData.requiredAgents || []
        }
      };
      
      this.tasks.set(task.id, task);
      
      loggers.main.info('Task created', { 
        taskId: task.id, 
        instruction: task.instruction,
        type: task.type 
      });
      
      this.emit('task:created', task);
      
      return task;
      
    } catch (error) {
      loggers.main.error('Error creating task', { error: error.message });
      throw error;
    }
  }

  /**
   * Inicia la ejecución de una tarea
   */
  async startTask(taskId) {
    try {
      const task = this.tasks.get(taskId);
      if (!task) {
        throw new Error(`Task not found: ${taskId}`);
      }
      
      if (task.status !== 'created') {
        throw new Error(`Task ${taskId} cannot be started. Current status: ${task.status}`);
      }
      
      task.status = 'running';
      task.startedAt = new Date().toISOString();
      task.updatedAt = new Date().toISOString();
      
      // Configurar timeout
      if (task.timeout) {
        setTimeout(() => {
          if (task.status === 'running') {
            this.timeoutTask(taskId);
          }
        }, task.timeout);
      }
      
      loggers.main.info('Task started', { taskId, instruction: task.instruction });
      
      this.emit('task:started', task);
      
      return task;
      
    } catch (error) {
      loggers.main.error('Error starting task', { taskId, error: error.message });
      throw error;
    }
  }

  /**
   * Actualiza el progreso de una tarea
   */
  async updateTaskProgress(taskId, progressData) {
    try {
      const task = this.tasks.get(taskId);
      if (!task) {
        throw new Error(`Task not found: ${taskId}`);
      }
      
      // Actualizar progreso
      if (progressData.progress !== undefined) {
        task.progress = Math.max(0, Math.min(100, progressData.progress));
      }
      
      // Agregar paso si se proporciona
      if (progressData.currentStep) {
        task.steps.push({
          step: progressData.currentStep,
          timestamp: new Date().toISOString(),
          data: progressData.stepData || {},
          result: progressData.stepResult || null
        });
      }
      
      // Actualizar contexto
      if (progressData.context) {
        task.context = { ...task.context, ...progressData.context };
      }
      
      task.updatedAt = new Date().toISOString();
      
      loggers.main.info('Task progress updated', { 
        taskId, 
        progress: task.progress,
        currentStep: progressData.currentStep 
      });
      
      this.emit('task:progress', task, progressData);
      
      return task;
      
    } catch (error) {
      loggers.main.error('Error updating task progress', { taskId, error: error.message });
      throw error;
    }
  }

  /**
   * Completa una tarea exitosamente
   */
  async completeTask(taskId, result) {
    try {
      const task = this.tasks.get(taskId);
      if (!task) {
        throw new Error(`Task not found: ${taskId}`);
      }
      
      task.status = 'completed';
      task.progress = 100;
      task.result = result;
      task.completedAt = new Date().toISOString();
      task.updatedAt = new Date().toISOString();
      
      // Calcular duración
      if (task.startedAt) {
        const duration = new Date(task.completedAt) - new Date(task.startedAt);
        task.metadata.actualDuration = duration;
      }
      
      // Mover a historial
      this.moveToHistory(task);
      
      loggers.main.info('Task completed', { 
        taskId, 
        duration: task.metadata.actualDuration,
        instruction: task.instruction 
      });
      
      this.emit('task:completed', task);
      
      return task;
      
    } catch (error) {
      loggers.main.error('Error completing task', { taskId, error: error.message });
      throw error;
    }
  }

  /**
   * Marca una tarea como fallida
   */
  async failTask(taskId, errorMessage) {
    try {
      const task = this.tasks.get(taskId);
      if (!task) {
        throw new Error(`Task not found: ${taskId}`);
      }
      
      task.status = 'failed';
      task.error = errorMessage;
      task.completedAt = new Date().toISOString();
      task.updatedAt = new Date().toISOString();
      
      // Calcular duración
      if (task.startedAt) {
        const duration = new Date(task.completedAt) - new Date(task.startedAt);
        task.metadata.actualDuration = duration;
      }
      
      // Mover a historial
      this.moveToHistory(task);
      
      loggers.main.error('Task failed', { 
        taskId, 
        error: errorMessage,
        instruction: task.instruction 
      });
      
      this.emit('task:failed', task, errorMessage);
      
      return task;
      
    } catch (error) {
      loggers.main.error('Error failing task', { taskId, error: error.message });
      throw error;
    }
  }

  /**
   * Cancela una tarea
   */
  async cancelTask(taskId) {
    try {
      const task = this.tasks.get(taskId);
      if (!task) {
        throw new Error(`Task not found: ${taskId}`);
      }
      
      task.status = 'cancelled';
      task.completedAt = new Date().toISOString();
      task.updatedAt = new Date().toISOString();
      
      // Mover a historial
      this.moveToHistory(task);
      
      loggers.main.info('Task cancelled', { taskId, instruction: task.instruction });
      
      this.emit('task:cancelled', task);
      
      return task;
      
    } catch (error) {
      loggers.main.error('Error cancelling task', { taskId, error: error.message });
      throw error;
    }
  }

  /**
   * Maneja timeout de tareas
   */
  async timeoutTask(taskId) {
    try {
      const task = this.tasks.get(taskId);
      if (!task || task.status !== 'running') {
        return;
      }
      
      await this.failTask(taskId, 'Task timed out');
      
      loggers.main.warn('Task timed out', { taskId, instruction: task.instruction });
      
    } catch (error) {
      loggers.main.error('Error handling task timeout', { taskId, error: error.message });
    }
  }

  /**
   * Obtiene una tarea por ID
   */
  getTask(taskId) {
    return this.tasks.get(taskId);
  }

  /**
   * Obtiene todas las tareas activas
   */
  getActiveTasks() {
    return Array.from(this.tasks.values()).filter(task => 
      ['created', 'running'].includes(task.status)
    );
  }

  /**
   * Obtiene tareas por estado
   */
  getTasksByStatus(status) {
    return Array.from(this.tasks.values()).filter(task => task.status === status);
  }

  /**
   * Obtiene el historial de tareas
   */
  getTaskHistory(limit = 50) {
    return this.taskHistory.slice(-limit);
  }

  /**
   * Mueve una tarea al historial
   */
  moveToHistory(task) {
    this.tasks.delete(task.id);
    this.taskHistory.push(task);
    
    // Mantener tamaño del historial
    if (this.taskHistory.length > this.maxHistorySize) {
      this.taskHistory = this.taskHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Obtiene estadísticas de tareas
   */
  getStatistics() {
    const activeTasks = this.getActiveTasks();
    const history = this.taskHistory;
    
    const stats = {
      active: {
        total: activeTasks.length,
        running: activeTasks.filter(t => t.status === 'running').length,
        created: activeTasks.filter(t => t.status === 'created').length
      },
      completed: history.filter(t => t.status === 'completed').length,
      failed: history.filter(t => t.status === 'failed').length,
      cancelled: history.filter(t => t.status === 'cancelled').length,
      averageDuration: this.calculateAverageDuration(history),
      successRate: this.calculateSuccessRate(history)
    };
    
    return stats;
  }

  /**
   * Calcula la duración promedio de las tareas
   */
  calculateAverageDuration(tasks) {
    const completedTasks = tasks.filter(t => 
      t.status === 'completed' && t.metadata.actualDuration
    );
    
    if (completedTasks.length === 0) return 0;
    
    const totalDuration = completedTasks.reduce((sum, task) => 
      sum + task.metadata.actualDuration, 0
    );
    
    return Math.round(totalDuration / completedTasks.length);
  }

  /**
   * Calcula la tasa de éxito
   */
  calculateSuccessRate(tasks) {
    const finishedTasks = tasks.filter(t => 
      ['completed', 'failed', 'cancelled'].includes(t.status)
    );
    
    if (finishedTasks.length === 0) return 0;
    
    const successfulTasks = finishedTasks.filter(t => t.status === 'completed');
    
    return Math.round((successfulTasks.length / finishedTasks.length) * 100);
  }

  /**
   * Limpia el historial de tareas
   */
  clearHistory() {
    this.taskHistory = [];
    loggers.main.info('Task history cleared');
  }

  /**
   * Obtiene el estado del Task Manager
   */
  getStatus() {
    return {
      activeTasks: this.tasks.size,
      historySize: this.taskHistory.length,
      statistics: this.getStatistics()
    };
  }
}
