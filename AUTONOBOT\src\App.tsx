import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import AutonobotInterface from './components/AutonobotInterface';
import AutonomousAgent from './components/AutonomousAgent';
import RemoteAutonomousAgent from './components/RemoteAutonomousAgent';

function App() {
  const [activeMode, setActiveMode] = useState<'classic' | 'autonomous' | 'remote'>('remote');

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-indigo-950 p-4 md:p-8 font-sans text-gray-100 relative overflow-hidden">
      <div className="absolute inset-0 bg-grid opacity-20"></div>

      <div className="max-w-6xl mx-auto glass-panel rounded-2xl shadow-2xl overflow-hidden relative z-10">
        <header className="bg-gradient-to-r from-[rgb(var(--neon-primary))] to-[rgb(var(--neon-secondary))] p-8 text-center">
          <div className="flex items-center justify-center mb-3 animate-float">
            <Bot size={48} className="mr-3 text-white" />
            <h1 className="text-4xl md:text-5xl font-bold neon-text">AUTONOBOT</h1>
          </div>
          <p className="text-lg md:text-xl text-blue-100 opacity-90">
            {activeMode === 'remote' ? 'Remote Desktop Multi-Agent System' :
             activeMode === 'autonomous' ? 'Advanced Multi-Agent System' :
             'Autonomous Web Navigation Agent'}
          </p>

          {/* Mode Selector */}
          <div className="mt-6 flex justify-center space-x-3">
            <button
              onClick={() => setActiveMode('remote')}
              className={`px-6 py-2 rounded-lg flex items-center space-x-2 transition-all ${
                activeMode === 'remote'
                  ? 'bg-white text-blue-600 shadow-lg'
                  : 'bg-blue-600/20 text-white hover:bg-blue-600/30'
              }`}
            >
              <Monitor size={20} />
              <span>Remote Desktop</span>
            </button>
            <button
              onClick={() => setActiveMode('autonomous')}
              className={`px-6 py-2 rounded-lg flex items-center space-x-2 transition-all ${
                activeMode === 'autonomous'
                  ? 'bg-white text-blue-600 shadow-lg'
                  : 'bg-blue-600/20 text-white hover:bg-blue-600/30'
              }`}
            >
              <Zap size={20} />
              <span>Autonomous Agents</span>
            </button>
            <button
              onClick={() => setActiveMode('classic')}
              className={`px-6 py-2 rounded-lg flex items-center space-x-2 transition-all ${
                activeMode === 'classic'
                  ? 'bg-white text-blue-600 shadow-lg'
                  : 'bg-blue-600/20 text-white hover:bg-blue-600/30'
              }`}
            >
              <Settings size={20} />
              <span>Classic Mode</span>
            </button>
          </div>
        </header>

        {/* Conditional Rendering based on mode */}
        {activeMode === 'remote' ? (
          <div className="bg-white text-gray-900">
            <RemoteAutonomousAgent />
          </div>
        ) : activeMode === 'autonomous' ? (
          <div className="bg-white text-gray-900">
            <AutonomousAgent />
          </div>
        ) : (
          <AutonobotInterface />
        )}
      </div>
    </div>
  );
}

export default App;