require('dotenv').config();
const { loggers } = require('./loggerService');

class ConfigService {
  constructor() {
    this.config = this.loadConfiguration();
    this.validateConfiguration();
    loggers.config.info('Configuration service initialized');
  }

  loadConfiguration() {
    return {
      // Server Configuration
      server: {
        port: parseInt(process.env.PORT) || 5000,
        nodeEnv: process.env.NODE_ENV || 'development',
        logLevel: process.env.LOG_LEVEL || 'info'
      },

      // API Keys
      apiKeys: {
        openai: process.env.OPENAI_API_KEY,
        gemini: process.env.GEMINI_API_KEY,
        anthropic: process.env.ANTHROPIC_API_KEY,
        googleSearch: process.env.GOOGLE_SEARCH_API_KEY,
        googleSearchEngineId: process.env.GOOGLE_SEARCH_ENGINE_ID,
        bingSearch: process.env.BING_SEARCH_API_KEY
      },

      // Search Configuration
      search: {
        maxResults: parseInt(process.env.MAX_SEARCH_RESULTS) || 10,
        timeout: parseInt(process.env.SEARCH_TIMEOUT) || 10000,
        enabledProviders: ['google', 'bing'],
        defaultProvider: 'google',
        fallbackProvider: 'bing',
        cacheResults: true,
        cacheTTL: 300000 // 5 minutes
      },

      // Voice Configuration
      voice: {
        enabled: process.env.VOICE_ENABLED === 'true',
        wakeWord: 'autonobot',
        responseDelay: 500,
        maxListeningTime: 30000,
        voiceSettings: {
          rate: 1,
          pitch: 1,
          volume: 1,
          voice: 'default'
        }
      },

      // LLM Configuration
      llm: {
        defaultModel: 'gemini',
        fallbackModel: 'anthropic',
        maxTokens: 1024,
        temperature: 0.7,
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000
      },

      // Browser Configuration
      browser: {
        headless: true,
        timeout: 30000,
        viewport: {
          width: 1920,
          height: 1080
        },
        userAgent: 'AUTONOBOT/1.0 (Autonomous Web Agent)',
        maxPages: 5,
        navigationTimeout: 30000
      },

      // Agent Configuration
      agent: {
        maxSteps: 20,
        stepTimeout: 60000,
        memoryLimit: 1000,
        enableScreenshots: true,
        enableLogging: true,
        autoSave: true
      },

      // Security Configuration
      security: {
        enableRateLimit: true,
        rateLimitWindow: 15 * 60 * 1000, // 15 minutes
        rateLimitMax: 100, // requests per window
        enableHelmet: true,
        corsOrigins: ['http://localhost:3000', 'http://localhost:5173'],
        apiKeyValidation: true
      },

      // Performance Configuration
      performance: {
        enableMetrics: true,
        metricsInterval: 60000, // 1 minute
        memoryThreshold: 512 * 1024 * 1024, // 512MB
        cpuThreshold: 80, // 80%
        enableProfiling: false
      }
    };
  }

  validateConfiguration() {
    const errors = [];

    // Validate required API keys
    if (!this.config.apiKeys.gemini && !this.config.apiKeys.anthropic && !this.config.apiKeys.openai) {
      errors.push('At least one LLM API key must be configured');
    }

    // Validate search configuration
    if (!this.config.apiKeys.googleSearch && !this.config.apiKeys.bingSearch) {
      loggers.config.warn('No search API keys configured - search functionality will be limited');
    }

    // Validate numeric values
    if (this.config.server.port < 1 || this.config.server.port > 65535) {
      errors.push('Invalid port number');
    }

    if (this.config.search.maxResults < 1 || this.config.search.maxResults > 100) {
      errors.push('Invalid max search results (must be 1-100)');
    }

    if (this.config.agent.maxSteps < 1 || this.config.agent.maxSteps > 100) {
      errors.push('Invalid max agent steps (must be 1-100)');
    }

    // Log validation results
    if (errors.length > 0) {
      errors.forEach(error => loggers.config.error(`Configuration validation error: ${error}`));
      throw new Error(`Configuration validation failed: ${errors.join(', ')}`);
    }

    loggers.config.info('Configuration validation passed');
  }

  get(path) {
    return this.getNestedValue(this.config, path);
  }

  set(path, value) {
    this.setNestedValue(this.config, path, value);
    loggers.config.info(`Configuration updated: ${path} = ${value}`);
  }

  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
  }

  setNestedValue(obj, path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {};
      return current[key];
    }, obj);
    target[lastKey] = value;
  }

  isProduction() {
    return this.config.server.nodeEnv === 'production';
  }

  isDevelopment() {
    return this.config.server.nodeEnv === 'development';
  }

  hasApiKey(service) {
    const key = this.config.apiKeys[service];
    return key && key !== `your_${service}_api_key_here` && key.length > 0;
  }

  getAvailableSearchProviders() {
    const providers = [];
    if (this.hasApiKey('googleSearch') && this.hasApiKey('googleSearchEngineId')) {
      providers.push('google');
    }
    if (this.hasApiKey('bingSearch')) {
      providers.push('bing');
    }
    return providers;
  }

  getAvailableLLMProviders() {
    const providers = [];
    if (this.hasApiKey('gemini')) providers.push('gemini');
    if (this.hasApiKey('anthropic')) providers.push('anthropic');
    if (this.hasApiKey('openai')) providers.push('openai');
    return providers;
  }

  getStatus() {
    return {
      environment: this.config.server.nodeEnv,
      logLevel: this.config.server.logLevel,
      availableSearchProviders: this.getAvailableSearchProviders(),
      availableLLMProviders: this.getAvailableLLMProviders(),
      voiceEnabled: this.config.voice.enabled,
      securityEnabled: this.config.security.enableRateLimit,
      performanceMonitoring: this.config.performance.enableMetrics
    };
  }

  reload() {
    loggers.config.info('Reloading configuration...');
    this.config = this.loadConfiguration();
    this.validateConfiguration();
    loggers.config.info('Configuration reloaded successfully');
  }
}

// Create singleton instance
const configService = new ConfigService();

module.exports = configService;
