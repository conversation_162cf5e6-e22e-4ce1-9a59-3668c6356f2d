import axios from 'axios';

interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  displayUrl: string;
  rank: number;
  relevanceScore: number;
}

interface SearchResponse {
  query: string;
  provider: string;
  timestamp: string;
  totalResults: number;
  searchTime: number;
  results: SearchResult[];
}

interface SearchOptions {
  maxResults?: number;
  provider?: string;
  useCache?: boolean;
}

interface SearchStats {
  cacheSize: number;
  availableProviders: string[];
  defaultProvider: string;
  fallbackProvider: string;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  details?: string;
  meta: {
    requestId: string;
    timestamp: string;
    processingTime?: number;
  };
}

class SearchService {
  private baseUrl: string;
  private cache: Map<string, { data: SearchResponse; timestamp: number }>;
  private cacheTTL: number = 300000; // 5 minutes

  constructor() {
    this.baseUrl = 'http://localhost:5000/api/autonobot/search';
    this.cache = new Map();
  }

  async search(query: string, options: SearchOptions = {}): Promise<SearchResponse> {
    try {
      // Validate input
      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        throw new Error('Query is required and must be a non-empty string');
      }

      if (query.length > 500) {
        throw new Error('Query is too long (maximum 500 characters)');
      }

      const trimmedQuery = query.trim();

      // Check cache first if enabled
      if (options.useCache !== false) {
        const cached = this.getFromCache(trimmedQuery);
        if (cached) {
          console.log('Returning cached search results for:', trimmedQuery);
          return cached;
        }
      }

      console.log('Performing web search:', { query: trimmedQuery, options });

      // Make API request
      const response = await axios.post<ApiResponse<SearchResponse>>(`${this.baseUrl}/search`, {
        query: trimmedQuery,
        ...options
      });

      if (!response.data.success) {
        throw new Error(response.data.error || 'Search request failed');
      }

      const searchResponse = response.data.data!;

      // Cache the results if enabled
      if (options.useCache !== false) {
        this.addToCache(trimmedQuery, searchResponse);
      }

      console.log('Search completed successfully:', {
        query: trimmedQuery,
        provider: searchResponse.provider,
        resultCount: searchResponse.results.length
      });

      return searchResponse;

    } catch (error) {
      console.error('Search failed:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 429) {
          throw new Error('Too many search requests. Please try again later.');
        } else if (error.response?.status === 503) {
          throw new Error('Search service is temporarily unavailable.');
        } else if (error.response?.status === 504) {
          throw new Error('Search request timed out. Please try again.');
        } else if (error.response?.data?.error) {
          throw new Error(error.response.data.error);
        }
      }
      
      throw error;
    }
  }

  async getProviders(): Promise<{
    available: string[];
    default: string;
    fallback: string;
    status: Array<{ name: string; enabled: boolean; configured: boolean }>;
  }> {
    try {
      const response = await axios.get<ApiResponse<any>>(`${this.baseUrl}/providers`);
      
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to get providers');
      }

      return response.data.data;
    } catch (error) {
      console.error('Failed to get search providers:', error);
      throw error;
    }
  }

  async getStats(): Promise<SearchStats> {
    try {
      const response = await axios.get<ApiResponse<SearchStats>>(`${this.baseUrl}/stats`);
      
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to get search stats');
      }

      return response.data.data!;
    } catch (error) {
      console.error('Failed to get search stats:', error);
      throw error;
    }
  }

  async clearCache(): Promise<void> {
    try {
      await axios.delete<ApiResponse<any>>(`${this.baseUrl}/cache`);
      this.cache.clear();
      console.log('Search cache cleared');
    } catch (error) {
      console.error('Failed to clear search cache:', error);
      throw error;
    }
  }

  async checkHealth(): Promise<{
    status: string;
    availableProviders: number;
    providers: string[];
    cacheSize: number;
  }> {
    try {
      const response = await axios.get<ApiResponse<any>>(`${this.baseUrl}/health`);
      return response.data.data;
    } catch (error) {
      console.error('Search health check failed:', error);
      throw error;
    }
  }

  private getFromCache(query: string): SearchResponse | null {
    const cached = this.cache.get(query.toLowerCase());
    if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
      return cached.data;
    }
    if (cached) {
      this.cache.delete(query.toLowerCase());
    }
    return null;
  }

  private addToCache(query: string, results: SearchResponse): void {
    this.cache.set(query.toLowerCase(), {
      data: results,
      timestamp: Date.now()
    });
    
    // Limit cache size
    if (this.cache.size > 50) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
  }

  public clearLocalCache(): void {
    this.cache.clear();
    console.log('Local search cache cleared');
  }

  public getLocalCacheSize(): number {
    return this.cache.size;
  }

  public setCacheTTL(ttl: number): void {
    this.cacheTTL = ttl;
    console.log('Cache TTL updated:', ttl);
  }

  // Utility method to format search results for display
  public formatResults(results: SearchResult[]): string {
    if (!results || results.length === 0) {
      return 'No search results found.';
    }

    return results.map((result, index) => {
      return `${index + 1}. **${result.title}**\n   ${result.snippet}\n   ${result.url}\n`;
    }).join('\n');
  }

  // Utility method to extract key information from search results
  public extractKeyInfo(results: SearchResult[], maxResults: number = 5): string {
    if (!results || results.length === 0) {
      return 'No information found.';
    }

    const topResults = results.slice(0, maxResults);
    const keyInfo = topResults.map(result => {
      return `From ${result.displayUrl}: ${result.snippet}`;
    }).join('\n\n');

    return keyInfo;
  }

  // Method to search and get formatted results in one call
  public async searchAndFormat(query: string, options: SearchOptions = {}): Promise<string> {
    try {
      const response = await this.search(query, options);
      return this.formatResults(response.results);
    } catch (error) {
      return `Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  // Method to search and extract key information
  public async searchAndExtract(query: string, maxResults: number = 5, options: SearchOptions = {}): Promise<string> {
    try {
      const response = await this.search(query, options);
      return this.extractKeyInfo(response.results, maxResults);
    } catch (error) {
      return `Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}

// Create singleton instance
export const searchService = new SearchService();
export type { SearchResult, SearchResponse, SearchOptions, SearchStats };
