// Simplified AUTONOBOT server for testing
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const port = process.env.PORT || 5000;

// Basic middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Basic routes
app.get('/', (req, res) => {
  res.json({
    name: 'AUTONOBOT API Server',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    message: 'Simple server mode - some features may be limited'
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    mode: 'simple'
  });
});

// Basic search endpoint (mock)
app.post('/api/autonobot/search/search', (req, res) => {
  const { query } = req.body;
  res.json({
    query,
    results: [
      {
        title: 'Mock Search Result',
        url: 'https://example.com',
        snippet: 'This is a mock search result for testing purposes.'
      }
    ],
    provider: 'mock',
    timestamp: new Date().toISOString()
  });
});

// Basic agent endpoint (mock)
app.post('/api/autonobot/start-task', (req, res) => {
  const { taskInput } = req.body;
  res.json({
    taskId: 'mock-task-' + Date.now(),
    status: 'started',
    message: 'Task started in simple mode',
    taskInput,
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Server error:', error.message);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message,
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(port, () => {
  console.log(`🤖 AUTONOBOT Simple Server listening at http://localhost:${port}`);
  console.log(`📊 Health check available at http://localhost:${port}/health`);
  console.log(`🔍 Mock search API available at http://localhost:${port}/api/autonobot/search/search`);
  console.log(`🎯 Mock agent API available at http://localhost:${port}/api/autonobot/start-task`);
  console.log(`\n✨ Frontend should be running at http://localhost:5173`);
  console.log(`\n🚀 AUTONOBOT is ready! Open http://localhost:5173 in your browser.`);
});
