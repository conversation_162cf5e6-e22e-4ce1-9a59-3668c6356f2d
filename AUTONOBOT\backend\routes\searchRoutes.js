const express = require('express');
const rateLimit = require('express-rate-limit');
const searchService = require('../services/searchService');
const configService = require('../services/configService');
const { loggers, apiLogger } = require('../services/loggerService');

const router = express.Router();

// Rate limiting for search endpoints
const searchRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // limit each IP to 50 search requests per windowMs
  message: {
    error: 'Too many search requests, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply rate limiting and API logging
router.use(searchRateLimit);
router.use(apiLogger);

// Search endpoint
router.post('/search', async (req, res) => {
  try {
    const { query, maxResults, provider, useCache } = req.body;

    // Validate input
    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      return res.status(400).json({
        error: 'Query is required and must be a non-empty string'
      });
    }

    if (query.length > 500) {
      return res.status(400).json({
        error: 'Query is too long (maximum 500 characters)'
      });
    }

    // Validate optional parameters
    const options = {};
    
    if (maxResults !== undefined) {
      if (!Number.isInteger(maxResults) || maxResults < 1 || maxResults > 50) {
        return res.status(400).json({
          error: 'maxResults must be an integer between 1 and 50'
        });
      }
      options.maxResults = maxResults;
    }

    if (provider !== undefined) {
      const availableProviders = configService.getAvailableSearchProviders();
      if (!availableProviders.includes(provider)) {
        return res.status(400).json({
          error: `Invalid provider. Available providers: ${availableProviders.join(', ')}`
        });
      }
      options.provider = provider;
    }

    if (useCache !== undefined) {
      if (typeof useCache !== 'boolean') {
        return res.status(400).json({
          error: 'useCache must be a boolean'
        });
      }
      options.useCache = useCache;
    }

    loggers.api.info('Search request received', { 
      query: query.substring(0, 100), 
      options,
      ip: req.ip 
    });

    // Perform search
    const results = await searchService.search(query.trim(), options);

    // Return results
    res.json({
      success: true,
      data: results,
      meta: {
        requestId: `search_${Date.now()}`,
        timestamp: new Date().toISOString(),
        processingTime: results.searchTime
      }
    });

  } catch (error) {
    loggers.api.error('Search request failed', { 
      error: error.message,
      query: req.body.query?.substring(0, 100),
      ip: req.ip 
    });

    // Determine appropriate error response
    let statusCode = 500;
    let errorMessage = 'Internal server error';

    if (error.message.includes('No search providers configured')) {
      statusCode = 503;
      errorMessage = 'Search service temporarily unavailable';
    } else if (error.message.includes('timeout')) {
      statusCode = 504;
      errorMessage = 'Search request timed out';
    } else if (error.message.includes('API')) {
      statusCode = 502;
      errorMessage = 'Search provider error';
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      details: configService.isDevelopment() ? error.message : undefined,
      meta: {
        requestId: `search_error_${Date.now()}`,
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Get search providers status
router.get('/providers', (req, res) => {
  try {
    const availableProviders = configService.getAvailableSearchProviders();
    const stats = searchService.getStats();

    res.json({
      success: true,
      data: {
        available: availableProviders,
        default: stats.defaultProvider,
        fallback: stats.fallbackProvider,
        status: availableProviders.map(provider => ({
          name: provider,
          enabled: true,
          configured: true
        }))
      },
      meta: {
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    loggers.api.error('Providers status request failed', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: 'Failed to get providers status',
      meta: {
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Get search statistics
router.get('/stats', (req, res) => {
  try {
    const stats = searchService.getStats();

    res.json({
      success: true,
      data: stats,
      meta: {
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    loggers.api.error('Search stats request failed', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: 'Failed to get search statistics',
      meta: {
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Clear search cache (admin endpoint)
router.delete('/cache', (req, res) => {
  try {
    searchService.clearCache();
    
    loggers.api.info('Search cache cleared', { ip: req.ip });

    res.json({
      success: true,
      message: 'Search cache cleared successfully',
      meta: {
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    loggers.api.error('Cache clear request failed', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: 'Failed to clear search cache',
      meta: {
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Health check endpoint
router.get('/health', async (req, res) => {
  try {
    const availableProviders = configService.getAvailableSearchProviders();
    const isHealthy = availableProviders.length > 0;

    res.status(isHealthy ? 200 : 503).json({
      success: isHealthy,
      status: isHealthy ? 'healthy' : 'unhealthy',
      data: {
        availableProviders: availableProviders.length,
        providers: availableProviders,
        cacheSize: searchService.getStats().cacheSize
      },
      meta: {
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    loggers.api.error('Search health check failed', { error: error.message });
    
    res.status(500).json({
      success: false,
      status: 'error',
      error: 'Health check failed',
      meta: {
        timestamp: new Date().toISOString()
      }
    });
  }
});

module.exports = router;
