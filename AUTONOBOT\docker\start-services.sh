#!/bin/bash

echo "🚀 Starting AUTONOBOT Remote Desktop Environment"
echo "================================================"

# Configurar variables de entorno
export DISPLAY=:1
export VNC_RESOLUTION=${VNC_RESOLUTION:-1920x1080}
export VNC_COL_DEPTH=${VNC_COL_DEPTH:-24}

# Crear directorios necesarios
mkdir -p /var/log/supervisor
mkdir -p /home/<USER>/.vnc
mkdir -p /home/<USER>/autonobot-workspace

# Configurar permisos
chown -R autonobot:autonobot /home/<USER>
chmod 755 /home/<USER>/autonobot-workspace

# Limpiar procesos VNC existentes
echo "🧹 Cleaning up existing VNC processes..."
pkill -f "Xtightvnc"
rm -f /tmp/.X1-lock
rm -f /tmp/.X11-unix/X1

# Configurar VNC password si no existe
if [ ! -f /home/<USER>/.vnc/passwd ]; then
    echo "🔐 Setting up VNC password..."
    su - autonobot -c "mkdir -p ~/.vnc && echo 'autonobot' | vncpasswd -f > ~/.vnc/passwd && chmod 600 ~/.vnc/passwd"
fi

# Configurar xstartup si no existe
if [ ! -f /home/<USER>/.vnc/xstartup ]; then
    echo "⚙️ Setting up VNC startup script..."
    su - autonobot -c "cat > ~/.vnc/xstartup << 'EOF'
#!/bin/bash
xrdb \$HOME/.Xresources
startxfce4 &
EOF"
    su - autonobot -c "chmod +x ~/.vnc/xstartup"
fi

# Copiar archivos de Python si no existen
if [ ! -f /home/<USER>/autonobot-workspace/selenium_server.py ]; then
    echo "📋 Setting up Python services..."
    
    # Crear servidor Selenium
    cat > /home/<USER>/autonobot-workspace/selenium_server.py << 'EOF'
#!/usr/bin/env python3
"""
AUTONOBOT Selenium Server
Manages Chrome browser in remote desktop environment
"""

import time
import json
import asyncio
import websockets
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AutonobotSeleniumServer:
    def __init__(self):
        self.driver = None
        self.is_running = False
        
    def setup_chrome_driver(self):
        """Configurar Chrome WebDriver para el entorno remoto"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--remote-debugging-port=9222')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--start-maximized')
            
            # Configurar para entorno remoto
            chrome_options.add_argument('--display=:1')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            
            logger.info("✅ Chrome WebDriver initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Chrome WebDriver: {e}")
            return False
    
    def navigate_to_url(self, url):
        """Navegar a una URL"""
        try:
            if not self.driver:
                if not self.setup_chrome_driver():
                    return False
                    
            logger.info(f"🌐 Navigating to: {url}")
            self.driver.get(url)
            
            # Esperar a que la página cargue
            WebDriverWait(self.driver, 10).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            logger.info("✅ Navigation completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Navigation failed: {e}")
            return False
    
    def take_screenshot(self, filename=None):
        """Capturar screenshot"""
        try:
            if not self.driver:
                return None
                
            if not filename:
                filename = f"/tmp/screenshot_{int(time.time())}.png"
                
            self.driver.save_screenshot(filename)
            logger.info(f"📸 Screenshot saved: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"❌ Screenshot failed: {e}")
            return None
    
    def get_page_info(self):
        """Obtener información de la página actual"""
        try:
            if not self.driver:
                return None
                
            info = {
                'url': self.driver.current_url,
                'title': self.driver.title,
                'timestamp': time.time()
            }
            
            return info
            
        except Exception as e:
            logger.error(f"❌ Failed to get page info: {e}")
            return None
    
    def start_server(self):
        """Iniciar el servidor"""
        logger.info("🚀 Starting AUTONOBOT Selenium Server")
        
        if self.setup_chrome_driver():
            # Navegar a página de inicio
            self.navigate_to_url("https://www.google.com")
            self.is_running = True
            
            # Mantener el servidor activo
            try:
                while self.is_running:
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("🛑 Server stopped by user")
            finally:
                self.cleanup()
        else:
            logger.error("❌ Failed to start server")
    
    def cleanup(self):
        """Limpiar recursos"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("🧹 Chrome driver cleaned up")
            except:
                pass

if __name__ == "__main__":
    server = AutonobotSeleniumServer()
    server.start_server()
EOF

    # Crear bridge de comunicación
    cat > /home/<USER>/autonobot-workspace/autonobot_bridge.py << 'EOF'
#!/usr/bin/env python3
"""
AUTONOBOT Bridge
Comunicación entre el sistema de agentes y el navegador remoto
"""

import asyncio
import websockets
import json
import logging
from selenium_server import AutonobotSeleniumServer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AutonobotBridge:
    def __init__(self):
        self.selenium_server = AutonobotSeleniumServer()
        self.connected_clients = set()
        
    async def handle_client(self, websocket, path):
        """Manejar conexiones de clientes WebSocket"""
        self.connected_clients.add(websocket)
        logger.info(f"🔗 Client connected: {websocket.remote_address}")
        
        try:
            async for message in websocket:
                await self.process_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"🔌 Client disconnected: {websocket.remote_address}")
        finally:
            self.connected_clients.discard(websocket)
    
    async def process_message(self, websocket, message):
        """Procesar mensajes de los clientes"""
        try:
            data = json.loads(message)
            command = data.get('command')
            
            if command == 'navigate':
                url = data.get('url')
                success = self.selenium_server.navigate_to_url(url)
                await self.send_response(websocket, {
                    'type': 'navigation_result',
                    'success': success,
                    'url': url
                })
                
            elif command == 'screenshot':
                filename = self.selenium_server.take_screenshot()
                await self.send_response(websocket, {
                    'type': 'screenshot_result',
                    'filename': filename
                })
                
            elif command == 'page_info':
                info = self.selenium_server.get_page_info()
                await self.send_response(websocket, {
                    'type': 'page_info',
                    'data': info
                })
                
        except Exception as e:
            logger.error(f"❌ Error processing message: {e}")
            await self.send_response(websocket, {
                'type': 'error',
                'message': str(e)
            })
    
    async def send_response(self, websocket, data):
        """Enviar respuesta al cliente"""
        try:
            await websocket.send(json.dumps(data))
        except Exception as e:
            logger.error(f"❌ Error sending response: {e}")
    
    async def broadcast(self, data):
        """Enviar mensaje a todos los clientes conectados"""
        if self.connected_clients:
            await asyncio.gather(
                *[self.send_response(client, data) for client in self.connected_clients],
                return_exceptions=True
            )
    
    def start_bridge(self):
        """Iniciar el bridge"""
        logger.info("🌉 Starting AUTONOBOT Bridge on port 8080")
        
        # Inicializar Selenium server
        if self.selenium_server.setup_chrome_driver():
            # Iniciar servidor WebSocket
            start_server = websockets.serve(self.handle_client, "0.0.0.0", 8080)
            
            asyncio.get_event_loop().run_until_complete(start_server)
            asyncio.get_event_loop().run_forever()
        else:
            logger.error("❌ Failed to start bridge")

if __name__ == "__main__":
    bridge = AutonobotBridge()
    bridge.start_bridge()
EOF

    # Configurar permisos
    chown -R autonobot:autonobot /home/<USER>/autonobot-workspace
    chmod +x /home/<USER>/autonobot-workspace/*.py
fi

echo "✅ Setup completed"
echo "🖥️ VNC Server will be available on port 5901"
echo "🌐 noVNC Web Interface will be available on port 6901"
echo "🔗 WebSocket Bridge will be available on port 8080"
echo ""
echo "🚀 Starting services with Supervisor..."

# Iniciar supervisor
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
