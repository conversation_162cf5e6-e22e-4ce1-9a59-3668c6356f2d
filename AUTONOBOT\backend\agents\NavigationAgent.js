import EventEmitter from 'events';
import { loggers } from '../services/loggerService.js';
import browserService from '../services/browserService.js';
import { VisionService } from '../services/visionService.js';

/**
 * Navigation Agent - Especializado en navegación web e interacción con elementos
 * Navega por sitios web, hace clic en elementos, llena formularios, maneja interacciones complejas
 */
export class NavigationAgent extends EventEmitter {
  constructor(stateManager) {
    super();
    this.stateManager = stateManager;
    this.visionService = new VisionService();
    this.isActive = false;
    this.navigationHistory = [];
    this.maxHistorySize = 100;
    this.retryAttempts = 3;
    
    loggers.main.info('Navigation Agent initialized');
  }

  /**
   * Navega a un objetivo específico basado en la tarea
   */
  async navigateToTarget(task) {
    try {
      loggers.main.info('Navigating to target for task', { taskId: task.id });
      
      this.isActive = true;
      
      // Determinar estrategia de navegación
      const strategy = await this.determineNavigationStrategy(task);
      
      // Ejecutar navegación según estrategia
      const result = await this.executeNavigationStrategy(task, strategy);
      
      // Registrar navegación
      const navigationRecord = {
        taskId: task.id,
        strategy: strategy.type,
        timestamp: new Date().toISOString(),
        success: result.success,
        finalUrl: result.finalUrl,
        steps: result.steps || []
      };
      
      this.addToHistory(navigationRecord);
      
      this.isActive = false;
      
      // Emitir evento
      this.emit('navigation:complete', {
        taskId: task.id,
        result,
        strategy
      });
      
      loggers.main.info('Navigation completed', { 
        taskId: task.id, 
        success: result.success,
        finalUrl: result.finalUrl 
      });
      
      return result;
      
    } catch (error) {
      this.isActive = false;
      loggers.main.error('Error in navigation', { 
        taskId: task.id, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Determina la estrategia de navegación basada en la tarea
   */
  async determineNavigationStrategy(task) {
    const instruction = task.instruction.toLowerCase();
    const context = await this.stateManager.getTaskContext(task.id);
    
    // Estrategias disponibles
    const strategies = {
      direct_url: {
        type: 'direct_url',
        description: 'Navigate directly to a specific URL',
        applicable: this.hasDirectUrl(instruction)
      },
      search_result: {
        type: 'search_result',
        description: 'Navigate to a search result',
        applicable: context.context?.searchResults?.length > 0
      },
      element_interaction: {
        type: 'element_interaction',
        description: 'Interact with specific elements on current page',
        applicable: this.requiresElementInteraction(instruction)
      },
      form_filling: {
        type: 'form_filling',
        description: 'Fill and submit forms',
        applicable: this.requiresFormFilling(instruction)
      },
      multi_step: {
        type: 'multi_step',
        description: 'Complex multi-step navigation',
        applicable: this.requiresMultiStep(instruction)
      }
    };
    
    // Seleccionar estrategia más apropiada
    const applicableStrategies = Object.values(strategies).filter(s => s.applicable);
    
    if (applicableStrategies.length === 0) {
      return strategies.element_interaction; // Estrategia por defecto
    }
    
    // Priorizar estrategias (más específicas primero)
    const priority = ['direct_url', 'form_filling', 'search_result', 'element_interaction', 'multi_step'];
    
    for (const strategyType of priority) {
      const strategy = applicableStrategies.find(s => s.type === strategyType);
      if (strategy) {
        loggers.main.info('Navigation strategy selected', { 
          strategy: strategy.type, 
          description: strategy.description 
        });
        return strategy;
      }
    }
    
    return strategies.element_interaction;
  }

  /**
   * Ejecuta la estrategia de navegación seleccionada
   */
  async executeNavigationStrategy(task, strategy) {
    switch (strategy.type) {
      case 'direct_url':
        return await this.executeDirectUrlNavigation(task);
      case 'search_result':
        return await this.executeSearchResultNavigation(task);
      case 'element_interaction':
        return await this.executeElementInteraction(task);
      case 'form_filling':
        return await this.executeFormFilling(task);
      case 'multi_step':
        return await this.executeMultiStepNavigation(task);
      default:
        throw new Error(`Unknown navigation strategy: ${strategy.type}`);
    }
  }

  /**
   * Navega directamente a una URL específica
   */
  async executeDirectUrlNavigation(task) {
    try {
      const url = this.extractUrlFromInstruction(task.instruction);
      
      loggers.main.info('Executing direct URL navigation', { url });
      
      // Actualizar estado
      await this.stateManager.updateGlobalState({
        browserState: 'navigating',
        lastAction: 'direct_navigation'
      });
      
      // Navegar
      const finalUrl = await browserService.navigateTo(url);
      
      // Esperar a que la página cargue
      await this.waitForPageLoad();
      
      // Actualizar estado
      await this.stateManager.updateGlobalState({
        currentUrl: finalUrl,
        browserState: 'ready'
      });
      
      return {
        success: true,
        strategy: 'direct_url',
        targetUrl: url,
        finalUrl,
        steps: [{ action: 'navigate', url: finalUrl, timestamp: new Date().toISOString() }]
      };
      
    } catch (error) {
      loggers.main.error('Direct URL navigation failed', { error: error.message });
      return {
        success: false,
        strategy: 'direct_url',
        error: error.message
      };
    }
  }

  /**
   * Navega a un resultado de búsqueda
   */
  async executeSearchResultNavigation(task) {
    try {
      const context = await this.stateManager.getTaskContext(task.id);
      const searchResults = context.context?.searchResults || [];
      
      if (searchResults.length === 0) {
        throw new Error('No search results available for navigation');
      }
      
      // Seleccionar el mejor resultado
      const bestResult = searchResults[0]; // Ya están ordenados por relevancia
      
      loggers.main.info('Executing search result navigation', { 
        url: bestResult.url,
        title: bestResult.title 
      });
      
      // Navegar al resultado
      const finalUrl = await browserService.navigateTo(bestResult.url);
      
      // Esperar a que la página cargue
      await this.waitForPageLoad();
      
      // Actualizar estado
      await this.stateManager.updateGlobalState({
        currentUrl: finalUrl,
        browserState: 'ready'
      });
      
      return {
        success: true,
        strategy: 'search_result',
        targetResult: bestResult,
        finalUrl,
        steps: [{ 
          action: 'navigate_to_search_result', 
          result: bestResult, 
          finalUrl,
          timestamp: new Date().toISOString() 
        }]
      };
      
    } catch (error) {
      loggers.main.error('Search result navigation failed', { error: error.message });
      return {
        success: false,
        strategy: 'search_result',
        error: error.message
      };
    }
  }

  /**
   * Interactúa con elementos específicos en la página
   */
  async executeElementInteraction(task) {
    try {
      loggers.main.info('Executing element interaction', { taskId: task.id });
      
      const steps = [];
      
      // Capturar screenshot para análisis
      const screenshotPath = await this.captureScreenshot(task.id);
      
      // Identificar elementos objetivo
      const targetElements = await this.identifyTargetElements(task, screenshotPath);
      
      if (targetElements.length === 0) {
        throw new Error('No target elements found for interaction');
      }
      
      // Interactuar con elementos
      for (const element of targetElements) {
        try {
          const interactionResult = await this.interactWithElement(element, task);
          steps.push(interactionResult);
          
          // Esperar un poco entre interacciones
          await this.delay(1000);
          
        } catch (error) {
          loggers.main.warn('Element interaction failed', { 
            element: element.text, 
            error: error.message 
          });
          steps.push({
            action: 'interaction_failed',
            element: element.text,
            error: error.message,
            timestamp: new Date().toISOString()
          });
        }
      }
      
      return {
        success: steps.some(step => step.success),
        strategy: 'element_interaction',
        targetElements,
        steps
      };
      
    } catch (error) {
      loggers.main.error('Element interaction failed', { error: error.message });
      return {
        success: false,
        strategy: 'element_interaction',
        error: error.message
      };
    }
  }

  /**
   * Llena y envía formularios
   */
  async executeFormFilling(task) {
    try {
      loggers.main.info('Executing form filling', { taskId: task.id });
      
      // Capturar screenshot
      const screenshotPath = await this.captureScreenshot(task.id);
      
      // Identificar formularios
      const forms = await this.identifyForms(screenshotPath);
      
      if (forms.length === 0) {
        throw new Error('No forms found on the page');
      }
      
      const steps = [];
      
      // Procesar cada formulario
      for (const form of forms) {
        const formResult = await this.fillForm(form, task);
        steps.push(formResult);
      }
      
      return {
        success: steps.some(step => step.success),
        strategy: 'form_filling',
        forms,
        steps
      };
      
    } catch (error) {
      loggers.main.error('Form filling failed', { error: error.message });
      return {
        success: false,
        strategy: 'form_filling',
        error: error.message
      };
    }
  }

  /**
   * Ejecuta navegación multi-paso compleja
   */
  async executeMultiStepNavigation(task) {
    try {
      loggers.main.info('Executing multi-step navigation', { taskId: task.id });
      
      const steps = [];
      const maxSteps = 10; // Límite de seguridad
      
      for (let i = 0; i < maxSteps; i++) {
        // Analizar estado actual
        const currentState = await this.analyzeCurrentState(task);
        
        // Determinar siguiente acción
        const nextAction = await this.determineNextAction(task, currentState, steps);
        
        if (nextAction.type === 'complete') {
          break;
        }
        
        // Ejecutar acción
        const actionResult = await this.executeAction(nextAction, task);
        steps.push(actionResult);
        
        // Verificar si se completó la tarea
        if (actionResult.taskComplete) {
          break;
        }
        
        // Esperar entre pasos
        await this.delay(2000);
      }
      
      return {
        success: steps.length > 0 && steps[steps.length - 1].success,
        strategy: 'multi_step',
        totalSteps: steps.length,
        steps
      };
      
    } catch (error) {
      loggers.main.error('Multi-step navigation failed', { error: error.message });
      return {
        success: false,
        strategy: 'multi_step',
        error: error.message
      };
    }
  }

  /**
   * Identifica elementos objetivo en la página
   */
  async identifyTargetElements(task, screenshotPath) {
    try {
      // Extraer términos objetivo de la instrucción
      const targetTerms = this.extractTargetTerms(task.instruction);
      
      const elements = [];
      
      // Buscar cada término
      for (const term of targetTerms) {
        const foundElements = await this.visionService.findElements(screenshotPath, term);
        
        if (foundElements.structured?.found) {
          elements.push(...foundElements.structured.elements);
        }
      }
      
      // Eliminar duplicados y ordenar por confianza
      const uniqueElements = this.deduplicateElements(elements);
      uniqueElements.sort((a, b) => this.getConfidenceScore(b) - this.getConfidenceScore(a));
      
      return uniqueElements.slice(0, 5); // Top 5 elementos
      
    } catch (error) {
      loggers.main.error('Error identifying target elements', { error: error.message });
      return [];
    }
  }

  /**
   * Interactúa con un elemento específico
   */
  async interactWithElement(element, task) {
    try {
      const action = this.determineElementAction(element, task);
      
      loggers.main.info('Interacting with element', { 
        element: element.text, 
        action 
      });
      
      let success = false;
      
      switch (action) {
        case 'click':
          success = await this.clickElement(element);
          break;
        case 'type':
          const textToType = this.extractTextToType(task.instruction);
          success = await this.typeInElement(element, textToType);
          break;
        case 'hover':
          success = await this.hoverElement(element);
          break;
        default:
          success = await this.clickElement(element); // Default to click
      }
      
      return {
        action,
        element: element.text,
        success,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      return {
        action: 'interaction_failed',
        element: element.text,
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Métodos auxiliares
  hasDirectUrl(instruction) {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return urlRegex.test(instruction);
  }

  requiresElementInteraction(instruction) {
    const interactionKeywords = ['click', 'press', 'select', 'choose', 'tap'];
    return interactionKeywords.some(keyword => instruction.includes(keyword));
  }

  requiresFormFilling(instruction) {
    const formKeywords = ['fill', 'enter', 'type', 'submit', 'form', 'input'];
    return formKeywords.some(keyword => instruction.includes(keyword));
  }

  requiresMultiStep(instruction) {
    const multiStepKeywords = ['then', 'after', 'next', 'multiple', 'several'];
    return multiStepKeywords.some(keyword => instruction.includes(keyword));
  }

  extractUrlFromInstruction(instruction) {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const matches = instruction.match(urlRegex);
    return matches ? matches[0] : null;
  }

  extractTargetTerms(instruction) {
    // Extraer términos que podrían ser elementos objetivo
    const words = instruction.toLowerCase().split(' ');
    const stopWords = ['click', 'on', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'at'];
    
    return words.filter(word => 
      word.length > 2 && !stopWords.includes(word)
    ).slice(0, 5);
  }

  deduplicateElements(elements) {
    const seen = new Set();
    return elements.filter(element => {
      const key = `${element.type}_${element.text}`.toLowerCase();
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  getConfidenceScore(element) {
    if (element.confidence === 'high') return 0.9;
    if (element.confidence === 'medium') return 0.6;
    if (element.confidence === 'low') return 0.3;
    return 0.5;
  }

  determineElementAction(element, task) {
    const instruction = task.instruction.toLowerCase();
    
    if (instruction.includes('type') || instruction.includes('enter')) return 'type';
    if (instruction.includes('hover')) return 'hover';
    return 'click'; // Default
  }

  extractTextToType(instruction) {
    // Extraer texto entre comillas o después de "type"
    const quotedText = instruction.match(/"([^"]+)"/);
    if (quotedText) return quotedText[1];
    
    const typeMatch = instruction.match(/type\s+(.+)/i);
    if (typeMatch) return typeMatch[1].trim();
    
    return '';
  }

  async clickElement(element) {
    try {
      // Intentar diferentes selectores
      const selectors = [
        element.suggestedSelector,
        `text="${element.text}"`,
        `[aria-label="${element.text}"]`,
        `[title="${element.text}"]`
      ].filter(Boolean);
      
      for (const selector of selectors) {
        try {
          await browserService.clickElement(selector);
          return true;
        } catch (error) {
          continue;
        }
      }
      
      return false;
    } catch (error) {
      return false;
    }
  }

  async typeInElement(element, text) {
    try {
      const selector = element.suggestedSelector || `input[placeholder*="${element.text}"]`;
      await browserService.typeText(selector, text);
      return true;
    } catch (error) {
      return false;
    }
  }

  async hoverElement(element) {
    // Implementar hover si es necesario
    return true;
  }

  async waitForPageLoad(timeout = 10000) {
    await this.delay(2000); // Espera básica
  }

  async captureScreenshot(taskId) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `nav_${taskId}_${timestamp}.png`;
    const screenshotPath = `screenshots/${filename}`;
    
    await browserService.takeScreenshot(screenshotPath);
    return screenshotPath;
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  addToHistory(record) {
    this.navigationHistory.push(record);
    if (this.navigationHistory.length > this.maxHistorySize) {
      this.navigationHistory = this.navigationHistory.slice(-this.maxHistorySize);
    }
  }

  getStatus() {
    return {
      isActive: this.isActive,
      navigationCount: this.navigationHistory.length,
      lastNavigation: this.navigationHistory[this.navigationHistory.length - 1]?.timestamp || null
    };
  }

  async stop() {
    this.isActive = false;
    loggers.main.info('Navigation Agent stopped');
  }
}
