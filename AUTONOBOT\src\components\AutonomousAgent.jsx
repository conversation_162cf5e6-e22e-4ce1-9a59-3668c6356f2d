import React, { useState, useEffect, useRef } from 'react';
import { Send, Square, Activity, Eye, Search, Navigation, MessageSquare, Settings } from 'lucide-react';

const AutonomousAgent = () => {
  const [instruction, setInstruction] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [systemStatus, setSystemStatus] = useState(null);
  const [taskHistory, setTaskHistory] = useState([]);
  const [currentResponse, setCurrentResponse] = useState(null);
  const [agentLogs, setAgentLogs] = useState([]);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [wsConnection, setWsConnection] = useState(null);
  const logsEndRef = useRef(null);

  // Configuración avanzada
  const [advancedOptions, setAdvancedOptions] = useState({
    analysisType: 'interactive',
    includeContent: true,
    maxRetries: 3,
    timeout: 300000
  });

  useEffect(() => {
    // Obtener estado inicial del sistema
    fetchSystemStatus();
    fetchTaskHistory();
    
    // Configurar WebSocket para actualizaciones en tiempo real
    setupWebSocket();
    
    return () => {
      if (wsConnection) {
        wsConnection.close();
      }
    };
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [agentLogs]);

  const setupWebSocket = () => {
    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws`;
      
      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        console.log('WebSocket connected');
        addLog('🔗 Connected to real-time updates', 'system');
      };
      
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
      
      ws.onclose = () => {
        console.log('WebSocket disconnected');
        addLog('🔌 Disconnected from real-time updates', 'system');
      };
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        addLog('❌ WebSocket connection error', 'error');
      };
      
      setWsConnection(ws);
    } catch (error) {
      console.error('Failed to setup WebSocket:', error);
    }
  };

  const handleWebSocketMessage = (data) => {
    switch (data.type) {
      case 'task:started':
        addLog(`🚀 Task started: ${data.data.task.instruction}`, 'info');
        break;
      case 'task:step_completed':
        addLog(`✅ Step completed: ${data.data.step}`, 'success');
        break;
      case 'task:completed':
        addLog(`🎉 Task completed successfully`, 'success');
        setIsProcessing(false);
        fetchTaskHistory();
        break;
      case 'observation:complete':
        addLog(`👁️ Page observation completed`, 'info');
        break;
      case 'search:complete':
        addLog(`🔍 Search completed: ${data.data.results.length} results found`, 'info');
        break;
      case 'navigation:complete':
        addLog(`🧭 Navigation completed to: ${data.data.result.finalUrl}`, 'info');
        break;
      case 'response:generated':
        setCurrentResponse(data.data.response);
        addLog(`💬 Response generated`, 'info');
        break;
      default:
        console.log('Unknown WebSocket message:', data);
    }
  };

  const fetchSystemStatus = async () => {
    try {
      const response = await fetch('/api/autonomous/status');
      const data = await response.json();
      if (data.success) {
        setSystemStatus(data.status);
      }
    } catch (error) {
      console.error('Error fetching system status:', error);
      addLog('❌ Failed to fetch system status', 'error');
    }
  };

  const fetchTaskHistory = async () => {
    try {
      const response = await fetch('/api/autonomous/history?limit=10');
      const data = await response.json();
      if (data.success) {
        setTaskHistory(data.history);
      }
    } catch (error) {
      console.error('Error fetching task history:', error);
    }
  };

  const processInstruction = async () => {
    if (!instruction.trim() || isProcessing) return;

    setIsProcessing(true);
    setCurrentResponse(null);
    addLog(`🎯 Processing: "${instruction}"`, 'user');

    try {
      const requestBody = {
        instruction: instruction.trim(),
        options: showAdvanced ? advancedOptions : {}
      };

      const response = await fetch('/api/autonomous/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();

      if (data.success) {
        addLog(`✅ Task initiated successfully`, 'success');
        setInstruction('');
        fetchSystemStatus();
      } else {
        addLog(`❌ Error: ${data.error}`, 'error');
        setIsProcessing(false);
      }
    } catch (error) {
      console.error('Error processing instruction:', error);
      addLog(`❌ Network error: ${error.message}`, 'error');
      setIsProcessing(false);
    }
  };

  const stopExecution = async () => {
    try {
      const response = await fetch('/api/autonomous/stop', {
        method: 'POST'
      });

      const data = await response.json();

      if (data.success) {
        addLog('🛑 Execution stopped', 'info');
        setIsProcessing(false);
        fetchSystemStatus();
      } else {
        addLog(`❌ Error stopping execution: ${data.error}`, 'error');
      }
    } catch (error) {
      console.error('Error stopping execution:', error);
      addLog(`❌ Error stopping execution: ${error.message}`, 'error');
    }
  };

  const addLog = (message, type = 'info') => {
    const logEntry = {
      id: Date.now() + Math.random(),
      message,
      type,
      timestamp: new Date().toLocaleTimeString()
    };
    setAgentLogs(prev => [...prev.slice(-49), logEntry]); // Keep last 50 logs
  };

  const scrollToBottom = () => {
    logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getAgentStatusIcon = (agentName) => {
    const isActive = systemStatus?.agentStatus?.[agentName]?.isActive;
    return isActive ? '🟢' : '⚪';
  };

  const getLogIcon = (type) => {
    switch (type) {
      case 'user': return '👤';
      case 'success': return '✅';
      case 'error': return '❌';
      case 'system': return '🔧';
      default: return 'ℹ️';
    }
  };

  const getLogColor = (type) => {
    switch (type) {
      case 'user': return 'text-blue-600';
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'system': return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg">
        <h1 className="text-3xl font-bold mb-2">🤖 AUTONOBOT Autonomous Agent</h1>
        <p className="text-blue-100">
          Advanced multi-agent system with visual analysis, web navigation, and intelligent automation
        </p>
      </div>

      {/* System Status */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Activity className="mr-2" size={20} />
          System Status
        </h2>
        
        {systemStatus ? (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded">
              <div className="text-2xl mb-1">{getAgentStatusIcon('observer')}</div>
              <div className="text-sm font-medium">Observer Agent</div>
              <div className="text-xs text-gray-500">Visual Analysis</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded">
              <div className="text-2xl mb-1">{getAgentStatusIcon('search')}</div>
              <div className="text-sm font-medium">Search Agent</div>
              <div className="text-xs text-gray-500">Web Search</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded">
              <div className="text-2xl mb-1">{getAgentStatusIcon('navigation')}</div>
              <div className="text-sm font-medium">Navigation Agent</div>
              <div className="text-xs text-gray-500">Web Navigation</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded">
              <div className="text-2xl mb-1">{getAgentStatusIcon('response')}</div>
              <div className="text-sm font-medium">Response Agent</div>
              <div className="text-xs text-gray-500">AI Responses</div>
            </div>
          </div>
        ) : (
          <div className="text-center py-4 text-gray-500">Loading system status...</div>
        )}
      </div>

      {/* Instruction Input */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Give Instructions</h2>
        
        <div className="space-y-4">
          <div className="flex space-x-2">
            <input
              type="text"
              value={instruction}
              onChange={(e) => setInstruction(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && processInstruction()}
              placeholder="Enter your instruction (e.g., 'Search for React tutorials and summarize the best practices')"
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isProcessing}
            />
            <button
              onClick={processInstruction}
              disabled={!instruction.trim() || isProcessing}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isProcessing ? <Square size={20} /> : <Send size={20} />}
              <span className="ml-2">{isProcessing ? 'Stop' : 'Process'}</span>
            </button>
          </div>

          {/* Advanced Options Toggle */}
          <div className="flex items-center justify-between">
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
            >
              <Settings size={16} className="mr-1" />
              Advanced Options
            </button>
            
            {isProcessing && (
              <button
                onClick={stopExecution}
                className="px-4 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                Stop Execution
              </button>
            )}
          </div>

          {/* Advanced Options Panel */}
          {showAdvanced && (
            <div className="bg-gray-50 p-4 rounded-lg space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Analysis Type
                  </label>
                  <select
                    value={advancedOptions.analysisType}
                    onChange={(e) => setAdvancedOptions(prev => ({
                      ...prev,
                      analysisType: e.target.value
                    }))}
                    className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
                  >
                    <option value="interactive">Interactive Elements</option>
                    <option value="content">Content Analysis</option>
                    <option value="navigation">Navigation Focus</option>
                    <option value="general">General Analysis</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Max Retries
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="5"
                    value={advancedOptions.maxRetries}
                    onChange={(e) => setAdvancedOptions(prev => ({
                      ...prev,
                      maxRetries: parseInt(e.target.value)
                    }))}
                    className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
                  />
                </div>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="includeContent"
                  checked={advancedOptions.includeContent}
                  onChange={(e) => setAdvancedOptions(prev => ({
                    ...prev,
                    includeContent: e.target.checked
                  }))}
                  className="mr-2"
                />
                <label htmlFor="includeContent" className="text-sm text-gray-700">
                  Include detailed content analysis
                </label>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Current Response */}
      {currentResponse && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <MessageSquare className="mr-2" size={20} />
            Agent Response
          </h2>
          
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-blue-800">
                {currentResponse.type === 'final' ? 'Final Result' : 'Progress Update'}
              </span>
              {currentResponse.success !== undefined && (
                <span className={`text-sm px-2 py-1 rounded ${
                  currentResponse.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {currentResponse.success ? 'Success' : 'Failed'}
                </span>
              )}
            </div>
            
            <p className="text-gray-800 whitespace-pre-wrap">{currentResponse.message}</p>
            
            {currentResponse.summary && (
              <div className="mt-3 pt-3 border-t border-blue-200">
                <div className="text-sm text-blue-700">
                  <strong>Summary:</strong> {currentResponse.summary.stepsExecuted} steps executed, 
                  {currentResponse.summary.successRate}% success rate
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Agent Logs */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Agent Activity Log</h2>
        
        <div className="bg-gray-900 text-green-400 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm">
          {agentLogs.length === 0 ? (
            <div className="text-gray-500">Waiting for agent activity...</div>
          ) : (
            agentLogs.map((log) => (
              <div key={log.id} className={`mb-1 ${getLogColor(log.type)}`}>
                <span className="text-gray-400">[{log.timestamp}]</span>
                <span className="ml-2">{getLogIcon(log.type)}</span>
                <span className="ml-2">{log.message}</span>
              </div>
            ))
          )}
          <div ref={logsEndRef} />
        </div>
      </div>

      {/* Task History */}
      {taskHistory.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Recent Tasks</h2>
          
          <div className="space-y-2">
            {taskHistory.slice(0, 5).map((task, index) => (
              <div key={task.id || index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div className="flex-1">
                  <div className="font-medium text-gray-800">{task.instruction}</div>
                  <div className="text-sm text-gray-500">
                    {new Date(task.completedAt || task.createdAt).toLocaleString()}
                  </div>
                </div>
                <div className={`px-2 py-1 rounded text-xs ${
                  task.status === 'completed' ? 'bg-green-100 text-green-800' :
                  task.status === 'failed' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {task.status}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AutonomousAgent;
