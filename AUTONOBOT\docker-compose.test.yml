version: '3.8'

services:
  # Escritorio remoto VNC usando imagen precompilada
  autonobot-desktop-test:
    image: consol/ubuntu-xfce-vnc:latest
    container_name: autonobot-desktop-test
    ports:
      - "5901:5901"   # VNC Server directo
      - "6901:6901"   # noVNC Web Interface
    environment:
      - VNC_RESOLUTION=1920x1080
      - VNC_COL_DEPTH=24
      - VNC_PW=autonobot
      - VNC_VIEW_ONLY=false
      - DISPLAY=:1
    volumes:
      - ./screenshots:/headless/screenshots:rw
      - ./downloads:/headless/Downloads:rw
    shm_size: 2gb
    restart: unless-stopped
    networks:
      - autonobot-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6901/vnc.html"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

networks:
  autonobot-test-network:
    driver: bridge

volumes:
  screenshots:
  downloads:
