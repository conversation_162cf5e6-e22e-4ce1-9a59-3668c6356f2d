import React from 'react';
import { Bo<PERSON> } from 'lucide-react';
import AutonobotInterface from './components/AutonobotInterface';

function App() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-indigo-950 p-4 md:p-8 font-sans text-gray-100 relative overflow-hidden">
      <div className="absolute inset-0 bg-grid opacity-20"></div>
      
      <div className="max-w-6xl mx-auto glass-panel rounded-2xl shadow-2xl overflow-hidden relative z-10">
        <header className="bg-gradient-to-r from-[rgb(var(--neon-primary))] to-[rgb(var(--neon-secondary))] p-8 text-center">
          <div className="flex items-center justify-center mb-3 animate-float">
            <Bot size={48} className="mr-3 text-white" />
            <h1 className="text-4xl md:text-5xl font-bold neon-text">AUTONOBOT</h1>
          </div>
          <p className="text-lg md:text-xl text-blue-100 opacity-90">
            Autonomous Web Navigation Agent
          </p>
        </header>
        
        <AutonobotInterface />
      </div>
    </div>
  );
}

export default App;