#!/usr/bin/env node

/**
 * AUTONOBOT Remote Desktop System Launcher
 * Inicia el sistema completo con escritorio remoto
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🖥️ AUTONOBOT Remote Desktop System Launcher');
console.log('==========================================\n');

// Verificar que Docker esté disponible
async function checkDockerAvailability() {
  return new Promise((resolve) => {
    const docker = spawn('docker', ['--version'], { shell: true });
    
    docker.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Docker is available');
        resolve(true);
      } else {
        console.log('❌ Docker is not available or not running');
        console.log('   Please install Docker and make sure it\'s running');
        resolve(false);
      }
    });
    
    docker.on('error', () => {
      console.log('❌ Docker command not found');
      resolve(false);
    });
  });
}

// Verificar que docker-compose esté disponible
async function checkDockerComposeAvailability() {
  return new Promise((resolve) => {
    const dockerCompose = spawn('docker-compose', ['--version'], { shell: true });
    
    dockerCompose.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Docker Compose is available');
        resolve(true);
      } else {
        console.log('❌ Docker Compose is not available');
        resolve(false);
      }
    });
    
    dockerCompose.on('error', () => {
      console.log('❌ Docker Compose command not found');
      resolve(false);
    });
  });
}

// Crear directorios necesarios
function createDirectories() {
  const dirs = ['screenshots', 'logs', 'docker'];
  
  dirs.forEach(dir => {
    const dirPath = join(__dirname, dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    }
  });
}

// Construir imágenes Docker
async function buildDockerImages() {
  console.log('🔨 Building Docker images...');
  
  return new Promise((resolve, reject) => {
    const build = spawn('docker-compose', ['build'], {
      cwd: __dirname,
      stdio: 'inherit',
      shell: true
    });
    
    build.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Docker images built successfully');
        resolve(true);
      } else {
        console.log('❌ Failed to build Docker images');
        reject(new Error('Docker build failed'));
      }
    });
  });
}

// Iniciar servicios con Docker Compose
async function startDockerServices() {
  console.log('🚀 Starting Docker services...');
  
  return new Promise((resolve, reject) => {
    const up = spawn('docker-compose', ['up', '-d'], {
      cwd: __dirname,
      stdio: 'inherit',
      shell: true
    });
    
    up.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Docker services started successfully');
        resolve(true);
      } else {
        console.log('❌ Failed to start Docker services');
        reject(new Error('Docker services failed to start'));
      }
    });
  });
}

// Verificar estado de los servicios
async function checkServicesHealth() {
  console.log('🏥 Checking services health...');
  
  const services = [
    { name: 'Remote Desktop', url: 'http://localhost:6901', timeout: 30000 },
    { name: 'Backend API', url: 'http://localhost:5000/health', timeout: 15000 },
    { name: 'WebSocket Bridge', url: 'ws://localhost:8080', timeout: 15000 }
  ];
  
  for (const service of services) {
    console.log(`   Checking ${service.name}...`);
    
    try {
      if (service.url.startsWith('ws://')) {
        // Para WebSocket, solo reportar como disponible
        console.log(`   ✅ ${service.name} should be available`);
      } else {
        // Para HTTP, intentar hacer una petición
        const response = await fetch(service.url);
        if (response.ok) {
          console.log(`   ✅ ${service.name} is healthy`);
        } else {
          console.log(`   ⚠️ ${service.name} responded with status ${response.status}`);
        }
      }
    } catch (error) {
      console.log(`   ⚠️ ${service.name} is not yet available (this is normal during startup)`);
    }
  }
}

// Mostrar logs de los servicios
function showServiceLogs() {
  console.log('\n📋 Service logs (press Ctrl+C to stop):');
  
  const logs = spawn('docker-compose', ['logs', '-f'], {
    cwd: __dirname,
    stdio: 'inherit',
    shell: true
  });
  
  return logs;
}

// Función principal
async function main() {
  try {
    // Verificar prerrequisitos
    const dockerAvailable = await checkDockerAvailability();
    if (!dockerAvailable) {
      console.log('\n💡 To install Docker:');
      console.log('   - Windows/Mac: Download Docker Desktop from https://docker.com');
      console.log('   - Linux: Follow instructions at https://docs.docker.com/engine/install/');
      process.exit(1);
    }
    
    const dockerComposeAvailable = await checkDockerComposeAvailability();
    if (!dockerComposeAvailable) {
      console.log('\n💡 Docker Compose is usually included with Docker Desktop');
      console.log('   If not available, install it from https://docs.docker.com/compose/install/');
      process.exit(1);
    }
    
    // Crear directorios
    createDirectories();
    
    // Verificar si docker-compose.yml existe
    const dockerComposeFile = join(__dirname, 'docker-compose.yml');
    if (!fs.existsSync(dockerComposeFile)) {
      console.log('❌ docker-compose.yml not found');
      console.log('   Please make sure you have the complete AUTONOBOT remote desktop setup');
      process.exit(1);
    }
    
    // Construir imágenes
    await buildDockerImages();
    
    // Iniciar servicios
    await startDockerServices();
    
    // Esperar un poco para que los servicios se inicialicen
    console.log('\n⏳ Waiting for services to initialize...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Verificar salud de los servicios
    await checkServicesHealth();
    
    // Mostrar información de acceso
    console.log('\n🎉 AUTONOBOT Remote Desktop System is ready!');
    console.log('================================================');
    console.log('🌐 Frontend:           http://localhost:3000');
    console.log('🔧 Backend API:        http://localhost:5000');
    console.log('🖥️ Remote Desktop:     http://localhost:6901');
    console.log('🔗 WebSocket Bridge:   ws://localhost:8080');
    console.log('');
    console.log('💡 Instructions:');
    console.log('   1. Open http://localhost:3000 in your browser');
    console.log('   2. Click "Remote Desktop" mode');
    console.log('   3. Watch the remote browser in real-time');
    console.log('   4. Give instructions to the AI agents');
    console.log('');
    console.log('🖥️ Direct VNC Access:');
    console.log('   • Web: http://localhost:6901');
    console.log('   • VNC Client: localhost:5901 (password: autonobot)');
    console.log('');
    console.log('🛑 To stop the system: docker-compose down');
    console.log('📋 To view logs: docker-compose logs -f');
    console.log('');
    
    // Preguntar si mostrar logs
    console.log('Would you like to view service logs? (y/n)');
    
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on('data', (key) => {
      const input = key.toString().toLowerCase();
      
      if (input === 'y' || input === '\r' || input === '\n') {
        console.log('\n📋 Showing service logs...\n');
        const logsProcess = showServiceLogs();
        
        // Manejar Ctrl+C para detener logs
        process.on('SIGINT', () => {
          console.log('\n\n🛑 Stopping log display...');
          logsProcess.kill();
          process.exit(0);
        });
        
      } else if (input === 'n') {
        console.log('\n✅ System is running in the background');
        console.log('   Use "docker-compose logs -f" to view logs later');
        process.exit(0);
      } else if (input === '\u0003') { // Ctrl+C
        process.exit(0);
      }
    });
    
  } catch (error) {
    console.error('\n💥 Error starting remote desktop system:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Make sure Docker is running');
    console.log('   2. Check if ports 5000, 5901, 6901, 8080 are available');
    console.log('   3. Try: docker-compose down && docker-compose up --build');
    process.exit(1);
  }
}

// Manejar señales de terminación
process.on('SIGINT', () => {
  console.log('\n\n🛑 Shutting down...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n\n🛑 Received SIGTERM, shutting down...');
  process.exit(0);
});

// Ejecutar función principal
main();
