const { GoogleGenerativeAI } = require('@google/generative-ai');
const Anthropic = require('@anthropic-ai/sdk');
const OpenAI = require('openai');
const axios = require('axios');
const { loggers, performance, errorTracker } = require('./loggerService');
const configService = require('./configService');

// Initialize APIs
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Get the generative model
const getModel = () => {
  return genAI.getGenerativeModel({ model: "gemini-2.0-flash" });
};

/**
 * Enhanced LLM service with multiple providers and robust error handling
 */
class LLMService {
  constructor() {
    this.providers = ['gemini', 'anthropic', 'openai'];
    this.currentProvider = configService.get('llm.defaultModel');
    this.retryAttempts = configService.get('llm.retryAttempts');
    this.retryDelay = configService.get('llm.retryDelay');
    loggers.llm.info('LLM Service initialized', {
      currentProvider: this.currentProvider,
      availableProviders: this.getAvailableProviders()
    });
  }

  getAvailableProviders() {
    return configService.getAvailableLLMProviders();
  }

  async callLLM(prompt, schema = null, options = {}) {
    const callId = `llm_${Date.now()}`;
    performance.start(callId);

    try {
      const {
        provider = this.currentProvider,
        maxTokens = configService.get('llm.maxTokens'),
        temperature = configService.get('llm.temperature'),
        timeout = configService.get('llm.timeout')
      } = options;

      loggers.llm.info('LLM call started', {
        provider,
        promptLength: prompt.length,
        hasSchema: !!schema
      });

      // Try the specified provider first
      let result = await this.tryProvider(provider, prompt, schema, { maxTokens, temperature, timeout });

      if (result) {
        const duration = performance.end(callId);
        loggers.llm.info('LLM call successful', { provider, duration });
        return result;
      }

      // Try fallback providers
      const availableProviders = this.getAvailableProviders();
      for (const fallbackProvider of availableProviders) {
        if (fallbackProvider !== provider) {
          loggers.llm.warn(`Trying fallback provider: ${fallbackProvider}`);
          result = await this.tryProvider(fallbackProvider, prompt, schema, { maxTokens, temperature, timeout });
          if (result) {
            const duration = performance.end(callId);
            loggers.llm.info('LLM fallback successful', { provider: fallbackProvider, duration });
            return result;
          }
        }
      }

      throw new Error('All LLM providers failed');

    } catch (error) {
      performance.end(callId);
      loggers.llm.error('LLM call failed', { error: error.message });
      errorTracker.track(error, 'LLM Service');
      throw error;
    }
  }

  async tryProvider(provider, prompt, schema, options) {
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        switch (provider) {
          case 'gemini':
            return await this.callGemini(prompt, schema, options);
          case 'anthropic':
            return await this.callAnthropic(prompt, schema, options);
          case 'openai':
            return await this.callOpenAI(prompt, schema, options);
          default:
            throw new Error(`Unknown provider: ${provider}`);
        }
      } catch (error) {
        loggers.llm.warn(`Provider ${provider} attempt ${attempt} failed`, { error: error.message });

        if (attempt < this.retryAttempts) {
          await this.delay(this.retryDelay * attempt);
        } else {
          errorTracker.track(error, `LLM Provider: ${provider}`);
        }
      }
    }
    return null;
  }

  async callGemini(prompt, schema, options) {
    if (!configService.hasApiKey('gemini')) {
      throw new Error('Gemini API key not configured');
    }

    const model = getModel();
    const chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
    let requestBody = { contents: chatHistory };

    if (schema) {
      requestBody.generationConfig = {
        responseMimeType: "application/json",
        responseSchema: schema,
        maxOutputTokens: options.maxTokens,
        temperature: options.temperature
      };
    } else {
      requestBody.generationConfig = {
        maxOutputTokens: options.maxTokens,
        temperature: options.temperature
      };
    }

    const result = await Promise.race([
      model.generateContent(requestBody),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Gemini timeout')), options.timeout)
      )
    ]);

    const response = result.response;
    const text = response.text();

    if (schema) {
      try {
        return JSON.parse(text);
      } catch (e) {
        throw new Error(`Invalid JSON response from Gemini: ${e.message}`);
      }
    }
    return text;
  }

  async callAnthropic(prompt, schema, options) {
    if (!configService.hasApiKey('anthropic')) {
      throw new Error('Anthropic API key not configured');
    }

    const systemMessage = schema
      ? `You must respond with valid JSON that matches this schema: ${JSON.stringify(schema)}`
      : undefined;

    const message = await Promise.race([
      anthropic.messages.create({
        model: 'claude-3-opus-20240229',
        max_tokens: options.maxTokens,
        temperature: options.temperature,
        system: systemMessage,
        messages: [{ role: 'user', content: prompt }],
      }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Anthropic timeout')), options.timeout)
      )
    ]);

    const text = message.content[0].text;

    if (schema) {
      try {
        return JSON.parse(text);
      } catch (e) {
        throw new Error(`Invalid JSON response from Anthropic: ${e.message}`);
      }
    }
    return text;
  }

  async callOpenAI(prompt, schema, options) {
    if (!configService.hasApiKey('openai')) {
      throw new Error('OpenAI API key not configured');
    }

    const messages = [{ role: 'user', content: prompt }];

    if (schema) {
      messages.unshift({
        role: 'system',
        content: `You must respond with valid JSON that matches this schema: ${JSON.stringify(schema)}`
      });
    }

    const completion = await Promise.race([
      openai.chat.completions.create({
        model: 'gpt-4',
        messages,
        max_tokens: options.maxTokens,
        temperature: options.temperature,
      }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('OpenAI timeout')), options.timeout)
      )
    ]);

    const text = completion.choices[0].message.content;

    if (schema) {
      try {
        return JSON.parse(text);
      } catch (e) {
        throw new Error(`Invalid JSON response from OpenAI: ${e.message}`);
      }
    }
    return text;
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getStats() {
    return {
      currentProvider: this.currentProvider,
      availableProviders: this.getAvailableProviders(),
      retryAttempts: this.retryAttempts,
      retryDelay: this.retryDelay
    };
  }
}

// Create singleton instance
const llmService = new LLMService();

// Legacy function for backward compatibility
async function callLLM(prompt, schema = null) {
  return llmService.callLLM(prompt, schema);
}

// Legacy function for backward compatibility
async function callAnthropicLLM(prompt, schema = null) {
  return llmService.callAnthropic(prompt, schema, {
    maxTokens: configService.get('llm.maxTokens'),
    temperature: configService.get('llm.temperature'),
    timeout: configService.get('llm.timeout')
  });
}

// Simulation function for fallback (keep existing implementation)
async function simulateLLM(prompt, schema) {
  loggers.llm.warn('Using simulated LLM response');

  if (schema) {
    // Return a basic structure based on schema
    const mockResponse = {};
    if (schema.properties) {
      Object.keys(schema.properties).forEach(key => {
        const prop = schema.properties[key];
        if (prop.type === 'string') {
          mockResponse[key] = 'simulated response';
        } else if (prop.type === 'array') {
          mockResponse[key] = [];
        } else if (prop.type === 'object') {
          mockResponse[key] = {};
        }
      });
    }
    return mockResponse;
  }

  return 'This is a simulated response. Please configure your API keys for real LLM functionality.';
}

module.exports = {
  llmService,
  callLLM,
  callAnthropicLLM,
  simulateLLM
};
