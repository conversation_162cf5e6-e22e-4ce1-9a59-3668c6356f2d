const axios = require('axios');
const { loggers, performance, errorTracker } = require('./loggerService');
const configService = require('./configService');

class SearchService {
  constructor() {
    this.cache = new Map();
    this.providers = {
      google: this.googleSearch.bind(this),
      bing: this.bingSearch.bind(this)
    };
    loggers.search.info('Search service initialized');
  }

  async search(query, options = {}) {
    const searchId = `search_${Date.now()}`;
    performance.start(searchId);

    try {
      const {
        maxResults = configService.get('search.maxResults'),
        provider = configService.get('search.defaultProvider'),
        useCache = configService.get('search.cacheResults')
      } = options;

      loggers.search.info(`Starting search: "${query}"`, { provider, maxResults });

      // Check cache first
      if (useCache) {
        const cached = this.getFromCache(query);
        if (cached) {
          loggers.search.info('Returning cached results', { query });
          performance.end(searchId);
          return cached;
        }
      }

      // Get available providers
      const availableProviders = configService.getAvailableSearchProviders();
      if (availableProviders.length === 0) {
        throw new Error('No search providers configured');
      }

      // Try primary provider
      let results = null;
      let usedProvider = provider;

      if (availableProviders.includes(provider)) {
        try {
          results = await this.executeSearch(provider, query, maxResults);
        } catch (error) {
          loggers.search.warn(`Primary provider ${provider} failed`, { error: error.message });
          errorTracker.track(error, `Search provider: ${provider}`);
        }
      }

      // Try fallback provider if primary failed
      if (!results) {
        const fallbackProvider = configService.get('search.fallbackProvider');
        if (availableProviders.includes(fallbackProvider) && fallbackProvider !== provider) {
          try {
            results = await this.executeSearch(fallbackProvider, query, maxResults);
            usedProvider = fallbackProvider;
            loggers.search.info(`Fallback to ${fallbackProvider} successful`);
          } catch (error) {
            loggers.search.error(`Fallback provider ${fallbackProvider} failed`, { error: error.message });
            errorTracker.track(error, `Search fallback provider: ${fallbackProvider}`);
          }
        }
      }

      // Try any remaining providers
      if (!results) {
        for (const availableProvider of availableProviders) {
          if (availableProvider !== provider && availableProvider !== configService.get('search.fallbackProvider')) {
            try {
              results = await this.executeSearch(availableProvider, query, maxResults);
              usedProvider = availableProvider;
              loggers.search.info(`Emergency fallback to ${availableProvider} successful`);
              break;
            } catch (error) {
              loggers.search.error(`Emergency provider ${availableProvider} failed`, { error: error.message });
              errorTracker.track(error, `Search emergency provider: ${availableProvider}`);
            }
          }
        }
      }

      if (!results) {
        throw new Error('All search providers failed');
      }

      // Process and cache results
      const processedResults = this.processResults(results, query, usedProvider);
      
      if (useCache) {
        this.addToCache(query, processedResults);
      }

      const duration = performance.end(searchId);
      loggers.search.info(`Search completed successfully`, { 
        query, 
        provider: usedProvider, 
        resultCount: processedResults.results.length,
        duration 
      });

      return processedResults;

    } catch (error) {
      performance.end(searchId);
      loggers.search.error('Search failed', { query, error: error.message });
      errorTracker.track(error, `Search query: ${query}`);
      throw error;
    }
  }

  async executeSearch(provider, query, maxResults) {
    const timeout = configService.get('search.timeout');
    const searchFunction = this.providers[provider];
    
    if (!searchFunction) {
      throw new Error(`Unknown search provider: ${provider}`);
    }

    return Promise.race([
      searchFunction(query, maxResults),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Search timeout')), timeout)
      )
    ]);
  }

  async googleSearch(query, maxResults) {
    const apiKey = configService.get('apiKeys.googleSearch');
    const engineId = configService.get('apiKeys.googleSearchEngineId');

    if (!apiKey || !engineId) {
      throw new Error('Google Search API credentials not configured');
    }

    const url = 'https://www.googleapis.com/customsearch/v1';
    const params = {
      key: apiKey,
      cx: engineId,
      q: query,
      num: Math.min(maxResults, 10) // Google API max is 10
    };

    loggers.search.debug('Making Google Search API request', { query, maxResults });

    const response = await axios.get(url, { params });
    
    if (!response.data.items) {
      return { results: [], totalResults: 0, searchTime: 0 };
    }

    return {
      results: response.data.items.map(item => ({
        title: item.title,
        url: item.link,
        snippet: item.snippet,
        displayUrl: item.displayLink
      })),
      totalResults: parseInt(response.data.searchInformation?.totalResults) || 0,
      searchTime: parseFloat(response.data.searchInformation?.searchTime) || 0
    };
  }

  async bingSearch(query, maxResults) {
    const apiKey = configService.get('apiKeys.bingSearch');

    if (!apiKey) {
      throw new Error('Bing Search API key not configured');
    }

    const url = 'https://api.bing.microsoft.com/v7.0/search';
    const headers = {
      'Ocp-Apim-Subscription-Key': apiKey
    };
    const params = {
      q: query,
      count: Math.min(maxResults, 50), // Bing API max is 50
      responseFilter: 'Webpages'
    };

    loggers.search.debug('Making Bing Search API request', { query, maxResults });

    const response = await axios.get(url, { headers, params });
    
    if (!response.data.webPages?.value) {
      return { results: [], totalResults: 0, searchTime: 0 };
    }

    return {
      results: response.data.webPages.value.map(item => ({
        title: item.name,
        url: item.url,
        snippet: item.snippet,
        displayUrl: item.displayUrl
      })),
      totalResults: response.data.webPages.totalEstimatedMatches || 0,
      searchTime: 0 // Bing doesn't provide search time
    };
  }

  processResults(rawResults, query, provider) {
    const processed = {
      query,
      provider,
      timestamp: new Date().toISOString(),
      totalResults: rawResults.totalResults,
      searchTime: rawResults.searchTime,
      results: rawResults.results.map((result, index) => ({
        ...result,
        rank: index + 1,
        relevanceScore: this.calculateRelevanceScore(result, query)
      }))
    };

    // Sort by relevance score
    processed.results.sort((a, b) => b.relevanceScore - a.relevanceScore);

    return processed;
  }

  calculateRelevanceScore(result, query) {
    const queryTerms = query.toLowerCase().split(' ');
    const title = result.title.toLowerCase();
    const snippet = result.snippet.toLowerCase();
    
    let score = 0;
    
    queryTerms.forEach(term => {
      // Title matches are worth more
      if (title.includes(term)) score += 3;
      if (snippet.includes(term)) score += 1;
    });
    
    return score;
  }

  getFromCache(query) {
    const cached = this.cache.get(query.toLowerCase());
    if (cached && Date.now() - cached.timestamp < configService.get('search.cacheTTL')) {
      return cached.data;
    }
    if (cached) {
      this.cache.delete(query.toLowerCase());
    }
    return null;
  }

  addToCache(query, results) {
    this.cache.set(query.toLowerCase(), {
      data: results,
      timestamp: Date.now()
    });
    
    // Limit cache size
    if (this.cache.size > 100) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
  }

  clearCache() {
    this.cache.clear();
    loggers.search.info('Search cache cleared');
  }

  getStats() {
    return {
      cacheSize: this.cache.size,
      availableProviders: configService.getAvailableSearchProviders(),
      defaultProvider: configService.get('search.defaultProvider'),
      fallbackProvider: configService.get('search.fallbackProvider')
    };
  }
}

// Create singleton instance
const searchService = new SearchService();

module.exports = searchService;
