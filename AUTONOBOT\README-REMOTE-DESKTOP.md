# 🖥️ AUTONOBOT Remote Desktop System

## 🎯 Descripción

El sistema de escritorio remoto de AUTONOBOT proporciona una experiencia completa tipo "Do Browser" donde los usuarios pueden observar en tiempo real cómo los agentes de IA navegan por la web en un entorno virtualizado. Esta implementación incluye un escritorio remoto completo con navegador Chrome controlado por Selenium, visualización VNC integrada, y sincronización en tiempo real entre las acciones de los agentes y lo que ven los usuarios.

## 🏗️ Arquitectura del Sistema

### Componentes Principales

1. **🐳 Contenedor Docker con Escritorio Remoto**
   - Ubuntu 22.04 con entorno de escritorio XFCE
   - Servidor VNC (TightVNC) en puerto 5901
   - noVNC para acceso web en puerto 6901
   - Google Chrome con ChromeDriver
   - Servidor Selenium con WebSocket bridge

2. **🌐 Visor VNC Integrado**
   - Componente React que embebe noVNC
   - Controles de escala y calidad
   - Captura de screenshots
   - Reconexión automática

3. **🔗 Bridge de Comunicación**
   - Servidor WebSocket en puerto 8080
   - Comunicación bidireccional con agentes
   - Control remoto del navegador Chrome
   - Sincronización de eventos

4. **🤖 Agentes Adaptados**
   - Observer Agent con análisis visual remoto
   - Navigation Agent con Selenium WebDriver
   - Search Agent integrado con navegador remoto
   - Response Agent con contexto visual

## 🚀 Instalación y Configuración

### Prerrequisitos

- **Docker Desktop** (Windows/Mac) o **Docker Engine** (Linux)
- **Docker Compose** (incluido con Docker Desktop)
- **Node.js 18+** para el frontend
- **8GB RAM** mínimo recomendado
- **Puertos disponibles**: 3000, 5000, 5901, 6901, 8080

### Instalación Rápida

```bash
# 1. Clonar o navegar al directorio AUTONOBOT
cd AUTONOBOT

# 2. Instalar dependencias del frontend
npm install

# 3. Iniciar el sistema completo con Docker
node start-remote-desktop-system.js
```

### Instalación Manual

```bash
# 1. Construir imágenes Docker
docker-compose build

# 2. Iniciar servicios
docker-compose up -d

# 3. Verificar estado
docker-compose ps

# 4. Iniciar frontend
npm run dev
```

## 🎮 Uso del Sistema

### Acceso a la Interfaz

1. **Abrir**: http://localhost:3000
2. **Seleccionar**: "Remote Desktop" en el header
3. **Observar**: El escritorio remoto se carga automáticamente
4. **Interactuar**: Dar instrucciones mientras se observa la ejecución

### Interfaces Disponibles

- **🌐 Frontend Principal**: http://localhost:3000
- **🖥️ Escritorio Remoto (Web)**: http://localhost:6901
- **🔧 API Backend**: http://localhost:5000
- **🔗 WebSocket Bridge**: ws://localhost:8080

### Acceso VNC Directo

- **Cliente VNC**: `localhost:5901`
- **Contraseña**: `autonobot`
- **Resolución**: 1920x1080 (configurable)

## 🎯 Ejemplos de Uso

### Instrucciones Básicas

```
"Navigate to Google and search for React tutorials"
"Go to GitHub and find trending JavaScript repositories"
"Visit Wikipedia and find information about artificial intelligence"
"Search for Node.js best practices and summarize the findings"
```

### Instrucciones Avanzadas

```
"Go to Amazon, search for laptops, and compare the top 3 results"
"Navigate to Stack Overflow, find questions about Python, and analyze the most popular ones"
"Visit a news website, read the headlines, and summarize the main topics"
"Go to YouTube, search for programming tutorials, and list the most viewed ones"
```

## 🔧 Configuración Avanzada

### Variables de Entorno

```env
# Docker Compose
VNC_RESOLUTION=1920x1080
VNC_COL_DEPTH=24
VNC_PASSWORD=autonobot

# Backend
REMOTE_DESKTOP_URL=ws://autonobot-desktop:8080
VNC_URL=http://autonobot-desktop:6901

# Frontend
REACT_APP_BACKEND_URL=http://localhost:5000
REACT_APP_VNC_URL=http://localhost:6901
```

### Personalización del Escritorio

```bash
# Cambiar resolución
export VNC_RESOLUTION=1600x900

# Cambiar calidad de color
export VNC_COL_DEPTH=16

# Reconstruir con nueva configuración
docker-compose down
docker-compose up --build
```

## 🛠️ Desarrollo y Personalización

### Estructura de Archivos

```
AUTONOBOT/
├── docker/
│   ├── Dockerfile              # Imagen del escritorio remoto
│   ├── supervisord.conf        # Configuración de servicios
│   └── start-services.sh       # Script de inicio
├── src/components/
│   ├── RemoteDesktopViewer.jsx # Visor VNC integrado
│   └── RemoteAutonomousAgent.jsx # Interfaz principal
├── backend/services/
│   ├── remoteDesktopService.js # Comunicación con escritorio
│   └── remoteBrowserService.js # Adaptador Selenium
├── docker-compose.yml         # Orquestación de servicios
└── start-remote-desktop-system.js # Launcher automático
```

### Personalizar Agentes

1. **Modificar comportamiento**:
   ```javascript
   // En remoteBrowserService.js
   async navigateTo(url) {
     // Personalizar lógica de navegación
   }
   ```

2. **Agregar nuevos comandos**:
   ```python
   # En autonobot_bridge.py
   elif command == 'custom_action':
       result = self.selenium_server.custom_action(data)
   ```

3. **Personalizar análisis visual**:
   ```javascript
   // En ObserverAgent.js
   async analyzeRemoteScreenshot(screenshotPath) {
     // Integrar con Gemini 2.0 Flash
   }
   ```

## 🔍 Monitoreo y Debugging

### Logs del Sistema

```bash
# Ver todos los logs
docker-compose logs -f

# Logs específicos por servicio
docker-compose logs -f autonobot-desktop
docker-compose logs -f autonobot-backend

# Logs en tiempo real
docker-compose logs -f --tail=100
```

### Debugging del Escritorio Remoto

```bash
# Acceder al contenedor
docker exec -it autonobot-remote-desktop bash

# Verificar procesos VNC
ps aux | grep vnc

# Verificar servicios
supervisorctl status

# Reiniciar servicios
supervisorctl restart all
```

### Debugging del Navegador

```bash
# Acceder al navegador remoto
# Abrir http://localhost:6901 en el navegador

# Verificar ChromeDriver
docker exec -it autonobot-remote-desktop chromedriver --version

# Verificar procesos Chrome
docker exec -it autonobot-remote-desktop ps aux | grep chrome
```

## 🚨 Troubleshooting

### Problemas Comunes

1. **Escritorio remoto no se conecta**:
   ```bash
   # Verificar que el contenedor esté ejecutándose
   docker ps
   
   # Reiniciar servicios
   docker-compose restart autonobot-desktop
   ```

2. **VNC no responde**:
   ```bash
   # Limpiar procesos VNC
   docker exec -it autonobot-remote-desktop pkill -f vnc
   docker-compose restart autonobot-desktop
   ```

3. **Chrome no inicia**:
   ```bash
   # Verificar memoria disponible
   docker stats
   
   # Aumentar memoria compartida
   # En docker-compose.yml: shm_size: 4gb
   ```

4. **WebSocket no conecta**:
   ```bash
   # Verificar puerto 8080
   netstat -an | grep 8080
   
   # Verificar bridge
   docker-compose logs autonobot-desktop | grep bridge
   ```

### Optimización de Rendimiento

1. **Reducir latencia**:
   - Usar resolución más baja (1280x720)
   - Reducir calidad de color (16 bits)
   - Optimizar compresión VNC

2. **Mejorar estabilidad**:
   - Aumentar memoria del contenedor
   - Usar SSD para volúmenes Docker
   - Configurar límites de recursos

## 📊 Métricas y Estadísticas

### Endpoints de Monitoreo

```http
GET /api/autonomous/remote-desktop/status
GET /api/autonomous/remote-desktop/statistics
GET /api/autonomous/health
```

### Métricas Disponibles

- Estado de conexión VNC
- Latencia de WebSocket
- Uso de memoria del contenedor
- Tiempo de respuesta del navegador
- Estadísticas de capturas de pantalla

## 🔐 Seguridad

### Consideraciones de Seguridad

1. **Acceso VNC**:
   - Cambiar contraseña por defecto
   - Usar VPN para acceso remoto
   - Configurar firewall apropiado

2. **Contenedor Docker**:
   - Ejecutar como usuario no-root
   - Limitar recursos del contenedor
   - Usar imágenes base actualizadas

3. **Red**:
   - Configurar red Docker privada
   - Usar HTTPS en producción
   - Implementar autenticación

## 🚀 Despliegue en Producción

### Configuración de Producción

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  autonobot-desktop:
    restart: always
    environment:
      - VNC_PASSWORD=${VNC_PASSWORD}
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2'
```

### Escalabilidad

- Usar Docker Swarm o Kubernetes
- Implementar load balancer
- Configurar almacenamiento persistente
- Monitoreo con Prometheus/Grafana

## 📄 Licencia

Este proyecto está bajo la licencia MIT. Ver `LICENSE` para más detalles.

---

🖥️ **¡El futuro de la navegación web autónoma con visualización en tiempo real está aquí!**
