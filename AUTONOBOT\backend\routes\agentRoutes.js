const express = require('express');
const router = express.Router();
const browserService = require('../services/browserService');
const llmService = require('../services/llmService');

// Agent memory and state
let agentMemory = {
  collectedData: [],
  navigationHistory: [],
  taskObjectives: null,
  currentObjective: null,
  lastSearchTerm: ''
};

let currentAgentState = {
  log: [],
  status: 'idle', // idle, running, finished
  results: '',
  currentUrl: 'about:blank'
};

// Add message to agent log
const appendToAgentLog = (message) => {
  currentAgentState.log.push(message);
  console.log(message); // Also print to server console
};

// Start a new task
router.post('/start-task', async (req, res) => {
  const { taskInput } = req.body;
  
  if (!taskInput) {
    return res.status(400).json({ error: 'Task is required.' });
  }

  // Reset agent state
  agentMemory = {
    collectedData: [],
    navigationHistory: [],
    taskObjectives: null,
    currentObjective: null,
    lastSearchTerm: ''
  };
  
  currentAgentState = {
    log: [],
    status: 'running',
    results: '',
    currentUrl: 'about:blank'
  };
  
  appendToAgentLog(`[System] Starting AUTONOBOT with task: "${taskInput}"`);

  // Start agent loop in background
  agentLoop(taskInput).catch(error => {
    appendToAgentLog(`[ERROR] Agent loop error: ${error.message}`);
    currentAgentState.status = 'finished';
    currentAgentState.results = `Task failed with error: ${error.message}`;
  });

  res.json({ message: 'Task started', status: currentAgentState.status });
});

// Get current agent status
router.get('/status', (req, res) => {
  res.json(currentAgentState);
});

// Main agent loop
const agentLoop = async (taskInput) => {
  let stepCount = 0;
  const maxSteps = 15; // Limit steps to prevent infinite loops

  try {
    // Initial navigation to Google
    appendToAgentLog(`[AUTONOBOT] Starting task: "${taskInput}"`);
    currentAgentState.currentUrl = await browserService.navigateTo('https://www.google.com');
    agentMemory.navigationHistory.push(currentAgentState.currentUrl);
    appendToAgentLog(`[AUTONOBOT] Navigated to: ${currentAgentState.currentUrl}`);

    // Step 1: Initial task breakdown
    appendToAgentLog(`[AUTONOBOT] Breaking down the task`);
    
    const taskBreakdownPrompt = `Break down the following user task into main objectives, key information to extract, and success criteria. Respond in JSON format.
    Task: "${taskInput}"`;
    
    const taskSchema = {
      type: "OBJECT",
      properties: {
        objetivos_principales: { type: "ARRAY", items: { type: "STRING" } },
        informacion_a_extraer: { type: "ARRAY", items: { type: "STRING" } },
        criterios_de_exito: { type: "STRING" }
      },
      required: ["objetivos_principales", "informacion_a_extraer", "criterios_de_exito"]
    };
    
    // Use either real LLM or simulation based on API key availability
    const parsedTask = process.env.GEMINI_API_KEY && process.env.GEMINI_API_KEY !== 'your_gemini_api_key_here'
      ? await llmService.callLLM(taskBreakdownPrompt, taskSchema)
      : await llmService.simulateLLM(taskBreakdownPrompt, taskSchema);

    if (!parsedTask || !parsedTask.objetivos_principales || parsedTask.objetivos_principales.length === 0) {
      appendToAgentLog(`[ERROR] Failed to break down the task. Finishing.`);
      currentAgentState.status = 'finished';
      currentAgentState.results = "Could not understand the task. Please be more specific.";
      return;
    }

    agentMemory.taskObjectives = parsedTask.objetivos_principales;
    agentMemory.currentObjective = parsedTask.objetivos_principales[0];
    
    appendToAgentLog(`[AUTONOBOT] Task broken down. Objectives: ${agentMemory.taskObjectives.join(', ')}`);
    appendToAgentLog(`[AUTONOBOT] Current objective: ${agentMemory.currentObjective}`);

    // Main agent loop
    while (stepCount < maxSteps && currentAgentState.status === 'running') {
      appendToAgentLog(`--- Step ${stepCount + 1} ---`);

      // Step 2: Perception - get page contents
      appendToAgentLog(`[AUTONOBOT] Analyzing current page: ${currentAgentState.currentUrl}`);
      const pageHtml = await browserService.getPageHtml();
      const visibleText = browserService.extractVisibleText(pageHtml).substring(0, 2000); // Limit text length
      const interactiveElements = browserService.extractInteractiveElements(pageHtml);
      
      // Update current URL
      currentAgentState.currentUrl = await browserService.getCurrentUrl();

      const perceptionPrompt = `Given the following webpage (URL: ${currentAgentState.currentUrl}):
      Visible text: ${visibleText}...
      Interactive elements: ${JSON.stringify(interactiveElements)}

      What is the main purpose of this page? Which interactive elements are relevant for the task: "${taskInput}" and current objective: "${agentMemory.currentObjective}"?
      Provide a list of relevant elements with their IDs, description, and relevance. Also indicate if the task appears completed or if there's an error.
      
      Respond in JSON format.`;

      const perceptionSchema = {
        type: "OBJECT",
        properties: {
          proposito_pagina: { type: "STRING" },
          elementos_relevantes: {
            type: "ARRAY",
            items: {
              type: "OBJECT",
              properties: {
                id: { type: "STRING" },
                descripcion: { type: "STRING" },
                relevancia: { type: "STRING" }
              }
            }
          },
          tarea_completada: { type: "BOOLEAN" },
          error_detectado: { type: "BOOLEAN" },
          mensaje_error: { type: "STRING" }
        },
        required: ["proposito_pagina", "elementos_relevantes", "tarea_completada", "error_detectado"]
      };

      // Use either real LLM or simulation based on API key availability
      const perception = process.env.GEMINI_API_KEY && process.env.GEMINI_API_KEY !== 'your_gemini_api_key_here'
        ? await llmService.callLLM(perceptionPrompt, perceptionSchema)
        : await llmService.simulateLLM(perceptionPrompt, perceptionSchema);

      if (!perception) {
        appendToAgentLog(`[ERROR] Failed to analyze the page. Finishing.`);
        currentAgentState.status = 'finished';
        currentAgentState.results = "Error analyzing the page.";
        break;
      }

      appendToAgentLog(`[Perception] Page purpose: ${perception.proposito_pagina}`);
      appendToAgentLog(`[Perception] Relevant elements found: ${perception.elementos_relevantes.length}`);

      // Handle errors or task completion
      if (perception.error_detectado) {
        appendToAgentLog(`[AUTONOBOT] Error detected: ${perception.mensaje_error}`);
        const errorAction = await decideNextAction(taskInput, perception);
        
        if (errorAction && errorAction.accion === 'manejar_error') {
          await executeAction(errorAction.accion, errorAction.parametros, interactiveElements);
          break;
        }
      }

      if (perception.tarea_completada) {
        appendToAgentLog(`[AUTONOBOT] Task appears to be completed`);
        const completionAction = await decideNextAction(taskInput, perception);
        
        if (completionAction && completionAction.accion === 'finalizar_tarea') {
          await executeAction(completionAction.accion, completionAction.parametros, interactiveElements);
          break;
        }
      }

      // Step 3: Decision - determine next action
      const decision = await decideNextAction(taskInput, perception);
      
      if (!decision) {
        appendToAgentLog(`[ERROR] Failed to decide next action. Finishing.`);
        currentAgentState.status = 'finished';
        currentAgentState.results = "Error in decision making.";
        break;
      }

      // Step 4: Execution - perform the action
      appendToAgentLog(`[Decision] Next action: ${decision.accion}`);
      const executionStatus = await executeAction(decision.accion, decision.parametros, interactiveElements);

      if (executionStatus === 'finalizado') {
        break; // Task completed or error handled
      }

      stepCount++;
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate "thinking" time
    }

    if (stepCount >= maxSteps) {
      appendToAgentLog(`[AUTONOBOT] Step limit reached. Finishing task.`);
      currentAgentState.results = "The task could not be completed within the step limit. May need more specific instructions.";
      currentAgentState.status = 'finished';
    }

  } catch (error) {
    appendToAgentLog(`[CRITICAL ERROR] Unexpected error: ${error.message}`);
    currentAgentState.status = 'finished';
    currentAgentState.results = `Task failed with critical error: ${error.message}`;
  } finally {
    currentAgentState.status = 'finished';
  }
};

// Decide next action
async function decideNextAction(taskInput, perception) {
  const prompt = `Given the task: "${taskInput}", current objective: "${agentMemory.currentObjective}", 
  and page perception: ${JSON.stringify(perception)},
  what is the most logical next action?
  
  Navigation history: ${agentMemory.navigationHistory.slice(-3).join(' -> ')}
  
  Return one of these actions in JSON format:
  - 'navegar_a_url': {"accion": "navegar_a_url", "parametros": {"url": "destination_url"}}
  - 'hacer_clic': {"accion": "hacer_clic", "parametros": {"elemento_id": "element_id"}}
  - 'escribir_texto': {"accion": "escribir_texto", "parametros": {"elemento_id": "field_id", "texto": "text_to_write"}}
  - 'extraer_datos': {"accion": "extraer_datos", "parametros": {"elementos_a_extraer": [{"id": "element_id", "descripcion": "what_to_extract"}]}}
  - 'finalizar_tarea': {"accion": "finalizar_tarea", "parametros": {"resultados": "summary_of_results"}}
  - 'manejar_error': {"accion": "manejar_error", "parametros": {"descripcion_error": "error_description"}}
  
  Make sure element_id exists in the relevant elements if the action is 'hacer_clic' or 'escribir_texto'.`;

  const schema = {
    type: "OBJECT",
    properties: {
      accion: { 
        type: "STRING", 
        enum: ["navegar_a_url", "hacer_clic", "escribir_texto", "extraer_datos", "finalizar_tarea", "manejar_error"] 
      },
      parametros: {
        type: "OBJECT",
        properties: {
          url: { type: "STRING" },
          elemento_id: { type: "STRING" },
          texto: { type: "STRING" },
          elementos_a_extraer: {
            type: "ARRAY",
            items: {
              type: "OBJECT",
              properties: {
                id: { type: "STRING" },
                descripcion: { type: "STRING" }
              }
            }
          },
          resultados: { type: "STRING" },
          descripcion_error: { type: "STRING" }
        }
      }
    },
    required: ["accion", "parametros"]
  };

  // Use either real LLM or simulation based on API key availability
  return process.env.GEMINI_API_KEY && process.env.GEMINI_API_KEY !== 'your_gemini_api_key_here'
    ? await llmService.callLLM(prompt, schema)
    : await llmService.simulateLLM(prompt, schema);
}

// Execute action
async function executeAction(action, params, interactiveElements) {
  appendToAgentLog(`[Execution] Executing action: ${action}...`);
  
  // Create a copy of memory to update
  let newMemory = { ...agentMemory };

  switch (action) {
    case 'navegar_a_url':
      const targetUrl = params.url;
      if (targetUrl) {
        currentAgentState.currentUrl = await browserService.navigateTo(targetUrl);
        newMemory.navigationHistory.push(currentAgentState.currentUrl);
        appendToAgentLog(`[Execution] Navigated to: ${currentAgentState.currentUrl}`);
      } else {
        appendToAgentLog(`[ERROR] Invalid URL: ${targetUrl}`);
        return 'error';
      }
      break;
      
    case 'hacer_clic':
      const elementToClick = interactiveElements.find(el => el.id === params.elemento_id);
      if (elementToClick) {
        appendToAgentLog(`[Execution] Clicking on: ${elementToClick.description || elementToClick.id}`);
        const clickSuccess = await browserService.clickElement(params.elemento_id);
        
        if (!clickSuccess) {
          appendToAgentLog(`[ERROR] Failed to click element ${params.elemento_id}`);
          return 'error';
        }
        
        // Wait for any navigation to complete
        await browserService.waitForNavigation();
        
        // Update URL after click (may have navigated)
        currentAgentState.currentUrl = await browserService.getCurrentUrl();
        newMemory.navigationHistory.push(currentAgentState.currentUrl);
      } else {
        appendToAgentLog(`[ERROR] Element with ID ${params.elemento_id} not found for clicking`);
        return 'error';
      }
      break;
      
    case 'escribir_texto':
      const inputElement = interactiveElements.find(el => 
        el.id === params.elemento_id && 
        (el.tagName === 'input' || el.tagName === 'textarea')
      );
      
      if (inputElement) {
        appendToAgentLog(`[Execution] Writing "${params.texto}" in field: ${inputElement.description || inputElement.id}`);
        const typeSuccess = await browserService.typeText(params.elemento_id, params.texto);
        
        if (!typeSuccess) {
          appendToAgentLog(`[ERROR] Failed to write in element ${params.elemento_id}`);
          return 'error';
        }
        
        // Save search term if this was a search box
        if (inputElement.name === 'q' || inputElement.id.includes('search')) {
          newMemory.lastSearchTerm = params.texto;
        }
      } else {
        appendToAgentLog(`[ERROR] Input field with ID ${params.elemento_id} not found`);
        return 'error';
      }
      break;
      
    case 'extraer_datos':
      const extractedData = [];
      const freshHtml = await browserService.getPageHtml();
      
      if (params.elementos_a_extraer && Array.isArray(params.elementos_a_extraer)) {
        for (const item of params.elementos_a_extraer) {
          const element = interactiveElements.find(el => el.id === item.id);
          if (element) {
            extractedData.push({
              id: item.id,
              description: item.descripcion,
              value: element.value || element.text || 'N/A'
            });
          }
        }
      }
      
      newMemory.collectedData.push({ 
        url: currentAgentState.currentUrl, 
        data: extractedData 
      });
      
      appendToAgentLog(`[Execution] Extracted ${extractedData.length} data items`);
      break;
      
    case 'finalizar_tarea':
      currentAgentState.results = params.resultados || "Task completed without specific results.";
      appendToAgentLog(`[AUTONOBOT] Task finished: ${currentAgentState.results}`);
      currentAgentState.status = 'finished';
      return 'finalizado';
      
    case 'manejar_error':
      appendToAgentLog(`[AUTONOBOT] Error detected: ${params.descripcion_error}. Attempting recovery or finishing.`);
      currentAgentState.results = `Task could not be completed due to an error: ${params.descripcion_error}.`;
      currentAgentState.status = 'finished';
      return 'finalizado';
      
    default:
      appendToAgentLog(`[ERROR] Unknown action: ${action}`);
      return 'error';
  }
  
  // Update global agent memory
  agentMemory = newMemory;
  return action;
}

module.exports = router;
