# AUTONOBOT Setup and Configuration Guide

## Overview

AUTONOBOT is an enhanced autonomous web navigation agent with advanced AI capabilities, real-time web search integration, voice recognition, and voice synthesis. This guide will help you set up and configure all features.

## Prerequisites

- Node.js 18+ and npm
- Modern web browser with speech recognition support (Chrome/Edge recommended)
- API keys for the services you want to use

## Installation

1. **Clone and Install Dependencies**
   ```bash
   cd AUTONOBOT
   npm install
   ```

2. **Install Additional Dependencies**
   ```bash
   npm install express-rate-limit helmet winston openai
   ```

## API Key Configuration

### Required API Keys

Edit the `.env` file in the root directory and configure your API keys:

#### AI/LLM Services (at least one required)
```env
# Google Gemini (Recommended)
GEMINI_API_KEY=your_gemini_api_key_here
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# Anthropic Claude
ANTHROPIC_API_KEY=your_anthropic_api_key_here
VITE_ANTHROPIC_API_KEY=your_anthropic_api_key_here

# OpenAI GPT
OPENAI_API_KEY=your_openai_api_key_here
VITE_OPENAI_API_KEY=your_openai_api_key_here
```

#### Web Search Services (at least one recommended)
```env
# Google Custom Search API
GOOGLE_SEARCH_API_KEY=your_google_custom_search_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_google_custom_search_engine_id_here
VITE_GOOGLE_SEARCH_API_KEY=your_google_custom_search_api_key_here
VITE_GOOGLE_SEARCH_ENGINE_ID=your_google_custom_search_engine_id_here

# Bing Search API
BING_SEARCH_API_KEY=your_bing_search_api_key_here
VITE_BING_SEARCH_API_KEY=your_bing_search_api_key_here
```

### How to Obtain API Keys

#### Google Gemini API
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key to your `.env` file

#### Google Custom Search API
1. Visit [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the Custom Search JSON API
3. Create credentials (API key)
4. Set up a Custom Search Engine at [Google CSE](https://cse.google.com/)
5. Get your Search Engine ID

#### Anthropic Claude API
1. Visit [Anthropic Console](https://console.anthropic.com/)
2. Create an account and get API access
3. Generate an API key

#### OpenAI API
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create an account and add billing information
3. Generate an API key

#### Bing Search API
1. Visit [Azure Portal](https://portal.azure.com/)
2. Create a Bing Search v7 resource
3. Get your subscription key

## Configuration Options

### Application Settings
```env
# Environment
NODE_ENV=development
LOG_LEVEL=info

# Search Configuration
MAX_SEARCH_RESULTS=10
SEARCH_TIMEOUT=10000

# Voice Configuration
VOICE_ENABLED=true
```

## Running the Application

### Development Mode
```bash
npm start
```
This runs both frontend and backend concurrently.

### Individual Services
```bash
# Frontend only
npm run dev

# Backend only
npm run backend
```

## Features Overview

### 1. Web Search Integration
- **Real-time web search** using Google Custom Search API and Bing Search API
- **Automatic fallback** between search providers
- **Intelligent caching** to improve performance
- **Search result analysis** with AI integration

### 2. Voice Recognition
- **Wake word detection** ("autonobot")
- **Continuous listening** with timeout handling
- **High accuracy** speech-to-text conversion
- **Error handling** and retry mechanisms

### 3. Voice Synthesis
- **Natural text-to-speech** responses
- **Customizable voice settings** (rate, pitch, volume)
- **Response queuing** for multiple messages
- **Interruption handling**

### 4. AI Integration
- **Multiple AI providers** (Gemini, Claude, OpenAI)
- **Automatic fallback** between providers
- **Search-enhanced responses** for current information
- **Configurable parameters** (temperature, max tokens)

### 5. Web Navigation
- **Autonomous browsing** with Playwright
- **Element interaction** (clicking, typing, scrolling)
- **Screenshot capture** for debugging
- **Navigation history** tracking

## Usage Examples

### Voice Commands
1. Say "autonobot" to wake the agent
2. Give your command when prompted
3. Examples:
   - "Search for the latest news about AI"
   - "Navigate to GitHub and find React repositories"
   - "What's the weather like today?"

### Text Interface
- Type commands directly in the web interface
- Use the microphone button for voice input
- Monitor agent progress in the console

### API Endpoints

#### Search API
```bash
# Perform a search
POST /api/autonobot/search/search
{
  "query": "your search query",
  "maxResults": 10,
  "provider": "google"
}

# Get search providers
GET /api/autonobot/search/providers

# Health check
GET /api/autonobot/search/health
```

#### Agent API
```bash
# Start a task
POST /api/autonobot/start-task
{
  "taskInput": "your task description"
}

# Get agent status
GET /api/autonobot/status
```

## Troubleshooting

### Common Issues

1. **Speech recognition not working**
   - Ensure you're using Chrome or Edge browser
   - Check microphone permissions
   - Verify HTTPS connection (required for speech APIs)

2. **Search not working**
   - Verify API keys are correctly configured
   - Check API quotas and billing
   - Review network connectivity

3. **AI responses failing**
   - Ensure at least one AI API key is configured
   - Check API key validity and quotas
   - Review error logs in browser console

4. **Browser automation issues**
   - Playwright may need additional setup on some systems
   - Check if browser dependencies are installed
   - Review backend logs for browser errors

### Logs and Debugging

- **Frontend logs**: Browser developer console
- **Backend logs**: Terminal output and `logs/` directory
- **API logs**: `logs/api.log`
- **Error logs**: `logs/error.log`

### Performance Optimization

1. **Enable caching** for search results
2. **Adjust timeout values** based on your network
3. **Limit search results** to improve response time
4. **Use appropriate log levels** in production

## Security Considerations

- **API keys**: Never commit API keys to version control
- **Rate limiting**: Enabled by default to prevent abuse
- **CORS**: Configured for development origins
- **Input validation**: All user inputs are validated
- **Error handling**: Sensitive information is not exposed

## Advanced Configuration

### Custom Voice Settings
```javascript
voiceService.setVoiceSettings({
  rate: 1.2,
  pitch: 1.1,
  volume: 0.8,
  voice: 'female'
});
```

### AI Model Configuration
```javascript
aiService.setConfig({
  maxTokens: 2048,
  temperature: 0.5,
  enableSearch: true
});
```

### Search Provider Priority
Edit `backend/services/configService.js` to modify search provider preferences.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review logs for error details
3. Ensure all API keys are valid and have sufficient quotas
4. Verify network connectivity and firewall settings

## License

This project is licensed under the MIT License.
