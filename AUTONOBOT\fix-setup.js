#!/usr/bin/env node

/**
 * AUTONOBOT Setup Fix Script
 * Verifica y corrige problemas comunes de configuración
 */

import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 AUTONOBOT Setup Fix Script');
console.log('============================\n');

// Verificar directorio actual
console.log('📁 Current directory:', process.cwd());
console.log('📁 Script directory:', __dirname);

// Verificar archivos esenciales
const essentialFiles = [
  'package.json',
  'vite.config.ts',
  'index.html',
  'src/App.tsx'
];

console.log('\n📋 Checking essential files:');
let missingFiles = [];

essentialFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} - Found`);
  } else {
    console.log(`❌ ${file} - Missing`);
    missingFiles.push(file);
  }
});

// Verificar package.json específicamente
const packageJsonPath = path.join(__dirname, 'package.json');
if (fs.existsSync(packageJsonPath)) {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    console.log('\n📦 Package.json analysis:');
    console.log(`   Name: ${packageJson.name}`);
    console.log(`   Version: ${packageJson.version}`);
    console.log(`   Type: ${packageJson.type}`);
    
    console.log('\n🔧 Available scripts:');
    Object.keys(packageJson.scripts || {}).forEach(script => {
      console.log(`   ${script}: ${packageJson.scripts[script]}`);
    });
    
    // Verificar script dev específicamente
    if (packageJson.scripts && packageJson.scripts.dev) {
      console.log('\n✅ "dev" script found:', packageJson.scripts.dev);
    } else {
      console.log('\n❌ "dev" script missing!');
      missingFiles.push('dev script');
    }
    
  } catch (error) {
    console.log('\n❌ Error reading package.json:', error.message);
    missingFiles.push('valid package.json');
  }
} else {
  console.log('\n❌ package.json not found in current directory');
}

// Verificar node_modules
const nodeModulesPath = path.join(__dirname, 'node_modules');
if (fs.existsSync(nodeModulesPath)) {
  console.log('\n✅ node_modules directory found');
} else {
  console.log('\n⚠️ node_modules directory missing - need to run npm install');
  missingFiles.push('node_modules');
}

// Verificar dependencias críticas
const criticalDeps = ['vite', 'react', 'react-dom'];
console.log('\n🔍 Checking critical dependencies:');

criticalDeps.forEach(dep => {
  const depPath = path.join(__dirname, 'node_modules', dep);
  if (fs.existsSync(depPath)) {
    console.log(`✅ ${dep} - Installed`);
  } else {
    console.log(`❌ ${dep} - Missing`);
    missingFiles.push(dep);
  }
});

// Mostrar resumen
console.log('\n📊 Summary:');
if (missingFiles.length === 0) {
  console.log('✅ All essential files and dependencies are present');
  console.log('\n🚀 You should be able to run: npm run dev');
} else {
  console.log('❌ Missing files/dependencies:');
  missingFiles.forEach(file => {
    console.log(`   - ${file}`);
  });
  
  console.log('\n🔧 Recommended fixes:');
  
  if (missingFiles.includes('node_modules') || criticalDeps.some(dep => missingFiles.includes(dep))) {
    console.log('   1. Run: npm install');
  }
  
  if (missingFiles.includes('package.json') || missingFiles.includes('valid package.json')) {
    console.log('   2. Verify you are in the correct AUTONOBOT directory');
    console.log('   3. Check if there is a nested AUTONOBOT folder');
  }
  
  if (missingFiles.includes('dev script')) {
    console.log('   4. The package.json may be corrupted - check the scripts section');
  }
}

// Función para ejecutar npm install automáticamente
async function runNpmInstall() {
  console.log('\n🔄 Running npm install...');
  
  return new Promise((resolve, reject) => {
    const npmInstall = spawn('npm', ['install'], {
      cwd: __dirname,
      stdio: 'inherit',
      shell: true
    });
    
    npmInstall.on('close', (code) => {
      if (code === 0) {
        console.log('✅ npm install completed successfully');
        resolve(true);
      } else {
        console.log('❌ npm install failed');
        reject(new Error('npm install failed'));
      }
    });
  });
}

// Función para probar npm run dev
async function testNpmDev() {
  console.log('\n🧪 Testing npm run dev...');
  
  return new Promise((resolve) => {
    const npmDev = spawn('npm', ['run', 'dev', '--', '--help'], {
      cwd: __dirname,
      stdio: 'pipe',
      shell: true
    });
    
    let output = '';
    npmDev.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    npmDev.stderr.on('data', (data) => {
      output += data.toString();
    });
    
    npmDev.on('close', (code) => {
      if (code === 0 || output.includes('vite')) {
        console.log('✅ npm run dev command is working');
        resolve(true);
      } else {
        console.log('❌ npm run dev command failed');
        console.log('Output:', output);
        resolve(false);
      }
    });
    
    // Timeout después de 10 segundos
    setTimeout(() => {
      npmDev.kill();
      resolve(false);
    }, 10000);
  });
}

// Ejecutar fixes automáticos si es necesario
async function autoFix() {
  if (missingFiles.includes('node_modules') || criticalDeps.some(dep => missingFiles.includes(dep))) {
    try {
      await runNpmInstall();
      
      // Verificar de nuevo después de npm install
      console.log('\n🔍 Re-checking after npm install...');
      const stillMissing = criticalDeps.filter(dep => {
        const depPath = path.join(__dirname, 'node_modules', dep);
        return !fs.existsSync(depPath);
      });
      
      if (stillMissing.length === 0) {
        console.log('✅ All dependencies now installed');
        
        // Probar npm run dev
        const devWorks = await testNpmDev();
        if (devWorks) {
          console.log('\n🎉 Setup is now fixed! You can run: npm run dev');
        }
      } else {
        console.log('❌ Some dependencies still missing:', stillMissing);
      }
      
    } catch (error) {
      console.log('❌ Auto-fix failed:', error.message);
    }
  }
}

// Preguntar si ejecutar auto-fix
if (missingFiles.length > 0) {
  console.log('\n❓ Would you like to run automatic fixes? (y/n)');
  
  process.stdin.setRawMode(true);
  process.stdin.resume();
  process.stdin.on('data', async (key) => {
    const input = key.toString().toLowerCase();
    
    if (input === 'y' || input === '\r' || input === '\n') {
      console.log('\n🔧 Running automatic fixes...');
      await autoFix();
      process.exit(0);
    } else if (input === 'n') {
      console.log('\n📝 Manual fix required. Follow the recommended fixes above.');
      process.exit(0);
    } else if (input === '\u0003') { // Ctrl+C
      process.exit(0);
    }
  });
} else {
  console.log('\n🎉 Everything looks good! Try running: npm run dev');
}
