import { EventEmitter } from 'events';
import { loggers } from './loggerService.js';
import remoteDesktopService from './remoteDesktopService.js';
import path from 'path';
import fs from 'fs/promises';

/**
 * Remote Browser Service - Adaptador para usar Selenium en escritorio remoto
 * Reemplaza browserService para trabajar con el navegador remoto
 */
export class RemoteBrowserService extends EventEmitter {
  constructor() {
    super();
    this.isInitialized = false;
    this.currentUrl = null;
    this.pageTitle = null;
    this.screenshotCounter = 0;
    
    // Configurar event listeners del servicio de escritorio remoto
    this.setupRemoteDesktopListeners();
    
    loggers.main.info('Remote Browser Service initialized');
  }

  /**
   * Configurar listeners del servicio de escritorio remoto
   */
  setupRemoteDesktopListeners() {
    remoteDesktopService.on('connected', () => {
      this.isInitialized = true;
      this.emit('browser:ready');
      loggers.main.info('Remote browser is ready');
    });

    remoteDesktopService.on('disconnected', () => {
      this.isInitialized = false;
      this.emit('browser:disconnected');
      loggers.main.warn('Remote browser disconnected');
    });

    remoteDesktopService.on('navigation_result', (result) => {
      if (result.success) {
        this.currentUrl = result.url;
        this.emit('navigation:complete', result);
      } else {
        this.emit('navigation:failed', result);
      }
    });

    remoteDesktopService.on('page_info', (info) => {
      if (info.data) {
        this.currentUrl = info.data.url;
        this.pageTitle = info.data.title;
        this.emit('page:info_updated', info.data);
      }
    });

    remoteDesktopService.on('screenshot_result', (result) => {
      this.emit('screenshot:captured', result);
    });

    remoteDesktopService.on('remote_error', (error) => {
      this.emit('browser:error', error);
      loggers.main.error('Remote browser error', { error });
    });
  }

  /**
   * Inicializar el servicio
   */
  async initialize() {
    try {
      if (this.isInitialized) {
        return true;
      }

      loggers.main.info('Initializing remote browser service');
      
      // Conectar al escritorio remoto
      const connected = await remoteDesktopService.connect();
      
      if (connected) {
        // Esperar un poco para que el navegador esté listo
        await this.delay(3000);
        
        // Obtener información inicial de la página
        await this.getPageInfo();
        
        this.isInitialized = true;
        loggers.main.info('✅ Remote browser service initialized successfully');
        return true;
      } else {
        throw new Error('Failed to connect to remote desktop');
      }

    } catch (error) {
      loggers.main.error('Failed to initialize remote browser service', { error: error.message });
      throw error;
    }
  }

  /**
   * Navegar a una URL
   */
  async navigateTo(url) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      loggers.main.info('Navigating to URL', { url });
      
      await remoteDesktopService.navigateToUrl(url);
      
      // Esperar a que la navegación complete
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Navigation timeout'));
        }, 30000);

        const onComplete = (result) => {
          clearTimeout(timeout);
          this.removeListener('navigation:failed', onFailed);
          resolve(result.url || url);
        };

        const onFailed = (error) => {
          clearTimeout(timeout);
          this.removeListener('navigation:complete', onComplete);
          reject(new Error(`Navigation failed: ${error.message || 'Unknown error'}`));
        };

        this.once('navigation:complete', onComplete);
        this.once('navigation:failed', onFailed);
      });

    } catch (error) {
      loggers.main.error('Navigation failed', { url, error: error.message });
      throw error;
    }
  }

  /**
   * Capturar screenshot
   */
  async takeScreenshot(filename = null) {
    try {
      if (!this.isInitialized) {
        throw new Error('Remote browser not initialized');
      }

      if (!filename) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        filename = `screenshots/remote_screenshot_${timestamp}_${++this.screenshotCounter}.png`;
      }

      loggers.main.info('Taking screenshot', { filename });
      
      await remoteDesktopService.captureScreenshot();
      
      // Esperar a que el screenshot se capture
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Screenshot timeout'));
        }, 15000);

        const onCaptured = (result) => {
          clearTimeout(timeout);
          if (result.filename) {
            resolve(result.filename);
          } else {
            resolve(filename);
          }
        };

        this.once('screenshot:captured', onCaptured);
      });

    } catch (error) {
      loggers.main.error('Screenshot failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Hacer clic en un elemento
   */
  async clickElement(selector) {
    try {
      if (!this.isInitialized) {
        throw new Error('Remote browser not initialized');
      }

      loggers.main.info('Clicking element', { selector });
      
      await remoteDesktopService.clickElement(selector);
      
      // Esperar un poco para que la acción se complete
      await this.delay(1000);
      
      return true;

    } catch (error) {
      loggers.main.error('Click failed', { selector, error: error.message });
      throw error;
    }
  }

  /**
   * Escribir texto en un elemento
   */
  async typeText(selector, text) {
    try {
      if (!this.isInitialized) {
        throw new Error('Remote browser not initialized');
      }

      loggers.main.info('Typing text', { selector, textLength: text.length });
      
      await remoteDesktopService.typeText(selector, text);
      
      // Esperar un poco para que el texto se escriba
      await this.delay(500);
      
      return true;

    } catch (error) {
      loggers.main.error('Type text failed', { selector, error: error.message });
      throw error;
    }
  }

  /**
   * Obtener información de la página actual
   */
  async getPageInfo() {
    try {
      if (!this.isInitialized) {
        throw new Error('Remote browser not initialized');
      }

      await remoteDesktopService.getPageInfo();
      
      // Esperar a que la información se actualice
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Page info timeout'));
        }, 10000);

        const onInfoUpdated = (info) => {
          clearTimeout(timeout);
          resolve(info);
        };

        this.once('page:info_updated', onInfoUpdated);
      });

    } catch (error) {
      loggers.main.error('Get page info failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Obtener URL actual
   */
  async getCurrentUrl() {
    if (this.currentUrl) {
      return this.currentUrl;
    }
    
    try {
      const pageInfo = await this.getPageInfo();
      return pageInfo.url || null;
    } catch (error) {
      loggers.main.warn('Could not get current URL', { error: error.message });
      return null;
    }
  }

  /**
   * Extraer elementos interactivos de la página
   */
  async extractInteractiveElements() {
    try {
      // Ejecutar script para extraer elementos interactivos
      const script = `
        const elements = [];
        const interactiveSelectors = [
          'button', 'a[href]', 'input', 'select', 'textarea',
          '[onclick]', '[role="button"]', '[tabindex]'
        ];
        
        interactiveSelectors.forEach(selector => {
          document.querySelectorAll(selector).forEach((el, index) => {
            const rect = el.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
              elements.push({
                type: el.tagName.toLowerCase(),
                text: el.textContent?.trim().substring(0, 100) || '',
                selector: selector + ':nth-of-type(' + (index + 1) + ')',
                attributes: {
                  id: el.id || '',
                  class: el.className || '',
                  href: el.href || '',
                  type: el.type || ''
                },
                position: {
                  x: rect.left,
                  y: rect.top,
                  width: rect.width,
                  height: rect.height
                }
              });
            }
          });
        });
        
        return elements;
      `;

      await remoteDesktopService.executeScript(script);
      
      // Por ahora retornar elementos mock hasta que implementemos la respuesta del script
      return [
        {
          type: 'button',
          text: 'Search',
          selector: 'button[type="submit"]',
          attributes: { type: 'submit' }
        },
        {
          type: 'input',
          text: '',
          selector: 'input[type="text"]',
          attributes: { type: 'text' }
        }
      ];

    } catch (error) {
      loggers.main.error('Extract interactive elements failed', { error: error.message });
      return [];
    }
  }

  /**
   * Ejecutar JavaScript en la página
   */
  async executeScript(script) {
    try {
      if (!this.isInitialized) {
        throw new Error('Remote browser not initialized');
      }

      await remoteDesktopService.executeScript(script);
      return true;

    } catch (error) {
      loggers.main.error('Execute script failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Verificar si el servicio está listo
   */
  isReady() {
    return this.isInitialized && remoteDesktopService.isReady();
  }

  /**
   * Obtener estado del servicio
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      currentUrl: this.currentUrl,
      pageTitle: this.pageTitle,
      remoteDesktop: remoteDesktopService.getConnectionStatus()
    };
  }

  /**
   * Obtener URL del visor VNC
   */
  getVncUrl() {
    return remoteDesktopService.getVncUrl();
  }

  /**
   * Reiniciar el servicio
   */
  async restart() {
    loggers.main.info('Restarting remote browser service');
    
    this.isInitialized = false;
    this.currentUrl = null;
    this.pageTitle = null;
    
    await remoteDesktopService.restart();
    
    return await this.initialize();
  }

  /**
   * Cerrar el servicio
   */
  async close() {
    loggers.main.info('Closing remote browser service');
    
    this.isInitialized = false;
    remoteDesktopService.disconnect();
    
    this.emit('browser:closed');
  }

  /**
   * Delay helper
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Obtener estadísticas del servicio
   */
  getStatistics() {
    return {
      isInitialized: this.isInitialized,
      currentUrl: this.currentUrl,
      pageTitle: this.pageTitle,
      screenshotCount: this.screenshotCounter,
      remoteDesktop: remoteDesktopService.getStatistics()
    };
  }
}

// Crear instancia singleton
export const remoteBrowserService = new RemoteBrowserService();
export default remoteBrowserService;
