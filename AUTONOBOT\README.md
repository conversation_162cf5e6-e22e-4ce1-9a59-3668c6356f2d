# 🤖 AUTONOBOT - Enhanced Autonomous Web Navigation Agent

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org/)

AUTONOBOT is a sophisticated autonomous web navigation agent enhanced with real-time web search capabilities, advanced AI integration, voice recognition, and voice synthesis. It can understand natural language commands, search the web for current information, and autonomously navigate websites to complete complex tasks.

## 🌟 Key Features

### 🔍 **Real-Time Web Search Integration**
- **Google Custom Search API** and **Bing Search API** integration
- Automatic fallback between search providers
- Intelligent caching for improved performance
- Search result analysis and summarization

### 🎤 **Advanced Voice Recognition**
- Wake word detection ("autonobot")
- Continuous speech recognition with timeout handling
- High-accuracy speech-to-text conversion
- Error handling and retry mechanisms

### 🔊 **Natural Voice Synthesis**
- Text-to-speech responses with customizable settings
- Response queuing for multiple messages
- Voice interruption and control
- Multiple voice options and settings

### 🧠 **Multi-Provider AI Integration**
- **Google Gemini**, **Anthropic Claude**, and **OpenAI GPT** support
- Automatic fallback between AI providers
- Search-enhanced AI responses for current information
- Configurable parameters (temperature, max tokens, etc.)

### 🌐 **Autonomous Web Navigation**
- Playwright-powered browser automation
- Element interaction (clicking, typing, scrolling)
- Screenshot capture for debugging
- Navigation history and state management

### 🛡️ **Production-Ready Architecture**
- Comprehensive logging and monitoring
- Rate limiting and security features
- Error handling and recovery mechanisms
- Modular and extensible design

## 🚀 Quick Start

### 1. Installation
```bash
git clone <repository-url>
cd AUTONOBOT
npm install
```

### 2. Configuration
Copy and edit the environment file:
```bash
cp .env.example .env
```

Configure your API keys in `.env`:
```env
# AI Services (at least one required)
GEMINI_API_KEY=your_gemini_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Search Services (recommended)
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here
BING_SEARCH_API_KEY=your_bing_search_api_key_here
```

### 3. Run the Application
```bash
npm start
```

This starts both the frontend (React) and backend (Node.js) servers.

### 4. Test the Setup
```bash
node test-features.js
```

## 📖 Usage Examples

### Voice Commands
1. Say **"autonobot"** to wake the agent
2. Give your command when prompted:
   - *"Search for the latest news about artificial intelligence"*
   - *"Navigate to GitHub and find React repositories"*
   - *"What's the current weather in New York?"*
   - *"Find information about quantum computing"*

### Text Interface
- Type commands directly in the web interface
- Use the microphone button for voice input
- Monitor agent progress in real-time console

### API Usage
```javascript
// Search for information
const results = await searchService.search("latest AI developments");

// Get AI analysis
const analysis = await aiService.searchAndAnalyze("quantum computing trends");

// Voice interaction
voiceService.speak("Task completed successfully");
```

## 🔧 API Endpoints

### Search API
```bash
POST /api/autonobot/search/search
GET  /api/autonobot/search/providers
GET  /api/autonobot/search/health
DELETE /api/autonobot/search/cache
```

### Agent API
```bash
POST /api/autonobot/start-task
GET  /api/autonobot/status
```

### Health Check
```bash
GET /health
```

## 🛠️ Configuration

### Environment Variables
```env
# Application
NODE_ENV=development
LOG_LEVEL=info
PORT=5000

# Features
VOICE_ENABLED=true
MAX_SEARCH_RESULTS=10
SEARCH_TIMEOUT=10000

# Security
RATE_LIMIT_ENABLED=true
HELMET_ENABLED=true
```

### Voice Settings
```javascript
voiceService.setVoiceSettings({
  rate: 1.2,        // Speech rate
  pitch: 1.1,       // Voice pitch
  volume: 0.8,      // Volume level
  voice: 'female'   // Voice type
});
```

### AI Configuration
```javascript
aiService.setConfig({
  maxTokens: 2048,
  temperature: 0.7,
  enableSearch: true,
  searchThreshold: 0.8
});
```

## 📁 Project Structure

```
AUTONOBOT/
├── backend/
│   ├── services/          # Core services
│   │   ├── loggerService.js
│   │   ├── configService.js
│   │   ├── searchService.js
│   │   ├── llmService.js
│   │   └── browserService.js
│   ├── routes/            # API routes
│   └── server.js          # Express server
├── src/
│   ├── components/        # React components
│   ├── services/          # Frontend services
│   │   ├── searchService.ts
│   │   ├── voiceService.ts
│   │   └── aiService.ts
│   └── App.tsx
├── config/                # Configuration files
├── docs/                  # Documentation
├── logs/                  # Application logs
└── test-features.js       # Feature testing
```

## 🔍 How to Obtain API Keys

### Google Gemini API
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add to your `.env` file

### Google Custom Search
1. Enable [Custom Search JSON API](https://console.cloud.google.com/)
2. Create API credentials
3. Set up [Custom Search Engine](https://cse.google.com/)
4. Get your Search Engine ID

### Anthropic Claude
1. Visit [Anthropic Console](https://console.anthropic.com/)
2. Create account and get API access
3. Generate API key

### OpenAI GPT
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create account and add billing
3. Generate API key

### Bing Search
1. Create [Bing Search v7 resource](https://portal.azure.com/)
2. Get subscription key

## 🧪 Testing

### Run Feature Tests
```bash
node test-features.js
```

### Manual Testing
1. Start the application: `npm start`
2. Open browser to `http://localhost:5173`
3. Test voice commands with "autonobot" wake word
4. Try text commands in the interface
5. Monitor logs in `logs/` directory

## 🐛 Troubleshooting

### Common Issues

**Speech recognition not working:**
- Use Chrome or Edge browser
- Ensure microphone permissions
- Verify HTTPS connection

**Search not working:**
- Check API keys and quotas
- Verify network connectivity
- Review error logs

**AI responses failing:**
- Ensure at least one AI API key is configured
- Check API quotas and billing
- Review browser console for errors

### Debug Information
- Frontend logs: Browser developer console
- Backend logs: Terminal and `logs/` directory
- API logs: `logs/api.log`
- Error logs: `logs/error.log`

## 🔒 Security Features

- **Rate limiting** to prevent API abuse
- **Input validation** for all user inputs
- **API key protection** with environment variables
- **CORS configuration** for secure origins
- **Error sanitization** to prevent information leakage

## 🚀 Performance Optimization

- **Intelligent caching** for search results
- **Connection pooling** for API requests
- **Lazy loading** for components
- **Response compression** in production
- **Memory management** and cleanup

## 📊 Monitoring and Logging

- **Structured logging** with Winston
- **Performance metrics** tracking
- **Error tracking** and aggregation
- **API usage statistics**
- **Health check endpoints**

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Playwright** for browser automation
- **Google AI** for Gemini API
- **Anthropic** for Claude API
- **OpenAI** for GPT API
- **React** and **Node.js** communities

---

**Built with ❤️ for autonomous web navigation and AI-powered assistance**
