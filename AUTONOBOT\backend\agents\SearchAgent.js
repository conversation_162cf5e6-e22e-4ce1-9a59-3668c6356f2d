import EventEmitter from 'events';
import { loggers } from '../services/loggerService.js';
import searchService from '../services/searchService.js';
import browserService from '../services/browserService.js';

/**
 * Search Agent - Especializado en búsquedas web y navegación entre páginas
 * Realiza búsquedas web, navega entre páginas, extrae información relevante
 */
export class SearchAgent extends EventEmitter {
  constructor(stateManager) {
    super();
    this.stateManager = stateManager;
    this.isActive = false;
    this.searchHistory = [];
    this.maxHistorySize = 100;
    
    loggers.main.info('Search Agent initialized');
  }

  /**
   * Realiza una búsqueda basada en la tarea
   */
  async performSearch(task) {
    try {
      loggers.main.info('Performing search for task', { taskId: task.id });
      
      this.isActive = true;
      
      // Extraer términos de búsqueda de la instrucción
      const searchQuery = this.extractSearchQuery(task.instruction);
      
      // Actualizar contexto de la tarea
      await this.stateManager.updateTaskContext(task.id, {
        searchQuery,
        searchPhase: 'starting'
      });
      
      // Realizar búsqueda web
      const searchResults = await this.executeWebSearch(searchQuery, task);
      
      // Analizar y filtrar resultados
      const analyzedResults = await this.analyzeSearchResults(searchResults, task);
      
      // Seleccionar mejores candidatos
      const selectedResults = this.selectBestResults(analyzedResults, task);
      
      // Guardar en historial
      const searchRecord = {
        taskId: task.id,
        query: searchQuery,
        timestamp: new Date().toISOString(),
        resultsCount: searchResults.length,
        selectedCount: selectedResults.length,
        results: selectedResults
      };
      
      this.addToHistory(searchRecord);
      
      // Actualizar estado
      await this.stateManager.updateTaskContext(task.id, {
        searchResults: selectedResults,
        searchPhase: 'completed',
        searchRecord
      });
      
      this.isActive = false;
      
      // Emitir evento
      this.emit('search:complete', {
        taskId: task.id,
        query: searchQuery,
        results: selectedResults
      });
      
      loggers.main.info('Search completed', { 
        taskId: task.id, 
        query: searchQuery,
        resultsFound: selectedResults.length 
      });
      
      return {
        query: searchQuery,
        results: selectedResults,
        totalFound: searchResults.length,
        selected: selectedResults.length
      };
      
    } catch (error) {
      this.isActive = false;
      loggers.main.error('Error performing search', { 
        taskId: task.id, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Extrae términos de búsqueda de la instrucción del usuario
   */
  extractSearchQuery(instruction) {
    // Remover palabras de acción comunes
    const actionWords = [
      'search', 'find', 'look', 'get', 'navigate', 'go', 'visit', 'open',
      'buscar', 'encontrar', 'ir', 'navegar', 'abrir', 'visitar'
    ];
    
    // Remover preposiciones y artículos
    const stopWords = [
      'for', 'to', 'in', 'on', 'at', 'the', 'a', 'an', 'and', 'or', 'but',
      'para', 'en', 'de', 'la', 'el', 'un', 'una', 'y', 'o', 'pero'
    ];
    
    let query = instruction.toLowerCase();
    
    // Remover palabras de acción
    actionWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      query = query.replace(regex, '');
    });
    
    // Remover palabras vacías
    stopWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      query = query.replace(regex, '');
    });
    
    // Limpiar espacios extra
    query = query.replace(/\s+/g, ' ').trim();
    
    // Si la query está vacía, usar la instrucción original
    if (!query) {
      query = instruction;
    }
    
    loggers.main.info('Search query extracted', { 
      original: instruction, 
      extracted: query 
    });
    
    return query;
  }

  /**
   * Ejecuta la búsqueda web usando el servicio de búsqueda
   */
  async executeWebSearch(query, task) {
    try {
      const searchOptions = {
        maxResults: 10,
        provider: 'google', // Usar Google por defecto
        useCache: true
      };
      
      loggers.main.info('Executing web search', { query, options: searchOptions });
      
      const searchResponse = await searchService.search(query, searchOptions);
      
      if (!searchResponse.success) {
        throw new Error(`Search failed: ${searchResponse.error}`);
      }
      
      return searchResponse.results || [];
      
    } catch (error) {
      loggers.main.error('Web search execution failed', { 
        query, 
        error: error.message 
      });
      
      // Intentar con búsqueda alternativa o mock
      return this.getMockSearchResults(query);
    }
  }

  /**
   * Analiza los resultados de búsqueda para determinar relevancia
   */
  async analyzeSearchResults(results, task) {
    try {
      const analyzedResults = [];
      
      for (const result of results) {
        const analysis = {
          ...result,
          relevanceScore: this.calculateRelevanceScore(result, task),
          category: this.categorizeResult(result),
          actionable: this.isActionableResult(result),
          trustScore: this.calculateTrustScore(result)
        };
        
        analyzedResults.push(analysis);
      }
      
      // Ordenar por relevancia
      analyzedResults.sort((a, b) => b.relevanceScore - a.relevanceScore);
      
      loggers.main.info('Search results analyzed', { 
        totalResults: results.length,
        averageRelevance: analyzedResults.reduce((sum, r) => sum + r.relevanceScore, 0) / analyzedResults.length
      });
      
      return analyzedResults;
      
    } catch (error) {
      loggers.main.error('Error analyzing search results', { error: error.message });
      return results.map(r => ({ ...r, relevanceScore: 0.5 }));
    }
  }

  /**
   * Calcula puntuación de relevancia para un resultado
   */
  calculateRelevanceScore(result, task) {
    let score = 0;
    
    const instruction = task.instruction.toLowerCase();
    const title = (result.title || '').toLowerCase();
    const snippet = (result.snippet || '').toLowerCase();
    const url = (result.url || '').toLowerCase();
    
    // Coincidencias en título (peso alto)
    const titleWords = instruction.split(' ');
    titleWords.forEach(word => {
      if (word.length > 2 && title.includes(word)) {
        score += 0.3;
      }
    });
    
    // Coincidencias en snippet (peso medio)
    titleWords.forEach(word => {
      if (word.length > 2 && snippet.includes(word)) {
        score += 0.2;
      }
    });
    
    // Coincidencias en URL (peso bajo)
    titleWords.forEach(word => {
      if (word.length > 2 && url.includes(word)) {
        score += 0.1;
      }
    });
    
    // Bonificaciones por tipo de sitio
    if (url.includes('wikipedia.org')) score += 0.2;
    if (url.includes('github.com')) score += 0.15;
    if (url.includes('.edu')) score += 0.15;
    if (url.includes('.gov')) score += 0.1;
    
    // Penalizaciones
    if (url.includes('ads') || url.includes('sponsored')) score -= 0.3;
    
    return Math.max(0, Math.min(1, score));
  }

  /**
   * Categoriza un resultado de búsqueda
   */
  categorizeResult(result) {
    const url = (result.url || '').toLowerCase();
    const title = (result.title || '').toLowerCase();
    
    if (url.includes('wikipedia.org')) return 'encyclopedia';
    if (url.includes('github.com')) return 'code';
    if (url.includes('stackoverflow.com')) return 'technical';
    if (url.includes('youtube.com')) return 'video';
    if (url.includes('news') || url.includes('noticias')) return 'news';
    if (url.includes('shop') || url.includes('store')) return 'commerce';
    if (url.includes('.edu')) return 'academic';
    if (url.includes('.gov')) return 'government';
    
    return 'general';
  }

  /**
   * Determina si un resultado es accionable (se puede navegar y extraer info)
   */
  isActionableResult(result) {
    const url = (result.url || '').toLowerCase();
    
    // URLs que generalmente no son útiles para navegación automática
    const nonActionable = [
      'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx',
      'zip', 'rar', 'tar', 'gz',
      'mp3', 'mp4', 'avi', 'mov',
      'jpg', 'jpeg', 'png', 'gif'
    ];
    
    return !nonActionable.some(ext => url.includes(`.${ext}`));
  }

  /**
   * Calcula puntuación de confianza para un resultado
   */
  calculateTrustScore(result) {
    const url = (result.url || '').toLowerCase();
    let score = 0.5; // Base score
    
    // Sitios de alta confianza
    if (url.includes('wikipedia.org')) score = 0.9;
    else if (url.includes('.edu')) score = 0.85;
    else if (url.includes('.gov')) score = 0.8;
    else if (url.includes('github.com')) score = 0.75;
    else if (url.includes('stackoverflow.com')) score = 0.7;
    
    // Verificar HTTPS
    if (url.startsWith('https://')) score += 0.1;
    
    return Math.max(0, Math.min(1, score));
  }

  /**
   * Selecciona los mejores resultados para navegación
   */
  selectBestResults(analyzedResults, task) {
    // Filtrar resultados con puntuación mínima
    const minRelevance = 0.3;
    const relevantResults = analyzedResults.filter(r => 
      r.relevanceScore >= minRelevance && r.actionable
    );
    
    // Seleccionar top 5 resultados
    const selectedResults = relevantResults.slice(0, 5);
    
    loggers.main.info('Best results selected', { 
      totalAnalyzed: analyzedResults.length,
      relevant: relevantResults.length,
      selected: selectedResults.length 
    });
    
    return selectedResults;
  }

  /**
   * Navega a un resultado específico de búsqueda
   */
  async navigateToResult(result, task) {
    try {
      loggers.main.info('Navigating to search result', { 
        url: result.url, 
        title: result.title 
      });
      
      // Actualizar estado global
      await this.stateManager.updateGlobalState({
        currentUrl: result.url,
        lastAction: 'navigate_to_search_result',
        browserState: 'navigating'
      });
      
      // Navegar usando browser service
      const finalUrl = await browserService.navigateTo(result.url);
      
      // Actualizar estado después de navegación
      await this.stateManager.updateGlobalState({
        currentUrl: finalUrl,
        browserState: 'ready'
      });
      
      // Actualizar contexto de tarea
      await this.stateManager.updateTaskContext(task.id, {
        currentResult: result,
        navigationUrl: finalUrl,
        navigationTimestamp: new Date().toISOString()
      });
      
      loggers.main.info('Navigation completed', { 
        targetUrl: result.url,
        finalUrl 
      });
      
      return {
        success: true,
        targetUrl: result.url,
        finalUrl,
        result
      };
      
    } catch (error) {
      loggers.main.error('Error navigating to result', { 
        url: result.url, 
        error: error.message 
      });
      
      await this.stateManager.updateGlobalState({
        browserState: 'error',
        lastError: error.message
      });
      
      throw error;
    }
  }

  /**
   * Proporciona resultados mock para testing
   */
  getMockSearchResults(query) {
    return [
      {
        title: `Mock result for: ${query}`,
        url: 'https://example.com/mock-result',
        snippet: `This is a mock search result for the query "${query}". This would normally contain relevant information.`,
        relevanceScore: 0.8
      },
      {
        title: `Alternative result for: ${query}`,
        url: 'https://example.org/alternative',
        snippet: `Alternative mock result providing different perspective on "${query}".`,
        relevanceScore: 0.6
      }
    ];
  }

  /**
   * Agrega registro al historial
   */
  addToHistory(record) {
    this.searchHistory.push(record);
    
    // Mantener tamaño del historial
    if (this.searchHistory.length > this.maxHistorySize) {
      this.searchHistory = this.searchHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Obtiene historial de búsquedas
   */
  getSearchHistory(limit = 20) {
    return this.searchHistory.slice(-limit);
  }

  /**
   * Busca en el historial
   */
  searchHistory(query) {
    return this.searchHistory.filter(record => 
      record.query.toLowerCase().includes(query.toLowerCase()) ||
      record.results.some(result => 
        result.title.toLowerCase().includes(query.toLowerCase())
      )
    );
  }

  /**
   * Obtiene estadísticas del agente
   */
  getStatistics() {
    const history = this.searchHistory;
    
    return {
      totalSearches: history.length,
      averageResults: history.length > 0 
        ? history.reduce((sum, r) => sum + r.resultsCount, 0) / history.length 
        : 0,
      averageSelected: history.length > 0
        ? history.reduce((sum, r) => sum + r.selectedCount, 0) / history.length
        : 0,
      recentSearches: history.slice(-5).map(r => ({
        query: r.query,
        timestamp: r.timestamp,
        resultsCount: r.resultsCount
      }))
    };
  }

  /**
   * Obtiene el estado del agente
   */
  getStatus() {
    return {
      isActive: this.isActive,
      searchCount: this.searchHistory.length,
      lastSearch: this.searchHistory[this.searchHistory.length - 1]?.timestamp || null
    };
  }

  /**
   * Detiene el agente
   */
  async stop() {
    this.isActive = false;
    loggers.main.info('Search Agent stopped');
  }
}
