import EventEmitter from 'events';
import { loggers } from '../services/loggerService.js';

/**
 * State Manager - Gestiona el estado global del sistema de agentes
 * Mantiene contexto, memoria y estado compartido entre agentes
 */
export class StateManager extends EventEmitter {
  constructor() {
    super();
    
    // Almacenes de estado
    this.globalState = {
      currentUrl: null,
      currentPage: null,
      browserState: 'idle',
      lastAction: null,
      sessionId: this.generateSessionId()
    };
    
    this.taskContexts = new Map(); // Contexto por tarea
    this.agentStates = new Map();  // Estado por agente
    this.observations = new Map(); // Observaciones por tarea
    this.memory = new Map();       // Memoria a largo plazo
    this.cache = new Map();        // Cache temporal
    
    // Configuración
    this.maxMemorySize = 1000;
    this.maxCacheSize = 500;
    this.cacheTimeout = 300000; // 5 minutos
    
    loggers.main.info('State Manager initialized', { sessionId: this.globalState.sessionId });
  }

  /**
   * Actualiza el estado global del sistema
   */
  updateGlobalState(updates) {
    try {
      const previousState = { ...this.globalState };
      this.globalState = { ...this.globalState, ...updates };
      
      loggers.main.info('Global state updated', { 
        updates, 
        previousUrl: previousState.currentUrl,
        newUrl: this.globalState.currentUrl 
      });
      
      this.emit('state:global_updated', {
        previous: previousState,
        current: this.globalState,
        changes: updates
      });
      
      return this.globalState;
      
    } catch (error) {
      loggers.main.error('Error updating global state', { error: error.message });
      throw error;
    }
  }

  /**
   * Actualiza el contexto de una tarea específica
   */
  async updateTaskContext(taskId, contextUpdates) {
    try {
      const existingContext = this.taskContexts.get(taskId) || {};
      const updatedContext = {
        ...existingContext,
        ...contextUpdates,
        lastUpdated: new Date().toISOString()
      };
      
      this.taskContexts.set(taskId, updatedContext);
      
      loggers.main.info('Task context updated', { taskId, updates: contextUpdates });
      
      this.emit('state:task_context_updated', {
        taskId,
        context: updatedContext,
        updates: contextUpdates
      });
      
      return updatedContext;
      
    } catch (error) {
      loggers.main.error('Error updating task context', { taskId, error: error.message });
      throw error;
    }
  }

  /**
   * Actualiza el progreso de una tarea
   */
  async updateTaskProgress(taskId, progressData) {
    try {
      const context = this.taskContexts.get(taskId) || {};
      
      const progressUpdate = {
        progress: progressData.progress || context.progress || 0,
        currentStep: progressData.currentStep,
        stepResults: [...(context.stepResults || []), ...(progressData.stepResults || [])],
        lastStepTimestamp: new Date().toISOString()
      };
      
      await this.updateTaskContext(taskId, progressUpdate);
      
      this.emit('state:task_progress_updated', {
        taskId,
        progress: progressUpdate.progress,
        currentStep: progressUpdate.currentStep
      });
      
      return progressUpdate;
      
    } catch (error) {
      loggers.main.error('Error updating task progress', { taskId, error: error.message });
      throw error;
    }
  }

  /**
   * Actualiza el estado de un agente
   */
  updateAgentState(agentName, state) {
    try {
      const previousState = this.agentStates.get(agentName) || {};
      const updatedState = {
        ...previousState,
        ...state,
        lastUpdated: new Date().toISOString()
      };
      
      this.agentStates.set(agentName, updatedState);
      
      loggers.main.info('Agent state updated', { agentName, state });
      
      this.emit('state:agent_updated', {
        agentName,
        previous: previousState,
        current: updatedState
      });
      
      return updatedState;
      
    } catch (error) {
      loggers.main.error('Error updating agent state', { agentName, error: error.message });
      throw error;
    }
  }

  /**
   * Guarda una observación de página
   */
  async saveObservation(taskId, observation) {
    try {
      const taskObservations = this.observations.get(taskId) || [];
      taskObservations.push({
        ...observation,
        id: this.generateObservationId(),
        savedAt: new Date().toISOString()
      });
      
      this.observations.set(taskId, taskObservations);
      
      loggers.main.info('Observation saved', { 
        taskId, 
        observationCount: taskObservations.length,
        url: observation.url 
      });
      
      this.emit('state:observation_saved', {
        taskId,
        observation,
        totalObservations: taskObservations.length
      });
      
      return observation;
      
    } catch (error) {
      loggers.main.error('Error saving observation', { taskId, error: error.message });
      throw error;
    }
  }

  /**
   * Almacena información en memoria a largo plazo
   */
  storeMemory(key, data, metadata = {}) {
    try {
      const memoryEntry = {
        key,
        data,
        metadata: {
          ...metadata,
          createdAt: new Date().toISOString(),
          accessCount: 0
        }
      };
      
      this.memory.set(key, memoryEntry);
      
      // Mantener tamaño de memoria
      if (this.memory.size > this.maxMemorySize) {
        this.cleanupMemory();
      }
      
      loggers.main.info('Memory stored', { key, dataType: typeof data });
      
      return memoryEntry;
      
    } catch (error) {
      loggers.main.error('Error storing memory', { key, error: error.message });
      throw error;
    }
  }

  /**
   * Recupera información de la memoria
   */
  retrieveMemory(key) {
    try {
      const memoryEntry = this.memory.get(key);
      
      if (memoryEntry) {
        memoryEntry.metadata.accessCount++;
        memoryEntry.metadata.lastAccessed = new Date().toISOString();
        loggers.main.info('Memory retrieved', { key, accessCount: memoryEntry.metadata.accessCount });
        return memoryEntry.data;
      }
      
      return null;
      
    } catch (error) {
      loggers.main.error('Error retrieving memory', { key, error: error.message });
      return null;
    }
  }

  /**
   * Almacena datos en cache temporal
   */
  setCache(key, data, ttl = this.cacheTimeout) {
    try {
      const cacheEntry = {
        data,
        expiresAt: Date.now() + ttl,
        createdAt: new Date().toISOString()
      };
      
      this.cache.set(key, cacheEntry);
      
      // Mantener tamaño de cache
      if (this.cache.size > this.maxCacheSize) {
        this.cleanupCache();
      }
      
      loggers.main.info('Cache set', { key, ttl });
      
      return cacheEntry;
      
    } catch (error) {
      loggers.main.error('Error setting cache', { key, error: error.message });
      throw error;
    }
  }

  /**
   * Recupera datos del cache
   */
  getCache(key) {
    try {
      const cacheEntry = this.cache.get(key);
      
      if (!cacheEntry) {
        return null;
      }
      
      // Verificar expiración
      if (Date.now() > cacheEntry.expiresAt) {
        this.cache.delete(key);
        loggers.main.info('Cache expired and removed', { key });
        return null;
      }
      
      loggers.main.info('Cache hit', { key });
      return cacheEntry.data;
      
    } catch (error) {
      loggers.main.error('Error getting cache', { key, error: error.message });
      return null;
    }
  }

  /**
   * Obtiene el contexto completo de una tarea
   */
  getTaskContext(taskId) {
    return {
      context: this.taskContexts.get(taskId) || {},
      observations: this.observations.get(taskId) || [],
      globalState: this.globalState
    };
  }

  /**
   * Obtiene el estado de un agente
   */
  getAgentState(agentName) {
    return this.agentStates.get(agentName) || {};
  }

  /**
   * Obtiene todas las observaciones de una tarea
   */
  getTaskObservations(taskId) {
    return this.observations.get(taskId) || [];
  }

  /**
   * Busca en la memoria por patrón
   */
  searchMemory(pattern) {
    const results = [];
    
    for (const [key, entry] of this.memory.entries()) {
      if (key.includes(pattern) || 
          JSON.stringify(entry.data).toLowerCase().includes(pattern.toLowerCase())) {
        results.push({ key, ...entry });
      }
    }
    
    return results;
  }

  /**
   * Limpia memoria antigua
   */
  cleanupMemory() {
    const entries = Array.from(this.memory.entries());
    
    // Ordenar por último acceso y mantener los más recientes
    entries.sort((a, b) => {
      const aAccess = a[1].metadata.lastAccessed || a[1].metadata.createdAt;
      const bAccess = b[1].metadata.lastAccessed || b[1].metadata.createdAt;
      return new Date(bAccess) - new Date(aAccess);
    });
    
    // Mantener solo los más recientes
    const toKeep = entries.slice(0, Math.floor(this.maxMemorySize * 0.8));
    
    this.memory.clear();
    toKeep.forEach(([key, entry]) => {
      this.memory.set(key, entry);
    });
    
    loggers.main.info('Memory cleanup completed', { 
      removed: entries.length - toKeep.length,
      remaining: toKeep.length 
    });
  }

  /**
   * Limpia cache expirado
   */
  cleanupCache() {
    const now = Date.now();
    let removed = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
        removed++;
      }
    }
    
    // Si aún hay demasiados, remover los más antiguos
    if (this.cache.size > this.maxCacheSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => new Date(a[1].createdAt) - new Date(b[1].createdAt));
      
      const toRemove = entries.slice(0, this.cache.size - Math.floor(this.maxCacheSize * 0.8));
      toRemove.forEach(([key]) => {
        this.cache.delete(key);
        removed++;
      });
    }
    
    loggers.main.info('Cache cleanup completed', { removed, remaining: this.cache.size });
  }

  /**
   * Genera ID de sesión único
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Genera ID de observación único
   */
  generateObservationId() {
    return `obs_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Obtiene estadísticas del estado
   */
  getStatistics() {
    return {
      globalState: this.globalState,
      taskContexts: this.taskContexts.size,
      agentStates: this.agentStates.size,
      observations: Array.from(this.observations.values()).reduce((sum, obs) => sum + obs.length, 0),
      memoryEntries: this.memory.size,
      cacheEntries: this.cache.size,
      sessionId: this.globalState.sessionId
    };
  }

  /**
   * Limpia todos los datos de estado
   */
  clearAll() {
    this.taskContexts.clear();
    this.agentStates.clear();
    this.observations.clear();
    this.memory.clear();
    this.cache.clear();
    
    this.globalState = {
      currentUrl: null,
      currentPage: null,
      browserState: 'idle',
      lastAction: null,
      sessionId: this.generateSessionId()
    };
    
    loggers.main.info('All state cleared', { newSessionId: this.globalState.sessionId });
  }
}
