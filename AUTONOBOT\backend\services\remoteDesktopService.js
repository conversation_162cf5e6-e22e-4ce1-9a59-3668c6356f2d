import WebSocket from 'ws';
import { loggers } from './loggerService.js';
import { EventEmitter } from 'events';

/**
 * Remote Desktop Service - Comunicación con el escritorio remoto
 * Gestiona la conexión WebSocket con el bridge de Selenium en el contenedor
 */
export class RemoteDesktopService extends EventEmitter {
  constructor() {
    super();
    this.wsConnection = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000;
    this.remoteDesktopUrl = process.env.REMOTE_DESKTOP_URL || 'ws://localhost:8080';
    this.vncUrl = process.env.VNC_URL || 'http://localhost:6901';
    
    loggers.main.info('Remote Desktop Service initialized', { 
      remoteDesktopUrl: this.remoteDesktopUrl,
      vncUrl: this.vncUrl 
    });
  }

  /**
   * Conectar al escritorio remoto
   */
  async connect() {
    try {
      if (this.isConnected) {
        loggers.main.warn('Already connected to remote desktop');
        return true;
      }

      loggers.main.info('Connecting to remote desktop', { url: this.remoteDesktopUrl });

      this.wsConnection = new WebSocket(this.remoteDesktopUrl);

      this.wsConnection.on('open', () => {
        this.isConnected = true;
        this.reconnectAttempts = 0;
        loggers.main.info('✅ Connected to remote desktop');
        this.emit('connected');
      });

      this.wsConnection.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          loggers.main.error('Error parsing remote desktop message', { error: error.message });
        }
      });

      this.wsConnection.on('close', () => {
        this.isConnected = false;
        loggers.main.warn('🔌 Remote desktop connection closed');
        this.emit('disconnected');
        this.attemptReconnect();
      });

      this.wsConnection.on('error', (error) => {
        loggers.main.error('Remote desktop connection error', { error: error.message });
        this.emit('error', error);
      });

      return true;

    } catch (error) {
      loggers.main.error('Failed to connect to remote desktop', { error: error.message });
      this.emit('error', error);
      return false;
    }
  }

  /**
   * Desconectar del escritorio remoto
   */
  disconnect() {
    if (this.wsConnection) {
      this.wsConnection.close();
      this.wsConnection = null;
    }
    this.isConnected = false;
    loggers.main.info('Disconnected from remote desktop');
  }

  /**
   * Intentar reconexión automática
   */
  attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      loggers.main.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    loggers.main.info(`Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

    setTimeout(() => {
      this.connect();
    }, this.reconnectDelay);
  }

  /**
   * Manejar mensajes del escritorio remoto
   */
  handleMessage(message) {
    loggers.main.info('Remote desktop message received', { type: message.type });

    switch (message.type) {
      case 'navigation_result':
        this.emit('navigation_result', message);
        break;
      case 'screenshot_result':
        this.emit('screenshot_result', message);
        break;
      case 'page_info':
        this.emit('page_info', message);
        break;
      case 'error':
        this.emit('remote_error', message);
        break;
      default:
        loggers.main.warn('Unknown message type from remote desktop', { type: message.type });
    }
  }

  /**
   * Enviar comando al escritorio remoto
   */
  async sendCommand(command, data = {}) {
    if (!this.isConnected || !this.wsConnection) {
      throw new Error('Not connected to remote desktop');
    }

    const message = {
      command,
      ...data,
      timestamp: new Date().toISOString()
    };

    try {
      this.wsConnection.send(JSON.stringify(message));
      loggers.main.info('Command sent to remote desktop', { command, data });
      return true;
    } catch (error) {
      loggers.main.error('Failed to send command to remote desktop', { 
        command, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Navegar a una URL en el navegador remoto
   */
  async navigateToUrl(url) {
    try {
      await this.sendCommand('navigate', { url });
      loggers.main.info('Navigation command sent', { url });
      return true;
    } catch (error) {
      loggers.main.error('Failed to navigate', { url, error: error.message });
      throw error;
    }
  }

  /**
   * Capturar screenshot del navegador remoto
   */
  async captureScreenshot() {
    try {
      await this.sendCommand('screenshot');
      loggers.main.info('Screenshot command sent');
      return true;
    } catch (error) {
      loggers.main.error('Failed to capture screenshot', { error: error.message });
      throw error;
    }
  }

  /**
   * Obtener información de la página actual
   */
  async getPageInfo() {
    try {
      await this.sendCommand('page_info');
      loggers.main.info('Page info command sent');
      return true;
    } catch (error) {
      loggers.main.error('Failed to get page info', { error: error.message });
      throw error;
    }
  }

  /**
   * Hacer clic en un elemento
   */
  async clickElement(selector) {
    try {
      await this.sendCommand('click', { selector });
      loggers.main.info('Click command sent', { selector });
      return true;
    } catch (error) {
      loggers.main.error('Failed to click element', { selector, error: error.message });
      throw error;
    }
  }

  /**
   * Escribir texto en un elemento
   */
  async typeText(selector, text) {
    try {
      await this.sendCommand('type', { selector, text });
      loggers.main.info('Type command sent', { selector, text: text.substring(0, 50) + '...' });
      return true;
    } catch (error) {
      loggers.main.error('Failed to type text', { selector, error: error.message });
      throw error;
    }
  }

  /**
   * Ejecutar JavaScript en el navegador remoto
   */
  async executeScript(script) {
    try {
      await this.sendCommand('execute_script', { script });
      loggers.main.info('Script execution command sent');
      return true;
    } catch (error) {
      loggers.main.error('Failed to execute script', { error: error.message });
      throw error;
    }
  }

  /**
   * Obtener estado de la conexión
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      remoteDesktopUrl: this.remoteDesktopUrl,
      vncUrl: this.vncUrl
    };
  }

  /**
   * Obtener URL del visor VNC
   */
  getVncUrl() {
    return this.vncUrl;
  }

  /**
   * Verificar si el servicio está listo
   */
  isReady() {
    return this.isConnected;
  }

  /**
   * Reiniciar la conexión
   */
  async restart() {
    loggers.main.info('Restarting remote desktop connection');
    this.disconnect();
    await new Promise(resolve => setTimeout(resolve, 2000));
    return await this.connect();
  }

  /**
   * Obtener estadísticas del servicio
   */
  getStatistics() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
      remoteDesktopUrl: this.remoteDesktopUrl,
      vncUrl: this.vncUrl
    };
  }
}

// Crear instancia singleton
export const remoteDesktopService = new RemoteDesktopService();
export default remoteDesktopService;
