{"app": {"name": "AUTONOBOT", "version": "1.0.0", "description": "Autonomous Web Navigation Agent with AI Integration"}, "server": {"port": 5000, "host": "localhost", "environment": "development"}, "ai": {"providers": {"gemini": {"enabled": true, "model": "gemini-2.0-flash", "maxTokens": 1024, "temperature": 0.7, "timeout": 30000}, "anthropic": {"enabled": true, "model": "claude-3-opus-20240229", "maxTokens": 1024, "temperature": 0.7, "timeout": 30000}, "openai": {"enabled": true, "model": "gpt-4", "maxTokens": 1024, "temperature": 0.7, "timeout": 30000}}, "defaultProvider": "gemini", "fallbackProvider": "anthropic", "retryAttempts": 3, "retryDelay": 1000}, "search": {"providers": {"google": {"enabled": true, "maxResults": 10, "timeout": 10000}, "bing": {"enabled": true, "maxResults": 50, "timeout": 10000}}, "defaultProvider": "google", "fallbackProvider": "bing", "maxResults": 10, "timeout": 10000, "cacheEnabled": true, "cacheTTL": 300000, "maxCacheSize": 100}, "voice": {"enabled": true, "wakeWord": "autonobot", "maxListeningTime": 30000, "responseDelay": 500, "settings": {"rate": 1, "pitch": 1, "volume": 1, "voice": "default"}, "recognition": {"continuous": false, "autoRestart": true, "language": "en-US"}}, "browser": {"headless": true, "timeout": 30000, "navigationTimeout": 30000, "viewport": {"width": 1920, "height": 1080}, "userAgent": "AUTONOBOT/1.0 (Autonomous Web Agent)", "maxPages": 5, "enableScreenshots": true}, "agent": {"maxSteps": 20, "stepTimeout": 60000, "memoryLimit": 1000, "enableLogging": true, "autoSave": true, "enableScreenshots": true}, "security": {"enableHelmet": true, "enableRateLimit": true, "rateLimit": {"windowMs": 900000, "max": 100, "message": "Too many requests from this IP"}, "cors": {"origins": ["http://localhost:3000", "http://localhost:5173"], "credentials": true}, "apiKeyValidation": true}, "logging": {"level": "info", "enableConsole": true, "enableFile": true, "maxFileSize": "5MB", "maxFiles": 5, "logDirectory": "./logs"}, "performance": {"enableMetrics": true, "metricsInterval": 60000, "memoryThreshold": 536870912, "cpuThreshold": 80, "enableProfiling": false}, "features": {"webSearch": {"enabled": true, "autoSearch": true, "searchThreshold": 0.7}, "voiceCommands": {"enabled": true, "wakeWordDetection": true, "continuousListening": false}, "aiIntegration": {"enabled": true, "multiProvider": true, "autoFallback": true}, "webNavigation": {"enabled": true, "autonomousMode": true, "interactionLogging": true}}, "limits": {"maxSearchResults": 50, "maxPromptLength": 10000, "maxResponseLength": 5000, "maxConcurrentRequests": 10, "maxMemoryUsage": "1GB", "maxExecutionTime": 300000}, "development": {"enableDebugLogs": true, "enableMockResponses": false, "enableTestMode": false, "hotReload": true}, "production": {"enableDebugLogs": false, "enableMockResponses": false, "enableTestMode": false, "hotReload": false, "enableCompression": true, "enableCaching": true}}